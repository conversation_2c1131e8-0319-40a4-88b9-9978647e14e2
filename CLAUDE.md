# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
npm run dev          # Start Next.js development server
npm run build        # Build for production
npm start           # Start production server
npm run lint        # Run ESLint
npm run vercel-build # Build for Vercel deployment
npm run telegram:session # Generate Telegram session string
```

## Architecture Overview

This is a Next.js application that automates Telegram group creation for IBC projects using the Telegram MTProto API.

### Core Architecture

- **Authentication**: Clerk-based auth with middleware protection on all routes
- **API Layer**: Next.js API routes handle Telegram operations (`/api/create-group`, `/api/team-data`, `/api/health`)
- **Telegram Integration**: Uses `telegram` library with session-based authentication via MTProto
- **State Management**: React state with form validation and error handling via Sonner toasts

### Key Components

- `TelegramGroupForm.tsx`: Main form component with sales rep selection and project configuration
- `telegram.ts`: Telegram client initialization and session management
- `create-group/route.ts`: Core API endpoint that handles group creation, member addition, and automated commands

### Data Flow

1. User fills form with project details and selects sales representative
2. Form submits to `/api/create-group` with structured data
3. API initializes Telegram client and creates supergroup
4. Automated member addition (admin bot, project lead, sales rep, PA, mandatory members)
5. Automated commands execution (`/optin`, `/setadmin`, `/invite_new_group`)
6. Welcome message with project details and Calendly links

### Sales Rep System

Sales representatives are defined with:
- Emoji identifiers for group naming
- Telegram usernames and PA usernames
- Calendly links for different meeting types (intro, followUp, partnerships, otc, outreach)
- Tier/category information

### Environment Variables

Required for Telegram API integration:
- `TELEGRAM_API_ID` and `TELEGRAM_API_HASH`: Telegram app credentials
- `TELEGRAM_SESSION_STRING`: Pre-authenticated session for API calls
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` and `CLERK_SECRET_KEY`: Clerk authentication

### Security Notes

- All routes protected by Clerk middleware
- Telegram session string must be pre-generated using scripts
- No user credentials stored, relies on session-based auth
- Form validation prevents injection attacks

### Deployment

Configured for Vercel with `vercel.json` build settings. Uses Next.js 14 with App Router.