import { TelegramClient } from "telegram";
import { StringSession } from "telegram/sessions";
import { getTelegramConfig } from "./env";

let client: TelegramClient;

/**
 * Initialize and return a Telegram client instance
 * Uses validated environment variables and implements proper error handling
 */
export async function initTelegramClient(): Promise<TelegramClient> {
  if (!client) {
    try {
      const { apiId, apiHash, sessionString } = getTelegramConfig();
      const stringSession = new StringSession(sessionString);

      console.log("🔗 Initializing Telegram client...");

      client = new TelegramClient(stringSession, apiId, apiHash, {
        connectionRetries: 5,
        timeout: 30000, // 30 second timeout
      });

      await client.connect();

      if (!client.connected) {
        throw new Error("Failed to connect to Telegram - client not connected");
      }

      // Verify authentication by getting user info
      const me = await client.getMe();
      console.log(`✅ Connected to Telegram as: ${me.firstName} ${me.lastName || ''} (@${me.username || 'no username'})`);

    } catch (error: any) {
      console.error("❌ Failed to initialize Telegram client:", error.message);

      // Clean up failed client
      if (client) {
        try {
          await client.disconnect();
        } catch (disconnectError) {
          console.error("Error disconnecting failed client:", disconnectError);
        }
        client = undefined as any;
      }

      throw new Error(`Telegram client initialization failed: ${error.message}`);
    }
  }

  return client;
}

/**
 * Disconnect the Telegram client and clean up resources
 */
export async function disconnectTelegramClient(): Promise<void> {
  if (client && client.connected) {
    try {
      await client.disconnect();
      console.log("🔌 Telegram client disconnected");
    } catch (error: any) {
      console.error("Error disconnecting Telegram client:", error.message);
    } finally {
      client = undefined as any;
    }
  }
}

/**
 * Check if the Telegram client is connected
 */
export function isTelegramClientConnected(): boolean {
  return client && client.connected;
}