import { authMiddleware } from '@clerk/nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import {
  groupCreationRateLimit,
  teamDataRateLimit,
  healthRateLimit,
  generalRateLimit
} from '@/lib/rate-limit';

export default authMiddleware({
  beforeAuth: async (req: NextRequest) => {
    // Apply rate limiting based on the path
    const { pathname } = req.nextUrl;

    try {
      // Rate limit specific API endpoints
      if (pathname.startsWith('/api/create-group')) {
        const rateLimitResponse = await groupCreationRateLimit(req);
        if (rateLimitResponse) return rateLimitResponse;
      } else if (pathname.startsWith('/api/team-data')) {
        const rateLimitResponse = await teamDataRateLimit(req);
        if (rateLimitResponse) return rateLimitResponse;
      } else if (pathname.startsWith('/api/health')) {
        const rateLimitResponse = await healthRateLimit(req);
        if (rateLimitResponse) return rateLimitResponse;
      } else if (pathname.startsWith('/api/')) {
        // General rate limit for other API endpoints
        const rateLimitResponse = await generalRateLimit(req);
        if (rateLimitResponse) return rateLimitResponse;
      }

      return NextResponse.next();
    } catch (error) {
      console.error('Rate limiting error:', error);
      // Continue without rate limiting if there's an error
      return NextResponse.next();
    }
  },

  afterAuth: (auth, req) => {
    // Add user ID to headers for rate limiting
    if (auth.userId) {
      const response = NextResponse.next();
      response.headers.set('x-user-id', auth.userId);
      return response;
    }
    return NextResponse.next();
  }
});

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico|.*\\..*).*)'],
};