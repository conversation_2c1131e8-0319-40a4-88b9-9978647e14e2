"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/team-data/route";
exports.ids = ["app/api/team-data/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:events");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:process");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fteam-data%2Froute&page=%2Fapi%2Fteam-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam-data%2Froute.ts&appDir=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fteam-data%2Froute&page=%2Fapi%2Fteam-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam-data%2Froute.ts&appDir=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_francescooddo_Downloads_Coding_IBC_Group_Creator_src_app_api_team_data_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/team-data/route.ts */ \"(rsc)/./src/app/api/team-data/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/team-data/route\",\n        pathname: \"/api/team-data\",\n        filename: \"route\",\n        bundlePath: \"app/api/team-data/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Downloads/Coding/IBC/Group Creator/src/app/api/team-data/route.ts\",\n    nextConfigOutput,\n    userland: _Users_francescooddo_Downloads_Coding_IBC_Group_Creator_src_app_api_team_data_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/team-data/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fteam-data%2Froute&page=%2Fapi%2Fteam-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam-data%2Froute.ts&appDir=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/team-data/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/team-data/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! googleapis */ \"(rsc)/./node_modules/googleapis/build/src/index.js\");\nconst dynamic = \"force-dynamic\";\n\n\nasync function GET() {\n    try {\n        const clientEmail = process.env.GOOGLE_SHEETS_CLIENT_EMAIL?.replace(/\\\\n/g, \"\\n\");\n        const privateKeyRaw = process.env.GOOGLE_SHEETS_PRIVATE_KEY;\n        const privateKey = privateKeyRaw?.replace(/\\\\n/g, \"\\n\");\n        if (!clientEmail || !privateKey) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing Google Sheets credentials\"\n            }, {\n                status: 500\n            });\n        }\n        const jwtClient = new googleapis__WEBPACK_IMPORTED_MODULE_1__.google.auth.JWT({\n            email: clientEmail.replace(/\"/g, \"\").trim(),\n            key: privateKey.replace(/\"/g, \"\").trim(),\n            scopes: [\n                \"https://www.googleapis.com/auth/spreadsheets.readonly\"\n            ]\n        });\n        const sheets = googleapis__WEBPACK_IMPORTED_MODULE_1__.google.sheets({\n            version: \"v4\",\n            auth: jwtClient\n        });\n        const spreadsheetId = process.env.GOOGLE_SHEETS_SPREADSHEET_ID?.replace(/\"/g, \"\").trim();\n        if (!spreadsheetId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing spreadsheet ID\"\n            }, {\n                status: 500\n            });\n        }\n        const firstSheetName = \"Partnership Representatives\".trim();\n        const secondSheetName = \"Outreach Consultants\".trim();\n        // Read first sheet (sales reps)\n        let salesRepsResponse;\n        try {\n            salesRepsResponse = await sheets.spreadsheets.values.get({\n                spreadsheetId,\n                range: firstSheetName\n            });\n        } catch (error) {\n            console.error(\"Error fetching Partnership Representatives sheet:\", error.message);\n            throw error;\n        }\n        console.log(\"Raw Partnership Representatives data:\", salesRepsResponse.data);\n        const salesRepsRows = salesRepsResponse.data.values || [];\n        const salesRepsData = salesRepsRows.slice(1).map((row)=>({\n                name: row[0] || \"\",\n                emoji: row[1] || \"\",\n                username: row[2] || \"\",\n                paUsername: row[3] || \"\",\n                calendarLink: row[4] || \"\",\n                category: row[5] || \"\"\n            }));\n        // Read second sheet (outreach members)\n        let outreachResponse;\n        try {\n            outreachResponse = await sheets.spreadsheets.values.get({\n                spreadsheetId,\n                range: secondSheetName\n            });\n        } catch (error) {\n            console.error(\"Error fetching Outreach Consultants sheet:\", error.message);\n            throw error;\n        }\n        console.log(\"Raw Outreach Consultants data:\", outreachResponse.data);\n        const outreachRows = outreachResponse.data.values || [];\n        const outreachHeaders = outreachRows[0] || [];\n        const outreachData = outreachRows.slice(1).map((row)=>{\n            const obj = {};\n            outreachHeaders.forEach((header, idx)=>{\n                obj[header.trim().toLowerCase()] = row[idx] || \"\";\n            });\n            // Parse telegram usernames from columns 4, 5, and 6, filtering out empty or whitespace-only strings\n            const telegramUsernames = [\n                row[4],\n                row[5],\n                row[6]\n            ].map((s)=>(s || \"\").trim()).filter((s)=>s);\n            return {\n                name: obj[\"name\"] || \"\",\n                emoji: obj[\"emoji\"] || \"\",\n                telegramUsernames\n            };\n        });\n        console.log(\"Final salesReps array:\", salesRepsData);\n        console.log(\"Final outreachMembers array:\", outreachData);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            salesReps: salesRepsData,\n            outreachMembers: outreachData\n        });\n    } catch (error) {\n        console.error(\"Error fetching team data:\", error.message);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to fetch team data\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/team-data/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/debug","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-flag","vendor-chunks/googleapis","vendor-chunks/google-auth-library","vendor-chunks/uuid","vendor-chunks/googleapis-common","vendor-chunks/math-intrinsics","vendor-chunks/gaxios","vendor-chunks/es-errors","vendor-chunks/whatwg-url","vendor-chunks/qs","vendor-chunks/jws","vendor-chunks/call-bind-apply-helpers","vendor-chunks/json-bigint","vendor-chunks/google-logging-utils","vendor-chunks/get-proto","vendor-chunks/tr46","vendor-chunks/object-inspect","vendor-chunks/https-proxy-agent","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/gcp-metadata","vendor-chunks/function-bind","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/agent-base","vendor-chunks/node-fetch","vendor-chunks/webidl-conversions","vendor-chunks/url-template","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/safe-buffer","vendor-chunks/jwa","vendor-chunks/is-stream","vendor-chunks/hasown","vendor-chunks/gtoken","vendor-chunks/get-intrinsic","vendor-chunks/extend","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound","vendor-chunks/buffer-equal-constant-time","vendor-chunks/bignumber.js","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fteam-data%2Froute&page=%2Fapi%2Fteam-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteam-data%2Froute.ts&appDir=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();