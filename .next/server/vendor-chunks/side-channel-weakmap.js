"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-weakmap";
exports.ids = ["vendor-chunks/side-channel-weakmap"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-weakmap/index.js":
/*!****************************************************!*\
  !*** ./node_modules/side-channel-weakmap/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar GetIntrinsic = __webpack_require__(/*! get-intrinsic */ \"(rsc)/./node_modules/get-intrinsic/index.js\");\nvar callBound = __webpack_require__(/*! call-bound */ \"(rsc)/./node_modules/call-bound/index.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar getSideChannelMap = __webpack_require__(/*! side-channel-map */ \"(rsc)/./node_modules/side-channel-map/index.js\");\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar $WeakMap = GetIntrinsic(\"%WeakMap%\", true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => V} */ var $weakMapGet = callBound(\"WeakMap.prototype.get\", true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K, value: V) => void} */ var $weakMapSet = callBound(\"WeakMap.prototype.set\", true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */ var $weakMapHas = callBound(\"WeakMap.prototype.has\", true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */ var $weakMapDelete = callBound(\"WeakMap.prototype.delete\", true);\n/** @type {import('.')} */ module.exports = $WeakMap ? /** @type {Exclude<import('.'), false>} */ function getSideChannelWeakMap() {\n    /** @typedef {ReturnType<typeof getSideChannelWeakMap>} Channel */ /** @typedef {Parameters<Channel['get']>[0]} K */ /** @typedef {Parameters<Channel['set']>[1]} V */ /** @type {WeakMap<K & object, V> | undefined} */ var $wm;\n    /** @type {Channel | undefined} */ var $m;\n    /** @type {Channel} */ var channel = {\n        assert: function(key) {\n            if (!channel.has(key)) {\n                throw new $TypeError(\"Side channel does not contain \" + inspect(key));\n            }\n        },\n        \"delete\": function(key) {\n            if ($WeakMap && key && (typeof key === \"object\" || typeof key === \"function\")) {\n                if ($wm) {\n                    return $weakMapDelete($wm, key);\n                }\n            } else if (getSideChannelMap) {\n                if ($m) {\n                    return $m[\"delete\"](key);\n                }\n            }\n            return false;\n        },\n        get: function(key) {\n            if ($WeakMap && key && (typeof key === \"object\" || typeof key === \"function\")) {\n                if ($wm) {\n                    return $weakMapGet($wm, key);\n                }\n            }\n            return $m && $m.get(key);\n        },\n        has: function(key) {\n            if ($WeakMap && key && (typeof key === \"object\" || typeof key === \"function\")) {\n                if ($wm) {\n                    return $weakMapHas($wm, key);\n                }\n            }\n            return !!$m && $m.has(key);\n        },\n        set: function(key, value) {\n            if ($WeakMap && key && (typeof key === \"object\" || typeof key === \"function\")) {\n                if (!$wm) {\n                    $wm = new $WeakMap();\n                }\n                $weakMapSet($wm, key, value);\n            } else if (getSideChannelMap) {\n                if (!$m) {\n                    $m = getSideChannelMap();\n                }\n                // eslint-disable-next-line no-extra-parens\n                /** @type {NonNullable<typeof $m>} */ $m.set(key, value);\n            }\n        }\n    };\n    // @ts-expect-error TODO: figure out why this is erroring\n    return channel;\n} : getSideChannelMap;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-weakmap/index.js\n");

/***/ })

};
;