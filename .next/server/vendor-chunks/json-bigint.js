"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/json-bigint";
exports.ids = ["vendor-chunks/json-bigint"];
exports.modules = {

/***/ "(rsc)/./node_modules/json-bigint/index.js":
/*!*******************************************!*\
  !*** ./node_modules/json-bigint/index.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar json_stringify = (__webpack_require__(/*! ./lib/stringify.js */ \"(rsc)/./node_modules/json-bigint/lib/stringify.js\").stringify);\nvar json_parse = __webpack_require__(/*! ./lib/parse.js */ \"(rsc)/./node_modules/json-bigint/lib/parse.js\");\nmodule.exports = function(options) {\n    return {\n        parse: json_parse(options),\n        stringify: json_stringify\n    };\n};\n//create the default method members with no options applied for backwards compatibility\nmodule.exports.parse = json_parse();\nmodule.exports.stringify = json_stringify;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbi1iaWdpbnQvaW5kZXguanMiLCJtYXBwaW5ncyI6IjtBQUFBLElBQUlBLGlCQUFpQkMsOEdBQXVDO0FBQzVELElBQUlFLGFBQWlCRixtQkFBT0EsQ0FBQztBQUU3QkcsT0FBT0MsT0FBTyxHQUFHLFNBQVNDLE9BQU87SUFDN0IsT0FBUTtRQUNKQyxPQUFPSixXQUFXRztRQUNsQkosV0FBV0Y7SUFDZjtBQUNKO0FBQ0EsdUZBQXVGO0FBQ3ZGSSxvQkFBb0IsR0FBR0Q7QUFDdkJDLHdCQUF3QixHQUFHSiIsInNvdXJjZXMiOlsid2VicGFjazovL2N1c3RvbS1ncm91cC1jcmVhdG9yLy4vbm9kZV9tb2R1bGVzL2pzb24tYmlnaW50L2luZGV4LmpzP2NkOGMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGpzb25fc3RyaW5naWZ5ID0gcmVxdWlyZSgnLi9saWIvc3RyaW5naWZ5LmpzJykuc3RyaW5naWZ5O1xudmFyIGpzb25fcGFyc2UgICAgID0gcmVxdWlyZSgnLi9saWIvcGFyc2UuanMnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbihvcHRpb25zKSB7XG4gICAgcmV0dXJuICB7XG4gICAgICAgIHBhcnNlOiBqc29uX3BhcnNlKG9wdGlvbnMpLFxuICAgICAgICBzdHJpbmdpZnk6IGpzb25fc3RyaW5naWZ5XG4gICAgfVxufTtcbi8vY3JlYXRlIHRoZSBkZWZhdWx0IG1ldGhvZCBtZW1iZXJzIHdpdGggbm8gb3B0aW9ucyBhcHBsaWVkIGZvciBiYWNrd2FyZHMgY29tcGF0aWJpbGl0eVxubW9kdWxlLmV4cG9ydHMucGFyc2UgPSBqc29uX3BhcnNlKCk7XG5tb2R1bGUuZXhwb3J0cy5zdHJpbmdpZnkgPSBqc29uX3N0cmluZ2lmeTtcbiJdLCJuYW1lcyI6WyJqc29uX3N0cmluZ2lmeSIsInJlcXVpcmUiLCJzdHJpbmdpZnkiLCJqc29uX3BhcnNlIiwibW9kdWxlIiwiZXhwb3J0cyIsIm9wdGlvbnMiLCJwYXJzZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/json-bigint/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/json-bigint/lib/parse.js":
/*!***********************************************!*\
  !*** ./node_modules/json-bigint/lib/parse.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar BigNumber = null;\n// regexpxs extracted from\n// (c) BSD-3-Clause\n// https://github.com/fastify/secure-json-parse/graphs/contributors and https://github.com/hapijs/bourne/graphs/contributors\nconst suspectProtoRx = /(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])(?:p|\\\\u0070)(?:r|\\\\u0072)(?:o|\\\\u006[Ff])(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])/;\nconst suspectConstructorRx = /(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)/;\n/*\n    json_parse.js\n    2012-06-20\n\n    Public Domain.\n\n    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\n    This file creates a json_parse function.\n    During create you can (optionally) specify some behavioural switches\n\n        require('json-bigint')(options)\n\n            The optional options parameter holds switches that drive certain\n            aspects of the parsing process:\n            * options.strict = true will warn about duplicate-key usage in the json.\n              The default (strict = false) will silently ignore those and overwrite\n              values for keys that are in duplicate use.\n\n    The resulting function follows this signature:\n        json_parse(text, reviver)\n            This method parses a JSON text to produce an object or array.\n            It can throw a SyntaxError exception.\n\n            The optional reviver parameter is a function that can filter and\n            transform the results. It receives each of the keys and values,\n            and its return value is used instead of the original value.\n            If it returns what it received, then the structure is not modified.\n            If it returns undefined then the member is deleted.\n\n            Example:\n\n            // Parse the text. Values that look like ISO date strings will\n            // be converted to Date objects.\n\n            myData = json_parse(text, function (key, value) {\n                var a;\n                if (typeof value === 'string') {\n                    a =\n/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n                    if (a) {\n                        return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n                            +a[5], +a[6]));\n                    }\n                }\n                return value;\n            });\n\n    This is a reference implementation. You are free to copy, modify, or\n    redistribute.\n\n    This code should be minified before deployment.\n    See http://javascript.crockford.com/jsmin.html\n\n    USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n    NOT CONTROL.\n*/ /*members \"\", \"\\\"\", \"\\/\", \"\\\\\", at, b, call, charAt, f, fromCharCode,\n    hasOwnProperty, message, n, name, prototype, push, r, t, text\n*/ var json_parse = function(options) {\n    \"use strict\";\n    // This is a function that can parse a JSON text, producing a JavaScript\n    // data structure. It is a simple, recursive descent parser. It does not use\n    // eval or regular expressions, so it can be used as a model for implementing\n    // a JSON parser in other languages.\n    // We are defining the function inside of another function to avoid creating\n    // global variables.\n    // Default options one can override by passing options to the parse()\n    var _options = {\n        strict: false,\n        storeAsString: false,\n        alwaysParseAsBig: false,\n        useNativeBigInt: false,\n        protoAction: \"error\",\n        constructorAction: \"error\"\n    };\n    // If there are options, then use them to override the default _options\n    if (options !== undefined && options !== null) {\n        if (options.strict === true) {\n            _options.strict = true;\n        }\n        if (options.storeAsString === true) {\n            _options.storeAsString = true;\n        }\n        _options.alwaysParseAsBig = options.alwaysParseAsBig === true ? options.alwaysParseAsBig : false;\n        _options.useNativeBigInt = options.useNativeBigInt === true ? options.useNativeBigInt : false;\n        if (typeof options.constructorAction !== \"undefined\") {\n            if (options.constructorAction === \"error\" || options.constructorAction === \"ignore\" || options.constructorAction === \"preserve\") {\n                _options.constructorAction = options.constructorAction;\n            } else {\n                throw new Error(`Incorrect value for constructorAction option, must be \"error\", \"ignore\" or undefined but passed ${options.constructorAction}`);\n            }\n        }\n        if (typeof options.protoAction !== \"undefined\") {\n            if (options.protoAction === \"error\" || options.protoAction === \"ignore\" || options.protoAction === \"preserve\") {\n                _options.protoAction = options.protoAction;\n            } else {\n                throw new Error(`Incorrect value for protoAction option, must be \"error\", \"ignore\" or undefined but passed ${options.protoAction}`);\n            }\n        }\n    }\n    var at, ch, escapee = {\n        '\"': '\"',\n        \"\\\\\": \"\\\\\",\n        \"/\": \"/\",\n        b: \"\\b\",\n        f: \"\\f\",\n        n: \"\\n\",\n        r: \"\\r\",\n        t: \"\t\"\n    }, text, error = function(m) {\n        // Call error when something is wrong.\n        throw {\n            name: \"SyntaxError\",\n            message: m,\n            at: at,\n            text: text\n        };\n    }, next = function(c) {\n        // If a c parameter is provided, verify that it matches the current character.\n        if (c && c !== ch) {\n            error(\"Expected '\" + c + \"' instead of '\" + ch + \"'\");\n        }\n        // Get the next character. When there are no more characters,\n        // return the empty string.\n        ch = text.charAt(at);\n        at += 1;\n        return ch;\n    }, number = function() {\n        // Parse a number value.\n        var number, string = \"\";\n        if (ch === \"-\") {\n            string = \"-\";\n            next(\"-\");\n        }\n        while(ch >= \"0\" && ch <= \"9\"){\n            string += ch;\n            next();\n        }\n        if (ch === \".\") {\n            string += \".\";\n            while(next() && ch >= \"0\" && ch <= \"9\"){\n                string += ch;\n            }\n        }\n        if (ch === \"e\" || ch === \"E\") {\n            string += ch;\n            next();\n            if (ch === \"-\" || ch === \"+\") {\n                string += ch;\n                next();\n            }\n            while(ch >= \"0\" && ch <= \"9\"){\n                string += ch;\n                next();\n            }\n        }\n        number = +string;\n        if (!isFinite(number)) {\n            error(\"Bad number\");\n        } else {\n            if (BigNumber == null) BigNumber = __webpack_require__(/*! bignumber.js */ \"(rsc)/./node_modules/bignumber.js/bignumber.js\");\n            //if (number > 9007199254740992 || number < -9007199254740992)\n            // Bignumber has stricter check: everything with length > 15 digits disallowed\n            if (string.length > 15) return _options.storeAsString ? string : _options.useNativeBigInt ? BigInt(string) : new BigNumber(string);\n            else return !_options.alwaysParseAsBig ? number : _options.useNativeBigInt ? BigInt(number) : new BigNumber(number);\n        }\n    }, string = function() {\n        // Parse a string value.\n        var hex, i, string = \"\", uffff;\n        // When parsing for string values, we must look for \" and \\ characters.\n        if (ch === '\"') {\n            var startAt = at;\n            while(next()){\n                if (ch === '\"') {\n                    if (at - 1 > startAt) string += text.substring(startAt, at - 1);\n                    next();\n                    return string;\n                }\n                if (ch === \"\\\\\") {\n                    if (at - 1 > startAt) string += text.substring(startAt, at - 1);\n                    next();\n                    if (ch === \"u\") {\n                        uffff = 0;\n                        for(i = 0; i < 4; i += 1){\n                            hex = parseInt(next(), 16);\n                            if (!isFinite(hex)) {\n                                break;\n                            }\n                            uffff = uffff * 16 + hex;\n                        }\n                        string += String.fromCharCode(uffff);\n                    } else if (typeof escapee[ch] === \"string\") {\n                        string += escapee[ch];\n                    } else {\n                        break;\n                    }\n                    startAt = at;\n                }\n            }\n        }\n        error(\"Bad string\");\n    }, white = function() {\n        // Skip whitespace.\n        while(ch && ch <= \" \"){\n            next();\n        }\n    }, word = function() {\n        // true, false, or null.\n        switch(ch){\n            case \"t\":\n                next(\"t\");\n                next(\"r\");\n                next(\"u\");\n                next(\"e\");\n                return true;\n            case \"f\":\n                next(\"f\");\n                next(\"a\");\n                next(\"l\");\n                next(\"s\");\n                next(\"e\");\n                return false;\n            case \"n\":\n                next(\"n\");\n                next(\"u\");\n                next(\"l\");\n                next(\"l\");\n                return null;\n        }\n        error(\"Unexpected '\" + ch + \"'\");\n    }, value, array = function() {\n        // Parse an array value.\n        var array = [];\n        if (ch === \"[\") {\n            next(\"[\");\n            white();\n            if (ch === \"]\") {\n                next(\"]\");\n                return array; // empty array\n            }\n            while(ch){\n                array.push(value());\n                white();\n                if (ch === \"]\") {\n                    next(\"]\");\n                    return array;\n                }\n                next(\",\");\n                white();\n            }\n        }\n        error(\"Bad array\");\n    }, object = function() {\n        // Parse an object value.\n        var key, object = Object.create(null);\n        if (ch === \"{\") {\n            next(\"{\");\n            white();\n            if (ch === \"}\") {\n                next(\"}\");\n                return object; // empty object\n            }\n            while(ch){\n                key = string();\n                white();\n                next(\":\");\n                if (_options.strict === true && Object.hasOwnProperty.call(object, key)) {\n                    error('Duplicate key \"' + key + '\"');\n                }\n                if (suspectProtoRx.test(key) === true) {\n                    if (_options.protoAction === \"error\") {\n                        error(\"Object contains forbidden prototype property\");\n                    } else if (_options.protoAction === \"ignore\") {\n                        value();\n                    } else {\n                        object[key] = value();\n                    }\n                } else if (suspectConstructorRx.test(key) === true) {\n                    if (_options.constructorAction === \"error\") {\n                        error(\"Object contains forbidden constructor property\");\n                    } else if (_options.constructorAction === \"ignore\") {\n                        value();\n                    } else {\n                        object[key] = value();\n                    }\n                } else {\n                    object[key] = value();\n                }\n                white();\n                if (ch === \"}\") {\n                    next(\"}\");\n                    return object;\n                }\n                next(\",\");\n                white();\n            }\n        }\n        error(\"Bad object\");\n    };\n    value = function() {\n        // Parse a JSON value. It could be an object, an array, a string, a number,\n        // or a word.\n        white();\n        switch(ch){\n            case \"{\":\n                return object();\n            case \"[\":\n                return array();\n            case '\"':\n                return string();\n            case \"-\":\n                return number();\n            default:\n                return ch >= \"0\" && ch <= \"9\" ? number() : word();\n        }\n    };\n    // Return the json_parse function. It will have access to all of the above\n    // functions and variables.\n    return function(source, reviver) {\n        var result;\n        text = source + \"\";\n        at = 0;\n        ch = \" \";\n        result = value();\n        white();\n        if (ch) {\n            error(\"Syntax error\");\n        }\n        // If there is a reviver function, we recursively walk the new structure,\n        // passing each name/value pair to the reviver function for possible\n        // transformation, starting with a temporary root object that holds the result\n        // in an empty key. If there is not a reviver function, we simply return the\n        // result.\n        return typeof reviver === \"function\" ? function walk(holder, key) {\n            var k, v, value = holder[key];\n            if (value && typeof value === \"object\") {\n                Object.keys(value).forEach(function(k) {\n                    v = walk(value, k);\n                    if (v !== undefined) {\n                        value[k] = v;\n                    } else {\n                        delete value[k];\n                    }\n                });\n            }\n            return reviver.call(holder, key, value);\n        }({\n            \"\": result\n        }, \"\") : result;\n    };\n};\nmodule.exports = json_parse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/json-bigint/lib/parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/json-bigint/lib/stringify.js":
/*!***************************************************!*\
  !*** ./node_modules/json-bigint/lib/stringify.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar BigNumber = __webpack_require__(/*! bignumber.js */ \"(rsc)/./node_modules/bignumber.js/bignumber.js\");\n/*\n    json2.js\n    2013-05-26\n\n    Public Domain.\n\n    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\n    See http://www.JSON.org/js.html\n\n\n    This code should be minified before deployment.\n    See http://javascript.crockford.com/jsmin.html\n\n    USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n    NOT CONTROL.\n\n\n    This file creates a global JSON object containing two methods: stringify\n    and parse.\n\n        JSON.stringify(value, replacer, space)\n            value       any JavaScript value, usually an object or array.\n\n            replacer    an optional parameter that determines how object\n                        values are stringified for objects. It can be a\n                        function or an array of strings.\n\n            space       an optional parameter that specifies the indentation\n                        of nested structures. If it is omitted, the text will\n                        be packed without extra whitespace. If it is a number,\n                        it will specify the number of spaces to indent at each\n                        level. If it is a string (such as '\\t' or '&nbsp;'),\n                        it contains the characters used to indent at each level.\n\n            This method produces a JSON text from a JavaScript value.\n\n            When an object value is found, if the object contains a toJSON\n            method, its toJSON method will be called and the result will be\n            stringified. A toJSON method does not serialize: it returns the\n            value represented by the name/value pair that should be serialized,\n            or undefined if nothing should be serialized. The toJSON method\n            will be passed the key associated with the value, and this will be\n            bound to the value\n\n            For example, this would serialize Dates as ISO strings.\n\n                Date.prototype.toJSON = function (key) {\n                    function f(n) {\n                        // Format integers to have at least two digits.\n                        return n < 10 ? '0' + n : n;\n                    }\n\n                    return this.getUTCFullYear()   + '-' +\n                         f(this.getUTCMonth() + 1) + '-' +\n                         f(this.getUTCDate())      + 'T' +\n                         f(this.getUTCHours())     + ':' +\n                         f(this.getUTCMinutes())   + ':' +\n                         f(this.getUTCSeconds())   + 'Z';\n                };\n\n            You can provide an optional replacer method. It will be passed the\n            key and value of each member, with this bound to the containing\n            object. The value that is returned from your method will be\n            serialized. If your method returns undefined, then the member will\n            be excluded from the serialization.\n\n            If the replacer parameter is an array of strings, then it will be\n            used to select the members to be serialized. It filters the results\n            such that only members with keys listed in the replacer array are\n            stringified.\n\n            Values that do not have JSON representations, such as undefined or\n            functions, will not be serialized. Such values in objects will be\n            dropped; in arrays they will be replaced with null. You can use\n            a replacer function to replace those with JSON values.\n            JSON.stringify(undefined) returns undefined.\n\n            The optional space parameter produces a stringification of the\n            value that is filled with line breaks and indentation to make it\n            easier to read.\n\n            If the space parameter is a non-empty string, then that string will\n            be used for indentation. If the space parameter is a number, then\n            the indentation will be that many spaces.\n\n            Example:\n\n            text = JSON.stringify(['e', {pluribus: 'unum'}]);\n            // text is '[\"e\",{\"pluribus\":\"unum\"}]'\n\n\n            text = JSON.stringify(['e', {pluribus: 'unum'}], null, '\\t');\n            // text is '[\\n\\t\"e\",\\n\\t{\\n\\t\\t\"pluribus\": \"unum\"\\n\\t}\\n]'\n\n            text = JSON.stringify([new Date()], function (key, value) {\n                return this[key] instanceof Date ?\n                    'Date(' + this[key] + ')' : value;\n            });\n            // text is '[\"Date(---current time---)\"]'\n\n\n        JSON.parse(text, reviver)\n            This method parses a JSON text to produce an object or array.\n            It can throw a SyntaxError exception.\n\n            The optional reviver parameter is a function that can filter and\n            transform the results. It receives each of the keys and values,\n            and its return value is used instead of the original value.\n            If it returns what it received, then the structure is not modified.\n            If it returns undefined then the member is deleted.\n\n            Example:\n\n            // Parse the text. Values that look like ISO date strings will\n            // be converted to Date objects.\n\n            myData = JSON.parse(text, function (key, value) {\n                var a;\n                if (typeof value === 'string') {\n                    a =\n/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n                    if (a) {\n                        return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n                            +a[5], +a[6]));\n                    }\n                }\n                return value;\n            });\n\n            myData = JSON.parse('[\"Date(09/09/2001)\"]', function (key, value) {\n                var d;\n                if (typeof value === 'string' &&\n                        value.slice(0, 5) === 'Date(' &&\n                        value.slice(-1) === ')') {\n                    d = new Date(value.slice(5, -1));\n                    if (d) {\n                        return d;\n                    }\n                }\n                return value;\n            });\n\n\n    This is a reference implementation. You are free to copy, modify, or\n    redistribute.\n*/ /*jslint evil: true, regexp: true */ /*members \"\", \"\\b\", \"\\t\", \"\\n\", \"\\f\", \"\\r\", \"\\\"\", JSON, \"\\\\\", apply,\n    call, charCodeAt, getUTCDate, getUTCFullYear, getUTCHours,\n    getUTCMinutes, getUTCMonth, getUTCSeconds, hasOwnProperty, join,\n    lastIndex, length, parse, prototype, push, replace, slice, stringify,\n    test, toJSON, toString, valueOf\n*/ // Create a JSON object only if one does not already exist. We create the\n// methods in a closure to avoid creating global variables.\nvar JSON = module.exports;\n(function() {\n    \"use strict\";\n    function f(n) {\n        // Format integers to have at least two digits.\n        return n < 10 ? \"0\" + n : n;\n    }\n    var cx = /[\\u0000\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g, escapable = /[\\\\\\\"\\x00-\\x1f\\x7f-\\x9f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g, gap, indent, meta = {\n        \"\\b\": \"\\\\b\",\n        \"\t\": \"\\\\t\",\n        \"\\n\": \"\\\\n\",\n        \"\\f\": \"\\\\f\",\n        \"\\r\": \"\\\\r\",\n        '\"': '\\\\\"',\n        \"\\\\\": \"\\\\\\\\\"\n    }, rep;\n    function quote(string) {\n        // If the string contains no control characters, no quote characters, and no\n        // backslash characters, then we can safely slap some quotes around it.\n        // Otherwise we must also replace the offending characters with safe escape\n        // sequences.\n        escapable.lastIndex = 0;\n        return escapable.test(string) ? '\"' + string.replace(escapable, function(a) {\n            var c = meta[a];\n            return typeof c === \"string\" ? c : \"\\\\u\" + (\"0000\" + a.charCodeAt(0).toString(16)).slice(-4);\n        }) + '\"' : '\"' + string + '\"';\n    }\n    function str(key, holder) {\n        // Produce a string from holder[key].\n        var i, k, v, length, mind = gap, partial, value = holder[key], isBigNumber = value != null && (value instanceof BigNumber || BigNumber.isBigNumber(value));\n        // If the value has a toJSON method, call it to obtain a replacement value.\n        if (value && typeof value === \"object\" && typeof value.toJSON === \"function\") {\n            value = value.toJSON(key);\n        }\n        // If we were called with a replacer function, then call the replacer to\n        // obtain a replacement value.\n        if (typeof rep === \"function\") {\n            value = rep.call(holder, key, value);\n        }\n        // What happens next depends on the value's type.\n        switch(typeof value){\n            case \"string\":\n                if (isBigNumber) {\n                    return value;\n                } else {\n                    return quote(value);\n                }\n            case \"number\":\n                // JSON numbers must be finite. Encode non-finite numbers as null.\n                return isFinite(value) ? String(value) : \"null\";\n            case \"boolean\":\n            case \"null\":\n            case \"bigint\":\n                // If the value is a boolean or null, convert it to a string. Note:\n                // typeof null does not produce 'null'. The case is included here in\n                // the remote chance that this gets fixed someday.\n                return String(value);\n            // If the type is 'object', we might be dealing with an object or an array or\n            // null.\n            case \"object\":\n                // Due to a specification blunder in ECMAScript, typeof null is 'object',\n                // so watch out for that case.\n                if (!value) {\n                    return \"null\";\n                }\n                // Make an array to hold the partial results of stringifying this object value.\n                gap += indent;\n                partial = [];\n                // Is the value an array?\n                if (Object.prototype.toString.apply(value) === \"[object Array]\") {\n                    // The value is an array. Stringify every element. Use null as a placeholder\n                    // for non-JSON values.\n                    length = value.length;\n                    for(i = 0; i < length; i += 1){\n                        partial[i] = str(i, value) || \"null\";\n                    }\n                    // Join all of the elements together, separated with commas, and wrap them in\n                    // brackets.\n                    v = partial.length === 0 ? \"[]\" : gap ? \"[\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"]\" : \"[\" + partial.join(\",\") + \"]\";\n                    gap = mind;\n                    return v;\n                }\n                // If the replacer is an array, use it to select the members to be stringified.\n                if (rep && typeof rep === \"object\") {\n                    length = rep.length;\n                    for(i = 0; i < length; i += 1){\n                        if (typeof rep[i] === \"string\") {\n                            k = rep[i];\n                            v = str(k, value);\n                            if (v) {\n                                partial.push(quote(k) + (gap ? \": \" : \":\") + v);\n                            }\n                        }\n                    }\n                } else {\n                    // Otherwise, iterate through all of the keys in the object.\n                    Object.keys(value).forEach(function(k) {\n                        var v = str(k, value);\n                        if (v) {\n                            partial.push(quote(k) + (gap ? \": \" : \":\") + v);\n                        }\n                    });\n                }\n                // Join all of the member texts together, separated with commas,\n                // and wrap them in braces.\n                v = partial.length === 0 ? \"{}\" : gap ? \"{\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"}\" : \"{\" + partial.join(\",\") + \"}\";\n                gap = mind;\n                return v;\n        }\n    }\n    // If the JSON object does not yet have a stringify method, give it one.\n    if (typeof JSON.stringify !== \"function\") {\n        JSON.stringify = function(value, replacer, space) {\n            // The stringify method takes a value and an optional replacer, and an optional\n            // space parameter, and returns a JSON text. The replacer can be a function\n            // that can replace values, or an array of strings that will select the keys.\n            // A default replacer method can be provided. Use of the space parameter can\n            // produce text that is more easily readable.\n            var i;\n            gap = \"\";\n            indent = \"\";\n            // If the space parameter is a number, make an indent string containing that\n            // many spaces.\n            if (typeof space === \"number\") {\n                for(i = 0; i < space; i += 1){\n                    indent += \" \";\n                }\n            // If the space parameter is a string, it will be used as the indent string.\n            } else if (typeof space === \"string\") {\n                indent = space;\n            }\n            // If there is a replacer, it must be a function or an array.\n            // Otherwise, throw an error.\n            rep = replacer;\n            if (replacer && typeof replacer !== \"function\" && (typeof replacer !== \"object\" || typeof replacer.length !== \"number\")) {\n                throw new Error(\"JSON.stringify\");\n            }\n            // Make a fake root object containing our value under the key of ''.\n            // Return the result of stringifying the value.\n            return str(\"\", {\n                \"\": value\n            });\n        };\n    }\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/json-bigint/lib/stringify.js\n");

/***/ })

};
;