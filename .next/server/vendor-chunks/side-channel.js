"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel";
exports.ids = ["vendor-chunks/side-channel"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel/index.js":
/*!********************************************!*\
  !*** ./node_modules/side-channel/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar getSideChannelList = __webpack_require__(/*! side-channel-list */ \"(rsc)/./node_modules/side-channel-list/index.js\");\nvar getSideChannelMap = __webpack_require__(/*! side-channel-map */ \"(rsc)/./node_modules/side-channel-map/index.js\");\nvar getSideChannelWeakMap = __webpack_require__(/*! side-channel-weakmap */ \"(rsc)/./node_modules/side-channel-weakmap/index.js\");\nvar makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;\n/** @type {import('.')} */ module.exports = function getSideChannel() {\n    /** @typedef {ReturnType<typeof getSideChannel>} Channel */ /** @type {Channel | undefined} */ var $channelData;\n    /** @type {Channel} */ var channel = {\n        assert: function(key) {\n            if (!channel.has(key)) {\n                throw new $TypeError(\"Side channel does not contain \" + inspect(key));\n            }\n        },\n        \"delete\": function(key) {\n            return !!$channelData && $channelData[\"delete\"](key);\n        },\n        get: function(key) {\n            return $channelData && $channelData.get(key);\n        },\n        has: function(key) {\n            return !!$channelData && $channelData.has(key);\n        },\n        set: function(key, value) {\n            if (!$channelData) {\n                $channelData = makeChannel();\n            }\n            $channelData.set(key, value);\n        }\n    };\n    // @ts-expect-error TODO: figure out why this is erroring\n    return channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel/index.js\n");

/***/ })

};
;