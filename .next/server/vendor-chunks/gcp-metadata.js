"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gcp-metadata";
exports.ids = ["vendor-chunks/gcp-metadata"];
exports.modules = {

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js":
/*!**************************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/gcp-residency.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */ Object.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GCE_LINUX_BIOS_PATHS = void 0;\nexports.isGoogleCloudServerless = isGoogleCloudServerless;\nexports.isGoogleComputeEngineLinux = isGoogleComputeEngineLinux;\nexports.isGoogleComputeEngineMACAddress = isGoogleComputeEngineMACAddress;\nexports.isGoogleComputeEngine = isGoogleComputeEngine;\nexports.detectGCPResidency = detectGCPResidency;\nconst fs_1 = __webpack_require__(/*! fs */ \"fs\");\nconst os_1 = __webpack_require__(/*! os */ \"os\");\n/**\n * Known paths unique to Google Compute Engine Linux instances\n */ exports.GCE_LINUX_BIOS_PATHS = {\n    BIOS_DATE: \"/sys/class/dmi/id/bios_date\",\n    BIOS_VENDOR: \"/sys/class/dmi/id/bios_vendor\"\n};\nconst GCE_MAC_ADDRESS_REGEX = /^42:01/;\n/**\n * Determines if the process is running on a Google Cloud Serverless environment (Cloud Run or Cloud Functions instance).\n *\n * Uses the:\n * - {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n * - {@link https://cloud.google.com/functions/docs/env-var Cloud Functions environment variables}.\n *\n * @returns {boolean} `true` if the process is running on GCP serverless, `false` otherwise.\n */ function isGoogleCloudServerless() {\n    /**\n     * `CLOUD_RUN_JOB` is used for Cloud Run Jobs\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     *\n     * `FUNCTION_NAME` is used in older Cloud Functions environments:\n     * - See {@link https://cloud.google.com/functions/docs/env-var Python 3.7 and Go 1.11}.\n     *\n     * `K_SERVICE` is used in Cloud Run and newer Cloud Functions environments:\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     * - See {@link https://cloud.google.com/functions/docs/env-var Cloud Functions newer runtimes}.\n     */ const isGFEnvironment = process.env.CLOUD_RUN_JOB || process.env.FUNCTION_NAME || process.env.K_SERVICE;\n    return !!isGFEnvironment;\n}\n/**\n * Determines if the process is running on a Linux Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on Linux GCE, `false` otherwise.\n */ function isGoogleComputeEngineLinux() {\n    if ((0, os_1.platform)() !== \"linux\") return false;\n    try {\n        // ensure this file exist\n        (0, fs_1.statSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_DATE);\n        // ensure this file exist and matches\n        const biosVendor = (0, fs_1.readFileSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_VENDOR, \"utf8\");\n        return /Google/.test(biosVendor);\n    } catch (_a) {\n        return false;\n    }\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance with a known\n * MAC address.\n *\n * @returns {boolean} `true` if the process is running on GCE (as determined by MAC address), `false` otherwise.\n */ function isGoogleComputeEngineMACAddress() {\n    const interfaces = (0, os_1.networkInterfaces)();\n    for (const item of Object.values(interfaces)){\n        if (!item) continue;\n        for (const { mac } of item){\n            if (GCE_MAC_ADDRESS_REGEX.test(mac)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on GCE, `false` otherwise.\n */ function isGoogleComputeEngine() {\n    return isGoogleComputeEngineLinux() || isGoogleComputeEngineMACAddress();\n}\n/**\n * Determines if the process is running on Google Cloud Platform.\n *\n * @returns {boolean} `true` if the process is running on GCP, `false` otherwise.\n */ function detectGCPResidency() {\n    return isGoogleCloudServerless() || isGoogleComputeEngine();\n} //# sourceMappingURL=gcp-residency.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/index.js":
/*!******************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */ var __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.gcpResidencyCache = exports.METADATA_SERVER_DETECTION = exports.HEADERS = exports.HEADER_VALUE = exports.HEADER_NAME = exports.SECONDARY_HOST_ADDRESS = exports.HOST_ADDRESS = exports.BASE_PATH = void 0;\nexports.instance = instance;\nexports.project = project;\nexports.universe = universe;\nexports.bulk = bulk;\nexports.isAvailable = isAvailable;\nexports.resetIsAvailableCache = resetIsAvailableCache;\nexports.getGCPResidency = getGCPResidency;\nexports.setGCPResidency = setGCPResidency;\nexports.requestTimeout = requestTimeout;\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/src/index.js\");\nconst jsonBigint = __webpack_require__(/*! json-bigint */ \"(rsc)/./node_modules/json-bigint/index.js\");\nconst gcp_residency_1 = __webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\");\nconst logger = __webpack_require__(/*! google-logging-utils */ \"(rsc)/./node_modules/google-logging-utils/build/src/index.js\");\nexports.BASE_PATH = \"/computeMetadata/v1\";\nexports.HOST_ADDRESS = \"http://***************\";\nexports.SECONDARY_HOST_ADDRESS = \"http://metadata.google.internal.\";\nexports.HEADER_NAME = \"Metadata-Flavor\";\nexports.HEADER_VALUE = \"Google\";\nexports.HEADERS = Object.freeze({\n    [exports.HEADER_NAME]: exports.HEADER_VALUE\n});\nconst log = logger.log(\"gcp metadata\");\n/**\n * Metadata server detection override options.\n *\n * Available via `process.env.METADATA_SERVER_DETECTION`.\n */ exports.METADATA_SERVER_DETECTION = Object.freeze({\n    \"assume-present\": \"don't try to ping the metadata server, but assume it's present\",\n    none: \"don't try to ping the metadata server, but don't try to use it either\",\n    \"bios-only\": \"treat the result of a BIOS probe as canonical (don't fall back to pinging)\",\n    \"ping-only\": \"skip the BIOS probe, and go straight to pinging\"\n});\n/**\n * Returns the base URL while taking into account the GCE_METADATA_HOST\n * environment variable if it exists.\n *\n * @returns The base URL, e.g., http://***************/computeMetadata/v1.\n */ function getBaseUrl(baseUrl) {\n    if (!baseUrl) {\n        baseUrl = process.env.GCE_METADATA_IP || process.env.GCE_METADATA_HOST || exports.HOST_ADDRESS;\n    }\n    // If no scheme is provided default to HTTP:\n    if (!/^https?:\\/\\//.test(baseUrl)) {\n        baseUrl = `http://${baseUrl}`;\n    }\n    return new URL(exports.BASE_PATH, baseUrl).href;\n}\n// Accepts an options object passed from the user to the API. In previous\n// versions of the API, it referred to a `Request` or an `Axios` request\n// options object.  Now it refers to an object with very limited property\n// names. This is here to help ensure users don't pass invalid options when\n// they  upgrade from 0.4 to 0.5 to 0.8.\nfunction validate(options) {\n    Object.keys(options).forEach((key)=>{\n        switch(key){\n            case \"params\":\n            case \"property\":\n            case \"headers\":\n                break;\n            case \"qs\":\n                throw new Error(\"'qs' is not a valid configuration option. Please use 'params' instead.\");\n            default:\n                throw new Error(`'${key}' is not a valid configuration option.`);\n        }\n    });\n}\nasync function metadataAccessor(type, options = {}, noResponseRetries = 3, fastFail = false) {\n    let metadataKey = \"\";\n    let params = {};\n    let headers = {};\n    if (typeof type === \"object\") {\n        const metadataAccessor = type;\n        metadataKey = metadataAccessor.metadataKey;\n        params = metadataAccessor.params || params;\n        headers = metadataAccessor.headers || headers;\n        noResponseRetries = metadataAccessor.noResponseRetries || noResponseRetries;\n        fastFail = metadataAccessor.fastFail || fastFail;\n    } else {\n        metadataKey = type;\n    }\n    if (typeof options === \"string\") {\n        metadataKey += `/${options}`;\n    } else {\n        validate(options);\n        if (options.property) {\n            metadataKey += `/${options.property}`;\n        }\n        headers = options.headers || headers;\n        params = options.params || params;\n    }\n    const requestMethod = fastFail ? fastFailMetadataRequest : gaxios_1.request;\n    const req = {\n        url: `${getBaseUrl()}/${metadataKey}`,\n        headers: {\n            ...exports.HEADERS,\n            ...headers\n        },\n        retryConfig: {\n            noResponseRetries\n        },\n        params,\n        responseType: \"text\",\n        timeout: requestTimeout()\n    };\n    log.info(\"instance request %j\", req);\n    const res = await requestMethod(req);\n    log.info(\"instance metadata is %s\", res.data);\n    // NOTE: node.js converts all incoming headers to lower case.\n    if (res.headers[exports.HEADER_NAME.toLowerCase()] !== exports.HEADER_VALUE) {\n        throw new Error(`Invalid response from metadata service: incorrect ${exports.HEADER_NAME} header. Expected '${exports.HEADER_VALUE}', got ${res.headers[exports.HEADER_NAME.toLowerCase()] ? `'${res.headers[exports.HEADER_NAME.toLowerCase()]}'` : \"no header\"}`);\n    }\n    if (typeof res.data === \"string\") {\n        try {\n            return jsonBigint.parse(res.data);\n        } catch (_a) {\n        /* ignore */ }\n    }\n    return res.data;\n}\nasync function fastFailMetadataRequest(options) {\n    var _a;\n    const secondaryOptions = {\n        ...options,\n        url: (_a = options.url) === null || _a === void 0 ? void 0 : _a.toString().replace(getBaseUrl(), getBaseUrl(exports.SECONDARY_HOST_ADDRESS))\n    };\n    // We race a connection between DNS/IP to metadata server. There are a couple\n    // reasons for this:\n    //\n    // 1. the DNS is slow in some GCP environments; by checking both, we might\n    //    detect the runtime environment signficantly faster.\n    // 2. we can't just check the IP, which is tarpitted and slow to respond\n    //    on a user's local machine.\n    //\n    // Additional logic has been added to make sure that we don't create an\n    // unhandled rejection in scenarios where a failure happens sometime\n    // after a success.\n    //\n    // Note, however, if a failure happens prior to a success, a rejection should\n    // occur, this is for folks running locally.\n    //\n    let responded = false;\n    const r1 = (0, gaxios_1.request)(options).then((res)=>{\n        responded = true;\n        return res;\n    }).catch((err)=>{\n        if (responded) {\n            return r2;\n        } else {\n            responded = true;\n            throw err;\n        }\n    });\n    const r2 = (0, gaxios_1.request)(secondaryOptions).then((res)=>{\n        responded = true;\n        return res;\n    }).catch((err)=>{\n        if (responded) {\n            return r1;\n        } else {\n            responded = true;\n            throw err;\n        }\n    });\n    return Promise.race([\n        r1,\n        r2\n    ]);\n}\n/**\n * Obtain metadata for the current GCE instance.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const serviceAccount: {} = await instance('service-accounts/');\n * const serviceAccountEmail: string = await instance('service-accounts/default/email');\n * ```\n */ // eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction instance(options) {\n    return metadataAccessor(\"instance\", options);\n}\n/**\n * Obtain metadata for the current GCP project.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const projectId: string = await project('project-id');\n * const numericProjectId: number = await project('numeric-project-id');\n * ```\n */ // eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction project(options) {\n    return metadataAccessor(\"project\", options);\n}\n/**\n * Obtain metadata for the current universe.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const universeDomain: string = await universe('universe-domain');\n * ```\n */ function universe(options) {\n    return metadataAccessor(\"universe\", options);\n}\n/**\n * Retrieve metadata items in parallel.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const data = await bulk([\n *   {\n *     metadataKey: 'instance',\n *   },\n *   {\n *     metadataKey: 'project/project-id',\n *   },\n * ] as const);\n *\n * // data.instance;\n * // data['project/project-id'];\n * ```\n *\n * @param properties The metadata properties to retrieve\n * @returns The metadata in `metadatakey:value` format\n */ async function bulk(properties) {\n    const r = {};\n    await Promise.all(properties.map((item)=>{\n        return (async ()=>{\n            const res = await metadataAccessor(item);\n            const key = item.metadataKey;\n            r[key] = res;\n        })();\n    }));\n    return r;\n}\n/*\n * How many times should we retry detecting GCP environment.\n */ function detectGCPAvailableRetries() {\n    return process.env.DETECT_GCP_RETRIES ? Number(process.env.DETECT_GCP_RETRIES) : 0;\n}\nlet cachedIsAvailableResponse;\n/**\n * Determine if the metadata server is currently available.\n */ async function isAvailable() {\n    if (process.env.METADATA_SERVER_DETECTION) {\n        const value = process.env.METADATA_SERVER_DETECTION.trim().toLocaleLowerCase();\n        if (!(value in exports.METADATA_SERVER_DETECTION)) {\n            throw new RangeError(`Unknown \\`METADATA_SERVER_DETECTION\\` env variable. Got \\`${value}\\`, but it should be \\`${Object.keys(exports.METADATA_SERVER_DETECTION).join(\"`, `\")}\\`, or unset`);\n        }\n        switch(value){\n            case \"assume-present\":\n                return true;\n            case \"none\":\n                return false;\n            case \"bios-only\":\n                return getGCPResidency();\n            case \"ping-only\":\n        }\n    }\n    try {\n        // If a user is instantiating several GCP libraries at the same time,\n        // this may result in multiple calls to isAvailable(), to detect the\n        // runtime environment. We use the same promise for each of these calls\n        // to reduce the network load.\n        if (cachedIsAvailableResponse === undefined) {\n            cachedIsAvailableResponse = metadataAccessor(\"instance\", undefined, detectGCPAvailableRetries(), // If the default HOST_ADDRESS has been overridden, we should not\n            // make an effort to try SECONDARY_HOST_ADDRESS (as we are likely in\n            // a non-GCP environment):\n            !(process.env.GCE_METADATA_IP || process.env.GCE_METADATA_HOST));\n        }\n        await cachedIsAvailableResponse;\n        return true;\n    } catch (e) {\n        const err = e;\n        if (process.env.DEBUG_AUTH) {\n            console.info(err);\n        }\n        if (err.type === \"request-timeout\") {\n            // If running in a GCP environment, metadata endpoint should return\n            // within ms.\n            return false;\n        }\n        if (err.response && err.response.status === 404) {\n            return false;\n        } else {\n            if (!(err.response && err.response.status === 404) && // A warning is emitted if we see an unexpected err.code, or err.code\n            // is not populated:\n            (!err.code || ![\n                \"EHOSTDOWN\",\n                \"EHOSTUNREACH\",\n                \"ENETUNREACH\",\n                \"ENOENT\",\n                \"ENOTFOUND\",\n                \"ECONNREFUSED\"\n            ].includes(err.code))) {\n                let code = \"UNKNOWN\";\n                if (err.code) code = err.code;\n                process.emitWarning(`received unexpected error = ${err.message} code = ${code}`, \"MetadataLookupWarning\");\n            }\n            // Failure to resolve the metadata service means that it is not available.\n            return false;\n        }\n    }\n}\n/**\n * reset the memoized isAvailable() lookup.\n */ function resetIsAvailableCache() {\n    cachedIsAvailableResponse = undefined;\n}\n/**\n * A cache for the detected GCP Residency.\n */ exports.gcpResidencyCache = null;\n/**\n * Detects GCP Residency.\n * Caches results to reduce costs for subsequent calls.\n *\n * @see setGCPResidency for setting\n */ function getGCPResidency() {\n    if (exports.gcpResidencyCache === null) {\n        setGCPResidency();\n    }\n    return exports.gcpResidencyCache;\n}\n/**\n * Sets the detected GCP Residency.\n * Useful for forcing metadata server detection behavior.\n *\n * Set `null` to autodetect the environment (default behavior).\n * @see getGCPResidency for getting\n */ function setGCPResidency(value = null) {\n    exports.gcpResidencyCache = value !== null ? value : (0, gcp_residency_1.detectGCPResidency)();\n}\n/**\n * Obtain the timeout for requests to the metadata server.\n *\n * In certain environments and conditions requests can take longer than\n * the default timeout to complete. This function will determine the\n * appropriate timeout based on the environment.\n *\n * @returns {number} a request timeout duration in milliseconds.\n */ function requestTimeout() {\n    return getGCPResidency() ? 0 : 3000;\n}\n__exportStar(__webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\"), exports); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/index.js\n");

/***/ })

};
;