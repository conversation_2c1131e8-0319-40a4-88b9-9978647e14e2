"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/asn1js";
exports.ids = ["vendor-chunks/asn1js"];
exports.modules = {

/***/ "(rsc)/./node_modules/asn1js/build/index.es.js":
/*!***********************************************!*\
  !*** ./node_modules/asn1js/build/index.es.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* binding */ Any),\n/* harmony export */   BaseBlock: () => (/* binding */ BaseBlock),\n/* harmony export */   BaseStringBlock: () => (/* binding */ BaseStringBlock),\n/* harmony export */   BitString: () => (/* binding */ BitString),\n/* harmony export */   BmpString: () => (/* binding */ BmpString),\n/* harmony export */   Boolean: () => (/* binding */ Boolean),\n/* harmony export */   CharacterString: () => (/* binding */ CharacterString),\n/* harmony export */   Choice: () => (/* binding */ Choice),\n/* harmony export */   Constructed: () => (/* binding */ Constructed),\n/* harmony export */   DATE: () => (/* binding */ DATE),\n/* harmony export */   DateTime: () => (/* binding */ DateTime),\n/* harmony export */   Duration: () => (/* binding */ Duration),\n/* harmony export */   EndOfContent: () => (/* binding */ EndOfContent),\n/* harmony export */   Enumerated: () => (/* binding */ Enumerated),\n/* harmony export */   GeneralString: () => (/* binding */ GeneralString),\n/* harmony export */   GeneralizedTime: () => (/* binding */ GeneralizedTime),\n/* harmony export */   GraphicString: () => (/* binding */ GraphicString),\n/* harmony export */   HexBlock: () => (/* binding */ HexBlock),\n/* harmony export */   IA5String: () => (/* binding */ IA5String),\n/* harmony export */   Integer: () => (/* binding */ Integer),\n/* harmony export */   Null: () => (/* binding */ Null),\n/* harmony export */   NumericString: () => (/* binding */ NumericString),\n/* harmony export */   ObjectIdentifier: () => (/* binding */ ObjectIdentifier),\n/* harmony export */   OctetString: () => (/* binding */ OctetString),\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   PrintableString: () => (/* binding */ PrintableString),\n/* harmony export */   RawData: () => (/* binding */ RawData),\n/* harmony export */   RelativeObjectIdentifier: () => (/* binding */ RelativeObjectIdentifier),\n/* harmony export */   Repeated: () => (/* binding */ Repeated),\n/* harmony export */   Sequence: () => (/* binding */ Sequence),\n/* harmony export */   Set: () => (/* binding */ Set),\n/* harmony export */   TIME: () => (/* binding */ TIME),\n/* harmony export */   TeletexString: () => (/* binding */ TeletexString),\n/* harmony export */   TimeOfDay: () => (/* binding */ TimeOfDay),\n/* harmony export */   UTCTime: () => (/* binding */ UTCTime),\n/* harmony export */   UniversalString: () => (/* binding */ UniversalString),\n/* harmony export */   Utf8String: () => (/* binding */ Utf8String),\n/* harmony export */   ValueBlock: () => (/* binding */ ValueBlock),\n/* harmony export */   VideotexString: () => (/* binding */ VideotexString),\n/* harmony export */   ViewWriter: () => (/* binding */ ViewWriter),\n/* harmony export */   VisibleString: () => (/* binding */ VisibleString),\n/* harmony export */   compareSchema: () => (/* binding */ compareSchema),\n/* harmony export */   fromBER: () => (/* binding */ fromBER),\n/* harmony export */   verifySchema: () => (/* binding */ verifySchema)\n/* harmony export */ });\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/./node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var pvutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvutils */ \"(rsc)/./node_modules/pvutils/build/utils.es.js\");\n/*!\n * Copyright (c) 2014, GMO GlobalSign\n * Copyright (c) 2015-2022, Peculiar Ventures\n * All rights reserved.\n * \n * Author 2014-2019, Yury Strozhevsky\n * \n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n * \n * * Redistributions of source code must retain the above copyright notice, this\n *   list of conditions and the following disclaimer.\n * \n * * Redistributions in binary form must reproduce the above copyright notice, this\n *   list of conditions and the following disclaimer in the documentation and/or\n *   other materials provided with the distribution.\n * \n * * Neither the name of the copyright holder nor the names of its\n *   contributors may be used to endorse or promote products derived from\n *   this software without specific prior written permission.\n * \n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR\n * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n * \n */ \n\nfunction assertBigInt() {\n    if (typeof BigInt === \"undefined\") {\n        throw new Error(\"BigInt is not defined. Your environment doesn't implement BigInt.\");\n    }\n}\nfunction concat(buffers) {\n    let outputLength = 0;\n    let prevLength = 0;\n    for(let i = 0; i < buffers.length; i++){\n        const buffer = buffers[i];\n        outputLength += buffer.byteLength;\n    }\n    const retView = new Uint8Array(outputLength);\n    for(let i = 0; i < buffers.length; i++){\n        const buffer = buffers[i];\n        retView.set(new Uint8Array(buffer), prevLength);\n        prevLength += buffer.byteLength;\n    }\n    return retView.buffer;\n}\nfunction checkBufferParams(baseBlock, inputBuffer, inputOffset, inputLength) {\n    if (!(inputBuffer instanceof Uint8Array)) {\n        baseBlock.error = \"Wrong parameter: inputBuffer must be 'Uint8Array'\";\n        return false;\n    }\n    if (!inputBuffer.byteLength) {\n        baseBlock.error = \"Wrong parameter: inputBuffer has zero length\";\n        return false;\n    }\n    if (inputOffset < 0) {\n        baseBlock.error = \"Wrong parameter: inputOffset less than zero\";\n        return false;\n    }\n    if (inputLength < 0) {\n        baseBlock.error = \"Wrong parameter: inputLength less than zero\";\n        return false;\n    }\n    if (inputBuffer.byteLength - inputOffset - inputLength < 0) {\n        baseBlock.error = \"End of input reached before message was fully decoded (inconsistent offset and length values)\";\n        return false;\n    }\n    return true;\n}\nclass ViewWriter {\n    constructor(){\n        this.items = [];\n    }\n    write(buf) {\n        this.items.push(buf);\n    }\n    final() {\n        return concat(this.items);\n    }\n}\nconst powers2 = [\n    new Uint8Array([\n        1\n    ])\n];\nconst digitsString = \"0123456789\";\nconst NAME = \"name\";\nconst VALUE_HEX_VIEW = \"valueHexView\";\nconst IS_HEX_ONLY = \"isHexOnly\";\nconst ID_BLOCK = \"idBlock\";\nconst TAG_CLASS = \"tagClass\";\nconst TAG_NUMBER = \"tagNumber\";\nconst IS_CONSTRUCTED = \"isConstructed\";\nconst FROM_BER = \"fromBER\";\nconst TO_BER = \"toBER\";\nconst LOCAL = \"local\";\nconst EMPTY_STRING = \"\";\nconst EMPTY_BUFFER = new ArrayBuffer(0);\nconst EMPTY_VIEW = new Uint8Array(0);\nconst END_OF_CONTENT_NAME = \"EndOfContent\";\nconst OCTET_STRING_NAME = \"OCTET STRING\";\nconst BIT_STRING_NAME = \"BIT STRING\";\nfunction HexBlock(BaseClass) {\n    var _a;\n    return _a = class Some extends BaseClass {\n        constructor(...args){\n            var _a;\n            super(...args);\n            const params = args[0] || {};\n            this.isHexOnly = (_a = params.isHexOnly) !== null && _a !== void 0 ? _a : false;\n            this.valueHexView = params.valueHex ? pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(params.valueHex) : EMPTY_VIEW;\n        }\n        get valueHex() {\n            return this.valueHexView.slice().buffer;\n        }\n        set valueHex(value) {\n            this.valueHexView = new Uint8Array(value);\n        }\n        fromBER(inputBuffer, inputOffset, inputLength) {\n            const view = inputBuffer instanceof ArrayBuffer ? new Uint8Array(inputBuffer) : inputBuffer;\n            if (!checkBufferParams(this, view, inputOffset, inputLength)) {\n                return -1;\n            }\n            const endLength = inputOffset + inputLength;\n            this.valueHexView = view.subarray(inputOffset, endLength);\n            if (!this.valueHexView.length) {\n                this.warnings.push(\"Zero buffer length\");\n                return inputOffset;\n            }\n            this.blockLength = inputLength;\n            return endLength;\n        }\n        toBER(sizeOnly = false) {\n            if (!this.isHexOnly) {\n                this.error = \"Flag 'isHexOnly' is not set, abort\";\n                return EMPTY_BUFFER;\n            }\n            if (sizeOnly) {\n                return new ArrayBuffer(this.valueHexView.byteLength);\n            }\n            return this.valueHexView.byteLength === this.valueHexView.buffer.byteLength ? this.valueHexView.buffer : this.valueHexView.slice().buffer;\n        }\n        toJSON() {\n            return {\n                ...super.toJSON(),\n                isHexOnly: this.isHexOnly,\n                valueHex: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueHexView)\n            };\n        }\n    }, _a.NAME = \"hexBlock\", _a;\n}\nclass LocalBaseBlock {\n    constructor({ blockLength = 0, error = EMPTY_STRING, warnings = [], valueBeforeDecode = EMPTY_VIEW } = {}){\n        this.blockLength = blockLength;\n        this.error = error;\n        this.warnings = warnings;\n        this.valueBeforeDecodeView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(valueBeforeDecode);\n    }\n    static blockName() {\n        return this.NAME;\n    }\n    get valueBeforeDecode() {\n        return this.valueBeforeDecodeView.slice().buffer;\n    }\n    set valueBeforeDecode(value) {\n        this.valueBeforeDecodeView = new Uint8Array(value);\n    }\n    toJSON() {\n        return {\n            blockName: this.constructor.NAME,\n            blockLength: this.blockLength,\n            error: this.error,\n            warnings: this.warnings,\n            valueBeforeDecode: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueBeforeDecodeView)\n        };\n    }\n}\nLocalBaseBlock.NAME = \"baseBlock\";\nclass ValueBlock extends LocalBaseBlock {\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        throw TypeError(\"User need to make a specific function in a class which extends 'ValueBlock'\");\n    }\n    toBER(sizeOnly, writer) {\n        throw TypeError(\"User need to make a specific function in a class which extends 'ValueBlock'\");\n    }\n}\nValueBlock.NAME = \"valueBlock\";\nclass LocalIdentificationBlock extends HexBlock(LocalBaseBlock) {\n    constructor({ idBlock = {} } = {}){\n        var _a, _b, _c, _d;\n        super();\n        if (idBlock) {\n            this.isHexOnly = (_a = idBlock.isHexOnly) !== null && _a !== void 0 ? _a : false;\n            this.valueHexView = idBlock.valueHex ? pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(idBlock.valueHex) : EMPTY_VIEW;\n            this.tagClass = (_b = idBlock.tagClass) !== null && _b !== void 0 ? _b : -1;\n            this.tagNumber = (_c = idBlock.tagNumber) !== null && _c !== void 0 ? _c : -1;\n            this.isConstructed = (_d = idBlock.isConstructed) !== null && _d !== void 0 ? _d : false;\n        } else {\n            this.tagClass = -1;\n            this.tagNumber = -1;\n            this.isConstructed = false;\n        }\n    }\n    toBER(sizeOnly = false) {\n        let firstOctet = 0;\n        switch(this.tagClass){\n            case 1:\n                firstOctet |= 0x00;\n                break;\n            case 2:\n                firstOctet |= 0x40;\n                break;\n            case 3:\n                firstOctet |= 0x80;\n                break;\n            case 4:\n                firstOctet |= 0xC0;\n                break;\n            default:\n                this.error = \"Unknown tag class\";\n                return EMPTY_BUFFER;\n        }\n        if (this.isConstructed) firstOctet |= 0x20;\n        if (this.tagNumber < 31 && !this.isHexOnly) {\n            const retView = new Uint8Array(1);\n            if (!sizeOnly) {\n                let number = this.tagNumber;\n                number &= 0x1F;\n                firstOctet |= number;\n                retView[0] = firstOctet;\n            }\n            return retView.buffer;\n        }\n        if (!this.isHexOnly) {\n            const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.tagNumber, 7);\n            const encodedView = new Uint8Array(encodedBuf);\n            const size = encodedBuf.byteLength;\n            const retView = new Uint8Array(size + 1);\n            retView[0] = firstOctet | 0x1F;\n            if (!sizeOnly) {\n                for(let i = 0; i < size - 1; i++)retView[i + 1] = encodedView[i] | 0x80;\n                retView[size] = encodedView[size - 1];\n            }\n            return retView.buffer;\n        }\n        const retView = new Uint8Array(this.valueHexView.byteLength + 1);\n        retView[0] = firstOctet | 0x1F;\n        if (!sizeOnly) {\n            const curView = this.valueHexView;\n            for(let i = 0; i < curView.length - 1; i++)retView[i + 1] = curView[i] | 0x80;\n            retView[this.valueHexView.byteLength] = curView[curView.length - 1];\n        }\n        return retView.buffer;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        if (intBuffer.length === 0) {\n            this.error = \"Zero buffer length\";\n            return -1;\n        }\n        const tagClassMask = intBuffer[0] & 0xC0;\n        switch(tagClassMask){\n            case 0x00:\n                this.tagClass = 1;\n                break;\n            case 0x40:\n                this.tagClass = 2;\n                break;\n            case 0x80:\n                this.tagClass = 3;\n                break;\n            case 0xC0:\n                this.tagClass = 4;\n                break;\n            default:\n                this.error = \"Unknown tag class\";\n                return -1;\n        }\n        this.isConstructed = (intBuffer[0] & 0x20) === 0x20;\n        this.isHexOnly = false;\n        const tagNumberMask = intBuffer[0] & 0x1F;\n        if (tagNumberMask !== 0x1F) {\n            this.tagNumber = tagNumberMask;\n            this.blockLength = 1;\n        } else {\n            let count = 1;\n            let intTagNumberBuffer = this.valueHexView = new Uint8Array(255);\n            let tagNumberBufferMaxLength = 255;\n            while(intBuffer[count] & 0x80){\n                intTagNumberBuffer[count - 1] = intBuffer[count] & 0x7F;\n                count++;\n                if (count >= intBuffer.length) {\n                    this.error = \"End of input reached before message was fully decoded\";\n                    return -1;\n                }\n                if (count === tagNumberBufferMaxLength) {\n                    tagNumberBufferMaxLength += 255;\n                    const tempBufferView = new Uint8Array(tagNumberBufferMaxLength);\n                    for(let i = 0; i < intTagNumberBuffer.length; i++)tempBufferView[i] = intTagNumberBuffer[i];\n                    intTagNumberBuffer = this.valueHexView = new Uint8Array(tagNumberBufferMaxLength);\n                }\n            }\n            this.blockLength = count + 1;\n            intTagNumberBuffer[count - 1] = intBuffer[count] & 0x7F;\n            const tempBufferView = new Uint8Array(count);\n            for(let i = 0; i < count; i++)tempBufferView[i] = intTagNumberBuffer[i];\n            intTagNumberBuffer = this.valueHexView = new Uint8Array(count);\n            intTagNumberBuffer.set(tempBufferView);\n            if (this.blockLength <= 9) this.tagNumber = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(intTagNumberBuffer, 7);\n            else {\n                this.isHexOnly = true;\n                this.warnings.push(\"Tag too long, represented as hex-coded\");\n            }\n        }\n        if (this.tagClass === 1 && this.isConstructed) {\n            switch(this.tagNumber){\n                case 1:\n                case 2:\n                case 5:\n                case 6:\n                case 9:\n                case 13:\n                case 14:\n                case 23:\n                case 24:\n                case 31:\n                case 32:\n                case 33:\n                case 34:\n                    this.error = \"Constructed encoding used for primitive type\";\n                    return -1;\n            }\n        }\n        return inputOffset + this.blockLength;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            tagClass: this.tagClass,\n            tagNumber: this.tagNumber,\n            isConstructed: this.isConstructed\n        };\n    }\n}\nLocalIdentificationBlock.NAME = \"identificationBlock\";\nclass LocalLengthBlock extends LocalBaseBlock {\n    constructor({ lenBlock = {} } = {}){\n        var _a, _b, _c;\n        super();\n        this.isIndefiniteForm = (_a = lenBlock.isIndefiniteForm) !== null && _a !== void 0 ? _a : false;\n        this.longFormUsed = (_b = lenBlock.longFormUsed) !== null && _b !== void 0 ? _b : false;\n        this.length = (_c = lenBlock.length) !== null && _c !== void 0 ? _c : 0;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, view, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = view.subarray(inputOffset, inputOffset + inputLength);\n        if (intBuffer.length === 0) {\n            this.error = \"Zero buffer length\";\n            return -1;\n        }\n        if (intBuffer[0] === 0xFF) {\n            this.error = \"Length block 0xFF is reserved by standard\";\n            return -1;\n        }\n        this.isIndefiniteForm = intBuffer[0] === 0x80;\n        if (this.isIndefiniteForm) {\n            this.blockLength = 1;\n            return inputOffset + this.blockLength;\n        }\n        this.longFormUsed = !!(intBuffer[0] & 0x80);\n        if (this.longFormUsed === false) {\n            this.length = intBuffer[0];\n            this.blockLength = 1;\n            return inputOffset + this.blockLength;\n        }\n        const count = intBuffer[0] & 0x7F;\n        if (count > 8) {\n            this.error = \"Too big integer\";\n            return -1;\n        }\n        if (count + 1 > intBuffer.length) {\n            this.error = \"End of input reached before message was fully decoded\";\n            return -1;\n        }\n        const lenOffset = inputOffset + 1;\n        const lengthBufferView = view.subarray(lenOffset, lenOffset + count);\n        if (lengthBufferView[count - 1] === 0x00) this.warnings.push(\"Needlessly long encoded length\");\n        this.length = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(lengthBufferView, 8);\n        if (this.longFormUsed && this.length <= 127) this.warnings.push(\"Unnecessary usage of long length form\");\n        this.blockLength = count + 1;\n        return inputOffset + this.blockLength;\n    }\n    toBER(sizeOnly = false) {\n        let retBuf;\n        let retView;\n        if (this.length > 127) this.longFormUsed = true;\n        if (this.isIndefiniteForm) {\n            retBuf = new ArrayBuffer(1);\n            if (sizeOnly === false) {\n                retView = new Uint8Array(retBuf);\n                retView[0] = 0x80;\n            }\n            return retBuf;\n        }\n        if (this.longFormUsed) {\n            const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.length, 8);\n            if (encodedBuf.byteLength > 127) {\n                this.error = \"Too big length\";\n                return EMPTY_BUFFER;\n            }\n            retBuf = new ArrayBuffer(encodedBuf.byteLength + 1);\n            if (sizeOnly) return retBuf;\n            const encodedView = new Uint8Array(encodedBuf);\n            retView = new Uint8Array(retBuf);\n            retView[0] = encodedBuf.byteLength | 0x80;\n            for(let i = 0; i < encodedBuf.byteLength; i++)retView[i + 1] = encodedView[i];\n            return retBuf;\n        }\n        retBuf = new ArrayBuffer(1);\n        if (sizeOnly === false) {\n            retView = new Uint8Array(retBuf);\n            retView[0] = this.length;\n        }\n        return retBuf;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            isIndefiniteForm: this.isIndefiniteForm,\n            longFormUsed: this.longFormUsed,\n            length: this.length\n        };\n    }\n}\nLocalLengthBlock.NAME = \"lengthBlock\";\nconst typeStore = {};\nclass BaseBlock extends LocalBaseBlock {\n    constructor({ name = EMPTY_STRING, optional = false, primitiveSchema, ...parameters } = {}, valueBlockType){\n        super(parameters);\n        this.name = name;\n        this.optional = optional;\n        if (primitiveSchema) {\n            this.primitiveSchema = primitiveSchema;\n        }\n        this.idBlock = new LocalIdentificationBlock(parameters);\n        this.lenBlock = new LocalLengthBlock(parameters);\n        this.valueBlock = valueBlockType ? new valueBlockType(parameters) : new ValueBlock(parameters);\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const resultOffset = this.valueBlock.fromBER(inputBuffer, inputOffset, this.lenBlock.isIndefiniteForm ? inputLength : this.lenBlock.length);\n        if (resultOffset === -1) {\n            this.error = this.valueBlock.error;\n            return resultOffset;\n        }\n        if (!this.idBlock.error.length) this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length) this.blockLength += this.lenBlock.blockLength;\n        if (!this.valueBlock.error.length) this.blockLength += this.valueBlock.blockLength;\n        return resultOffset;\n    }\n    toBER(sizeOnly, writer) {\n        const _writer = writer || new ViewWriter();\n        if (!writer) {\n            prepareIndefiniteForm(this);\n        }\n        const idBlockBuf = this.idBlock.toBER(sizeOnly);\n        _writer.write(idBlockBuf);\n        if (this.lenBlock.isIndefiniteForm) {\n            _writer.write(new Uint8Array([\n                0x80\n            ]).buffer);\n            this.valueBlock.toBER(sizeOnly, _writer);\n            _writer.write(new ArrayBuffer(2));\n        } else {\n            const valueBlockBuf = this.valueBlock.toBER(sizeOnly);\n            this.lenBlock.length = valueBlockBuf.byteLength;\n            const lenBlockBuf = this.lenBlock.toBER(sizeOnly);\n            _writer.write(lenBlockBuf);\n            _writer.write(valueBlockBuf);\n        }\n        if (!writer) {\n            return _writer.final();\n        }\n        return EMPTY_BUFFER;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            idBlock: this.idBlock.toJSON(),\n            lenBlock: this.lenBlock.toJSON(),\n            valueBlock: this.valueBlock.toJSON(),\n            name: this.name,\n            optional: this.optional\n        };\n        if (this.primitiveSchema) object.primitiveSchema = this.primitiveSchema.toJSON();\n        return object;\n    }\n    toString(encoding = \"ascii\") {\n        if (encoding === \"ascii\") {\n            return this.onAsciiEncoding();\n        }\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.toBER());\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueBlock.valueBeforeDecodeView)}`;\n    }\n    isEqual(other) {\n        if (this === other) {\n            return true;\n        }\n        if (!(other instanceof this.constructor)) {\n            return false;\n        }\n        const thisRaw = this.toBER();\n        const otherRaw = other.toBER();\n        return pvutils__WEBPACK_IMPORTED_MODULE_1__.isEqualBuffer(thisRaw, otherRaw);\n    }\n}\nBaseBlock.NAME = \"BaseBlock\";\nfunction prepareIndefiniteForm(baseBlock) {\n    if (baseBlock instanceof typeStore.Constructed) {\n        for (const value of baseBlock.valueBlock.value){\n            if (prepareIndefiniteForm(value)) {\n                baseBlock.lenBlock.isIndefiniteForm = true;\n            }\n        }\n    }\n    return !!baseBlock.lenBlock.isIndefiniteForm;\n}\nclass BaseStringBlock extends BaseBlock {\n    constructor({ value = EMPTY_STRING, ...parameters } = {}, stringValueBlockType){\n        super(parameters, stringValueBlockType);\n        if (value) {\n            this.fromString(value);\n        }\n    }\n    getValue() {\n        return this.valueBlock.value;\n    }\n    setValue(value) {\n        this.valueBlock.value = value;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const resultOffset = this.valueBlock.fromBER(inputBuffer, inputOffset, this.lenBlock.isIndefiniteForm ? inputLength : this.lenBlock.length);\n        if (resultOffset === -1) {\n            this.error = this.valueBlock.error;\n            return resultOffset;\n        }\n        this.fromBuffer(this.valueBlock.valueHexView);\n        if (!this.idBlock.error.length) this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length) this.blockLength += this.lenBlock.blockLength;\n        if (!this.valueBlock.error.length) this.blockLength += this.valueBlock.blockLength;\n        return resultOffset;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : '${this.valueBlock.value}'`;\n    }\n}\nBaseStringBlock.NAME = \"BaseStringBlock\";\nclass LocalPrimitiveValueBlock extends HexBlock(ValueBlock) {\n    constructor({ isHexOnly = true, ...parameters } = {}){\n        super(parameters);\n        this.isHexOnly = isHexOnly;\n    }\n}\nLocalPrimitiveValueBlock.NAME = \"PrimitiveValueBlock\";\nvar _a$w;\nclass Primitive extends BaseBlock {\n    constructor(parameters = {}){\n        super(parameters, LocalPrimitiveValueBlock);\n        this.idBlock.isConstructed = false;\n    }\n}\n_a$w = Primitive;\n(()=>{\n    typeStore.Primitive = _a$w;\n})();\nPrimitive.NAME = \"PRIMITIVE\";\nfunction localChangeType(inputObject, newType) {\n    if (inputObject instanceof newType) {\n        return inputObject;\n    }\n    const newObject = new newType();\n    newObject.idBlock = inputObject.idBlock;\n    newObject.lenBlock = inputObject.lenBlock;\n    newObject.warnings = inputObject.warnings;\n    newObject.valueBeforeDecodeView = inputObject.valueBeforeDecodeView;\n    return newObject;\n}\nfunction localFromBER(inputBuffer, inputOffset = 0, inputLength = inputBuffer.length) {\n    const incomingOffset = inputOffset;\n    let returnObject = new BaseBlock({}, ValueBlock);\n    const baseBlock = new LocalBaseBlock();\n    if (!checkBufferParams(baseBlock, inputBuffer, inputOffset, inputLength)) {\n        returnObject.error = baseBlock.error;\n        return {\n            offset: -1,\n            result: returnObject\n        };\n    }\n    const intBuffer = inputBuffer.subarray(inputOffset, inputOffset + inputLength);\n    if (!intBuffer.length) {\n        returnObject.error = \"Zero buffer length\";\n        return {\n            offset: -1,\n            result: returnObject\n        };\n    }\n    let resultOffset = returnObject.idBlock.fromBER(inputBuffer, inputOffset, inputLength);\n    if (returnObject.idBlock.warnings.length) {\n        returnObject.warnings.concat(returnObject.idBlock.warnings);\n    }\n    if (resultOffset === -1) {\n        returnObject.error = returnObject.idBlock.error;\n        return {\n            offset: -1,\n            result: returnObject\n        };\n    }\n    inputOffset = resultOffset;\n    inputLength -= returnObject.idBlock.blockLength;\n    resultOffset = returnObject.lenBlock.fromBER(inputBuffer, inputOffset, inputLength);\n    if (returnObject.lenBlock.warnings.length) {\n        returnObject.warnings.concat(returnObject.lenBlock.warnings);\n    }\n    if (resultOffset === -1) {\n        returnObject.error = returnObject.lenBlock.error;\n        return {\n            offset: -1,\n            result: returnObject\n        };\n    }\n    inputOffset = resultOffset;\n    inputLength -= returnObject.lenBlock.blockLength;\n    if (!returnObject.idBlock.isConstructed && returnObject.lenBlock.isIndefiniteForm) {\n        returnObject.error = \"Indefinite length form used for primitive encoding form\";\n        return {\n            offset: -1,\n            result: returnObject\n        };\n    }\n    let newASN1Type = BaseBlock;\n    switch(returnObject.idBlock.tagClass){\n        case 1:\n            if (returnObject.idBlock.tagNumber >= 37 && returnObject.idBlock.isHexOnly === false) {\n                returnObject.error = \"UNIVERSAL 37 and upper tags are reserved by ASN.1 standard\";\n                return {\n                    offset: -1,\n                    result: returnObject\n                };\n            }\n            switch(returnObject.idBlock.tagNumber){\n                case 0:\n                    if (returnObject.idBlock.isConstructed && returnObject.lenBlock.length > 0) {\n                        returnObject.error = \"Type [UNIVERSAL 0] is reserved\";\n                        return {\n                            offset: -1,\n                            result: returnObject\n                        };\n                    }\n                    newASN1Type = typeStore.EndOfContent;\n                    break;\n                case 1:\n                    newASN1Type = typeStore.Boolean;\n                    break;\n                case 2:\n                    newASN1Type = typeStore.Integer;\n                    break;\n                case 3:\n                    newASN1Type = typeStore.BitString;\n                    break;\n                case 4:\n                    newASN1Type = typeStore.OctetString;\n                    break;\n                case 5:\n                    newASN1Type = typeStore.Null;\n                    break;\n                case 6:\n                    newASN1Type = typeStore.ObjectIdentifier;\n                    break;\n                case 10:\n                    newASN1Type = typeStore.Enumerated;\n                    break;\n                case 12:\n                    newASN1Type = typeStore.Utf8String;\n                    break;\n                case 13:\n                    newASN1Type = typeStore.RelativeObjectIdentifier;\n                    break;\n                case 14:\n                    newASN1Type = typeStore.TIME;\n                    break;\n                case 15:\n                    returnObject.error = \"[UNIVERSAL 15] is reserved by ASN.1 standard\";\n                    return {\n                        offset: -1,\n                        result: returnObject\n                    };\n                case 16:\n                    newASN1Type = typeStore.Sequence;\n                    break;\n                case 17:\n                    newASN1Type = typeStore.Set;\n                    break;\n                case 18:\n                    newASN1Type = typeStore.NumericString;\n                    break;\n                case 19:\n                    newASN1Type = typeStore.PrintableString;\n                    break;\n                case 20:\n                    newASN1Type = typeStore.TeletexString;\n                    break;\n                case 21:\n                    newASN1Type = typeStore.VideotexString;\n                    break;\n                case 22:\n                    newASN1Type = typeStore.IA5String;\n                    break;\n                case 23:\n                    newASN1Type = typeStore.UTCTime;\n                    break;\n                case 24:\n                    newASN1Type = typeStore.GeneralizedTime;\n                    break;\n                case 25:\n                    newASN1Type = typeStore.GraphicString;\n                    break;\n                case 26:\n                    newASN1Type = typeStore.VisibleString;\n                    break;\n                case 27:\n                    newASN1Type = typeStore.GeneralString;\n                    break;\n                case 28:\n                    newASN1Type = typeStore.UniversalString;\n                    break;\n                case 29:\n                    newASN1Type = typeStore.CharacterString;\n                    break;\n                case 30:\n                    newASN1Type = typeStore.BmpString;\n                    break;\n                case 31:\n                    newASN1Type = typeStore.DATE;\n                    break;\n                case 32:\n                    newASN1Type = typeStore.TimeOfDay;\n                    break;\n                case 33:\n                    newASN1Type = typeStore.DateTime;\n                    break;\n                case 34:\n                    newASN1Type = typeStore.Duration;\n                    break;\n                default:\n                    {\n                        const newObject = returnObject.idBlock.isConstructed ? new typeStore.Constructed() : new typeStore.Primitive();\n                        newObject.idBlock = returnObject.idBlock;\n                        newObject.lenBlock = returnObject.lenBlock;\n                        newObject.warnings = returnObject.warnings;\n                        returnObject = newObject;\n                    }\n            }\n            break;\n        case 2:\n        case 3:\n        case 4:\n        default:\n            {\n                newASN1Type = returnObject.idBlock.isConstructed ? typeStore.Constructed : typeStore.Primitive;\n            }\n    }\n    returnObject = localChangeType(returnObject, newASN1Type);\n    resultOffset = returnObject.fromBER(inputBuffer, inputOffset, returnObject.lenBlock.isIndefiniteForm ? inputLength : returnObject.lenBlock.length);\n    returnObject.valueBeforeDecodeView = inputBuffer.subarray(incomingOffset, incomingOffset + returnObject.blockLength);\n    return {\n        offset: resultOffset,\n        result: returnObject\n    };\n}\nfunction fromBER(inputBuffer) {\n    if (!inputBuffer.byteLength) {\n        const result = new BaseBlock({}, ValueBlock);\n        result.error = \"Input buffer has zero length\";\n        return {\n            offset: -1,\n            result\n        };\n    }\n    return localFromBER(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer).slice(), 0, inputBuffer.byteLength);\n}\nfunction checkLen(indefiniteLength, length) {\n    if (indefiniteLength) {\n        return 1;\n    }\n    return length;\n}\nclass LocalConstructedValueBlock extends ValueBlock {\n    constructor({ value = [], isIndefiniteForm = false, ...parameters } = {}){\n        super(parameters);\n        this.value = value;\n        this.isIndefiniteForm = isIndefiniteForm;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, view, inputOffset, inputLength)) {\n            return -1;\n        }\n        this.valueBeforeDecodeView = view.subarray(inputOffset, inputOffset + inputLength);\n        if (this.valueBeforeDecodeView.length === 0) {\n            this.warnings.push(\"Zero buffer length\");\n            return inputOffset;\n        }\n        let currentOffset = inputOffset;\n        while(checkLen(this.isIndefiniteForm, inputLength) > 0){\n            const returnObject = localFromBER(view, currentOffset, inputLength);\n            if (returnObject.offset === -1) {\n                this.error = returnObject.result.error;\n                this.warnings.concat(returnObject.result.warnings);\n                return -1;\n            }\n            currentOffset = returnObject.offset;\n            this.blockLength += returnObject.result.blockLength;\n            inputLength -= returnObject.result.blockLength;\n            this.value.push(returnObject.result);\n            if (this.isIndefiniteForm && returnObject.result.constructor.NAME === END_OF_CONTENT_NAME) {\n                break;\n            }\n        }\n        if (this.isIndefiniteForm) {\n            if (this.value[this.value.length - 1].constructor.NAME === END_OF_CONTENT_NAME) {\n                this.value.pop();\n            } else {\n                this.warnings.push(\"No EndOfContent block encoded\");\n            }\n        }\n        return currentOffset;\n    }\n    toBER(sizeOnly, writer) {\n        const _writer = writer || new ViewWriter();\n        for(let i = 0; i < this.value.length; i++){\n            this.value[i].toBER(sizeOnly, _writer);\n        }\n        if (!writer) {\n            return _writer.final();\n        }\n        return EMPTY_BUFFER;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            isIndefiniteForm: this.isIndefiniteForm,\n            value: []\n        };\n        for (const value of this.value){\n            object.value.push(value.toJSON());\n        }\n        return object;\n    }\n}\nLocalConstructedValueBlock.NAME = \"ConstructedValueBlock\";\nvar _a$v;\nclass Constructed extends BaseBlock {\n    constructor(parameters = {}){\n        super(parameters, LocalConstructedValueBlock);\n        this.idBlock.isConstructed = true;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        this.valueBlock.isIndefiniteForm = this.lenBlock.isIndefiniteForm;\n        const resultOffset = this.valueBlock.fromBER(inputBuffer, inputOffset, this.lenBlock.isIndefiniteForm ? inputLength : this.lenBlock.length);\n        if (resultOffset === -1) {\n            this.error = this.valueBlock.error;\n            return resultOffset;\n        }\n        if (!this.idBlock.error.length) this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length) this.blockLength += this.lenBlock.blockLength;\n        if (!this.valueBlock.error.length) this.blockLength += this.valueBlock.blockLength;\n        return resultOffset;\n    }\n    onAsciiEncoding() {\n        const values = [];\n        for (const value of this.valueBlock.value){\n            values.push(value.toString(\"ascii\").split(\"\\n\").map((o)=>`  ${o}`).join(\"\\n\"));\n        }\n        const blockName = this.idBlock.tagClass === 3 ? `[${this.idBlock.tagNumber}]` : this.constructor.NAME;\n        return values.length ? `${blockName} :\\n${values.join(\"\\n\")}` : `${blockName} :`;\n    }\n}\n_a$v = Constructed;\n(()=>{\n    typeStore.Constructed = _a$v;\n})();\nConstructed.NAME = \"CONSTRUCTED\";\nclass LocalEndOfContentValueBlock extends ValueBlock {\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        return inputOffset;\n    }\n    toBER(sizeOnly) {\n        return EMPTY_BUFFER;\n    }\n}\nLocalEndOfContentValueBlock.override = \"EndOfContentValueBlock\";\nvar _a$u;\nclass EndOfContent extends BaseBlock {\n    constructor(parameters = {}){\n        super(parameters, LocalEndOfContentValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 0;\n    }\n}\n_a$u = EndOfContent;\n(()=>{\n    typeStore.EndOfContent = _a$u;\n})();\nEndOfContent.NAME = END_OF_CONTENT_NAME;\nvar _a$t;\nclass Null extends BaseBlock {\n    constructor(parameters = {}){\n        super(parameters, ValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 5;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (this.lenBlock.length > 0) this.warnings.push(\"Non-zero length of value block for Null type\");\n        if (!this.idBlock.error.length) this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length) this.blockLength += this.lenBlock.blockLength;\n        this.blockLength += inputLength;\n        if (inputOffset + inputLength > inputBuffer.byteLength) {\n            this.error = \"End of input reached before message was fully decoded (inconsistent offset and length values)\";\n            return -1;\n        }\n        return inputOffset + inputLength;\n    }\n    toBER(sizeOnly, writer) {\n        const retBuf = new ArrayBuffer(2);\n        if (!sizeOnly) {\n            const retView = new Uint8Array(retBuf);\n            retView[0] = 0x05;\n            retView[1] = 0x00;\n        }\n        if (writer) {\n            writer.write(retBuf);\n        }\n        return retBuf;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME}`;\n    }\n}\n_a$t = Null;\n(()=>{\n    typeStore.Null = _a$t;\n})();\nNull.NAME = \"NULL\";\nclass LocalBooleanValueBlock extends HexBlock(ValueBlock) {\n    constructor({ value, ...parameters } = {}){\n        super(parameters);\n        if (parameters.valueHex) {\n            this.valueHexView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(parameters.valueHex);\n        } else {\n            this.valueHexView = new Uint8Array(1);\n        }\n        if (value) {\n            this.value = value;\n        }\n    }\n    get value() {\n        for (const octet of this.valueHexView){\n            if (octet > 0) {\n                return true;\n            }\n        }\n        return false;\n    }\n    set value(value) {\n        this.valueHexView[0] = value ? 0xFF : 0x00;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        this.valueHexView = inputView.subarray(inputOffset, inputOffset + inputLength);\n        if (inputLength > 1) this.warnings.push(\"Boolean value encoded in more then 1 octet\");\n        this.isHexOnly = true;\n        pvutils__WEBPACK_IMPORTED_MODULE_1__.utilDecodeTC.call(this);\n        this.blockLength = inputLength;\n        return inputOffset + inputLength;\n    }\n    toBER() {\n        return this.valueHexView.slice();\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.value\n        };\n    }\n}\nLocalBooleanValueBlock.NAME = \"BooleanValueBlock\";\nvar _a$s;\nclass Boolean extends BaseBlock {\n    constructor(parameters = {}){\n        super(parameters, LocalBooleanValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 1;\n    }\n    getValue() {\n        return this.valueBlock.value;\n    }\n    setValue(value) {\n        this.valueBlock.value = value;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.getValue}`;\n    }\n}\n_a$s = Boolean;\n(()=>{\n    typeStore.Boolean = _a$s;\n})();\nBoolean.NAME = \"BOOLEAN\";\nclass LocalOctetStringValueBlock extends HexBlock(LocalConstructedValueBlock) {\n    constructor({ isConstructed = false, ...parameters } = {}){\n        super(parameters);\n        this.isConstructed = isConstructed;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        let resultOffset = 0;\n        if (this.isConstructed) {\n            this.isHexOnly = false;\n            resultOffset = LocalConstructedValueBlock.prototype.fromBER.call(this, inputBuffer, inputOffset, inputLength);\n            if (resultOffset === -1) return resultOffset;\n            for(let i = 0; i < this.value.length; i++){\n                const currentBlockName = this.value[i].constructor.NAME;\n                if (currentBlockName === END_OF_CONTENT_NAME) {\n                    if (this.isIndefiniteForm) break;\n                    else {\n                        this.error = \"EndOfContent is unexpected, OCTET STRING may consists of OCTET STRINGs only\";\n                        return -1;\n                    }\n                }\n                if (currentBlockName !== OCTET_STRING_NAME) {\n                    this.error = \"OCTET STRING may consists of OCTET STRINGs only\";\n                    return -1;\n                }\n            }\n        } else {\n            this.isHexOnly = true;\n            resultOffset = super.fromBER(inputBuffer, inputOffset, inputLength);\n            this.blockLength = inputLength;\n        }\n        return resultOffset;\n    }\n    toBER(sizeOnly, writer) {\n        if (this.isConstructed) return LocalConstructedValueBlock.prototype.toBER.call(this, sizeOnly, writer);\n        return sizeOnly ? new ArrayBuffer(this.valueHexView.byteLength) : this.valueHexView.slice().buffer;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            isConstructed: this.isConstructed\n        };\n    }\n}\nLocalOctetStringValueBlock.NAME = \"OctetStringValueBlock\";\nvar _a$r;\nclass OctetString extends BaseBlock {\n    constructor({ idBlock = {}, lenBlock = {}, ...parameters } = {}){\n        var _b, _c;\n        (_b = parameters.isConstructed) !== null && _b !== void 0 ? _b : parameters.isConstructed = !!((_c = parameters.value) === null || _c === void 0 ? void 0 : _c.length);\n        super({\n            idBlock: {\n                isConstructed: parameters.isConstructed,\n                ...idBlock\n            },\n            lenBlock: {\n                ...lenBlock,\n                isIndefiniteForm: !!parameters.isIndefiniteForm\n            },\n            ...parameters\n        }, LocalOctetStringValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 4;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        this.valueBlock.isConstructed = this.idBlock.isConstructed;\n        this.valueBlock.isIndefiniteForm = this.lenBlock.isIndefiniteForm;\n        if (inputLength === 0) {\n            if (this.idBlock.error.length === 0) this.blockLength += this.idBlock.blockLength;\n            if (this.lenBlock.error.length === 0) this.blockLength += this.lenBlock.blockLength;\n            return inputOffset;\n        }\n        if (!this.valueBlock.isConstructed) {\n            const view = inputBuffer instanceof ArrayBuffer ? new Uint8Array(inputBuffer) : inputBuffer;\n            const buf = view.subarray(inputOffset, inputOffset + inputLength);\n            try {\n                if (buf.byteLength) {\n                    const asn = localFromBER(buf, 0, buf.byteLength);\n                    if (asn.offset !== -1 && asn.offset === inputLength) {\n                        this.valueBlock.value = [\n                            asn.result\n                        ];\n                    }\n                }\n            } catch (e) {}\n        }\n        return super.fromBER(inputBuffer, inputOffset, inputLength);\n    }\n    onAsciiEncoding() {\n        if (this.valueBlock.isConstructed || this.valueBlock.value && this.valueBlock.value.length) {\n            return Constructed.prototype.onAsciiEncoding.call(this);\n        }\n        return `${this.constructor.NAME} : ${pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueBlock.valueHexView)}`;\n    }\n    getValue() {\n        if (!this.idBlock.isConstructed) {\n            return this.valueBlock.valueHexView.slice().buffer;\n        }\n        const array = [];\n        for (const content of this.valueBlock.value){\n            if (content instanceof OctetString) {\n                array.push(content.valueBlock.valueHexView);\n            }\n        }\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.concat(array);\n    }\n}\n_a$r = OctetString;\n(()=>{\n    typeStore.OctetString = _a$r;\n})();\nOctetString.NAME = OCTET_STRING_NAME;\nclass LocalBitStringValueBlock extends HexBlock(LocalConstructedValueBlock) {\n    constructor({ unusedBits = 0, isConstructed = false, ...parameters } = {}){\n        super(parameters);\n        this.unusedBits = unusedBits;\n        this.isConstructed = isConstructed;\n        this.blockLength = this.valueHexView.byteLength;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (!inputLength) {\n            return inputOffset;\n        }\n        let resultOffset = -1;\n        if (this.isConstructed) {\n            resultOffset = LocalConstructedValueBlock.prototype.fromBER.call(this, inputBuffer, inputOffset, inputLength);\n            if (resultOffset === -1) return resultOffset;\n            for (const value of this.value){\n                const currentBlockName = value.constructor.NAME;\n                if (currentBlockName === END_OF_CONTENT_NAME) {\n                    if (this.isIndefiniteForm) break;\n                    else {\n                        this.error = \"EndOfContent is unexpected, BIT STRING may consists of BIT STRINGs only\";\n                        return -1;\n                    }\n                }\n                if (currentBlockName !== BIT_STRING_NAME) {\n                    this.error = \"BIT STRING may consists of BIT STRINGs only\";\n                    return -1;\n                }\n                const valueBlock = value.valueBlock;\n                if (this.unusedBits > 0 && valueBlock.unusedBits > 0) {\n                    this.error = 'Using of \"unused bits\" inside constructive BIT STRING allowed for least one only';\n                    return -1;\n                }\n                this.unusedBits = valueBlock.unusedBits;\n            }\n            return resultOffset;\n        }\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        this.unusedBits = intBuffer[0];\n        if (this.unusedBits > 7) {\n            this.error = \"Unused bits for BitString must be in range 0-7\";\n            return -1;\n        }\n        if (!this.unusedBits) {\n            const buf = intBuffer.subarray(1);\n            try {\n                if (buf.byteLength) {\n                    const asn = localFromBER(buf, 0, buf.byteLength);\n                    if (asn.offset !== -1 && asn.offset === inputLength - 1) {\n                        this.value = [\n                            asn.result\n                        ];\n                    }\n                }\n            } catch (e) {}\n        }\n        this.valueHexView = intBuffer.subarray(1);\n        this.blockLength = intBuffer.length;\n        return inputOffset + inputLength;\n    }\n    toBER(sizeOnly, writer) {\n        if (this.isConstructed) {\n            return LocalConstructedValueBlock.prototype.toBER.call(this, sizeOnly, writer);\n        }\n        if (sizeOnly) {\n            return new ArrayBuffer(this.valueHexView.byteLength + 1);\n        }\n        if (!this.valueHexView.byteLength) {\n            return EMPTY_BUFFER;\n        }\n        const retView = new Uint8Array(this.valueHexView.length + 1);\n        retView[0] = this.unusedBits;\n        retView.set(this.valueHexView, 1);\n        return retView.buffer;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            unusedBits: this.unusedBits,\n            isConstructed: this.isConstructed\n        };\n    }\n}\nLocalBitStringValueBlock.NAME = \"BitStringValueBlock\";\nvar _a$q;\nclass BitString extends BaseBlock {\n    constructor({ idBlock = {}, lenBlock = {}, ...parameters } = {}){\n        var _b, _c;\n        (_b = parameters.isConstructed) !== null && _b !== void 0 ? _b : parameters.isConstructed = !!((_c = parameters.value) === null || _c === void 0 ? void 0 : _c.length);\n        super({\n            idBlock: {\n                isConstructed: parameters.isConstructed,\n                ...idBlock\n            },\n            lenBlock: {\n                ...lenBlock,\n                isIndefiniteForm: !!parameters.isIndefiniteForm\n            },\n            ...parameters\n        }, LocalBitStringValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 3;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        this.valueBlock.isConstructed = this.idBlock.isConstructed;\n        this.valueBlock.isIndefiniteForm = this.lenBlock.isIndefiniteForm;\n        return super.fromBER(inputBuffer, inputOffset, inputLength);\n    }\n    onAsciiEncoding() {\n        if (this.valueBlock.isConstructed || this.valueBlock.value && this.valueBlock.value.length) {\n            return Constructed.prototype.onAsciiEncoding.call(this);\n        } else {\n            const bits = [];\n            const valueHex = this.valueBlock.valueHexView;\n            for (const byte of valueHex){\n                bits.push(byte.toString(2).padStart(8, \"0\"));\n            }\n            const bitsStr = bits.join(\"\");\n            return `${this.constructor.NAME} : ${bitsStr.substring(0, bitsStr.length - this.valueBlock.unusedBits)}`;\n        }\n    }\n}\n_a$q = BitString;\n(()=>{\n    typeStore.BitString = _a$q;\n})();\nBitString.NAME = BIT_STRING_NAME;\nvar _a$p;\nfunction viewAdd(first, second) {\n    const c = new Uint8Array([\n        0\n    ]);\n    const firstView = new Uint8Array(first);\n    const secondView = new Uint8Array(second);\n    let firstViewCopy = firstView.slice(0);\n    const firstViewCopyLength = firstViewCopy.length - 1;\n    const secondViewCopy = secondView.slice(0);\n    const secondViewCopyLength = secondViewCopy.length - 1;\n    let value = 0;\n    const max = secondViewCopyLength < firstViewCopyLength ? firstViewCopyLength : secondViewCopyLength;\n    let counter = 0;\n    for(let i = max; i >= 0; i--, counter++){\n        switch(true){\n            case counter < secondViewCopy.length:\n                value = firstViewCopy[firstViewCopyLength - counter] + secondViewCopy[secondViewCopyLength - counter] + c[0];\n                break;\n            default:\n                value = firstViewCopy[firstViewCopyLength - counter] + c[0];\n        }\n        c[0] = value / 10;\n        switch(true){\n            case counter >= firstViewCopy.length:\n                firstViewCopy = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilConcatView(new Uint8Array([\n                    value % 10\n                ]), firstViewCopy);\n                break;\n            default:\n                firstViewCopy[firstViewCopyLength - counter] = value % 10;\n        }\n    }\n    if (c[0] > 0) firstViewCopy = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilConcatView(c, firstViewCopy);\n    return firstViewCopy;\n}\nfunction power2(n) {\n    if (n >= powers2.length) {\n        for(let p = powers2.length; p <= n; p++){\n            const c = new Uint8Array([\n                0\n            ]);\n            let digits = powers2[p - 1].slice(0);\n            for(let i = digits.length - 1; i >= 0; i--){\n                const newValue = new Uint8Array([\n                    (digits[i] << 1) + c[0]\n                ]);\n                c[0] = newValue[0] / 10;\n                digits[i] = newValue[0] % 10;\n            }\n            if (c[0] > 0) digits = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilConcatView(c, digits);\n            powers2.push(digits);\n        }\n    }\n    return powers2[n];\n}\nfunction viewSub(first, second) {\n    let b = 0;\n    const firstView = new Uint8Array(first);\n    const secondView = new Uint8Array(second);\n    const firstViewCopy = firstView.slice(0);\n    const firstViewCopyLength = firstViewCopy.length - 1;\n    const secondViewCopy = secondView.slice(0);\n    const secondViewCopyLength = secondViewCopy.length - 1;\n    let value;\n    let counter = 0;\n    for(let i = secondViewCopyLength; i >= 0; i--, counter++){\n        value = firstViewCopy[firstViewCopyLength - counter] - secondViewCopy[secondViewCopyLength - counter] - b;\n        switch(true){\n            case value < 0:\n                b = 1;\n                firstViewCopy[firstViewCopyLength - counter] = value + 10;\n                break;\n            default:\n                b = 0;\n                firstViewCopy[firstViewCopyLength - counter] = value;\n        }\n    }\n    if (b > 0) {\n        for(let i = firstViewCopyLength - secondViewCopyLength + 1; i >= 0; i--, counter++){\n            value = firstViewCopy[firstViewCopyLength - counter] - b;\n            if (value < 0) {\n                b = 1;\n                firstViewCopy[firstViewCopyLength - counter] = value + 10;\n            } else {\n                b = 0;\n                firstViewCopy[firstViewCopyLength - counter] = value;\n                break;\n            }\n        }\n    }\n    return firstViewCopy.slice();\n}\nclass LocalIntegerValueBlock extends HexBlock(ValueBlock) {\n    constructor({ value, ...parameters } = {}){\n        super(parameters);\n        this._valueDec = 0;\n        if (parameters.valueHex) {\n            this.setValueHex();\n        }\n        if (value !== undefined) {\n            this.valueDec = value;\n        }\n    }\n    setValueHex() {\n        if (this.valueHexView.length >= 4) {\n            this.warnings.push(\"Too big Integer for decoding, hex only\");\n            this.isHexOnly = true;\n            this._valueDec = 0;\n        } else {\n            this.isHexOnly = false;\n            if (this.valueHexView.length > 0) {\n                this._valueDec = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilDecodeTC.call(this);\n            }\n        }\n    }\n    set valueDec(v) {\n        this._valueDec = v;\n        this.isHexOnly = false;\n        this.valueHexView = new Uint8Array(pvutils__WEBPACK_IMPORTED_MODULE_1__.utilEncodeTC(v));\n    }\n    get valueDec() {\n        return this._valueDec;\n    }\n    fromDER(inputBuffer, inputOffset, inputLength, expectedLength = 0) {\n        const offset = this.fromBER(inputBuffer, inputOffset, inputLength);\n        if (offset === -1) return offset;\n        const view = this.valueHexView;\n        if (view[0] === 0x00 && (view[1] & 0x80) !== 0) {\n            this.valueHexView = view.subarray(1);\n        } else {\n            if (expectedLength !== 0) {\n                if (view.length < expectedLength) {\n                    if (expectedLength - view.length > 1) expectedLength = view.length + 1;\n                    this.valueHexView = view.subarray(expectedLength - view.length);\n                }\n            }\n        }\n        return offset;\n    }\n    toDER(sizeOnly = false) {\n        const view = this.valueHexView;\n        switch(true){\n            case (view[0] & 0x80) !== 0:\n                {\n                    const updatedView = new Uint8Array(this.valueHexView.length + 1);\n                    updatedView[0] = 0x00;\n                    updatedView.set(view, 1);\n                    this.valueHexView = updatedView;\n                }\n                break;\n            case view[0] === 0x00 && (view[1] & 0x80) === 0:\n                {\n                    this.valueHexView = this.valueHexView.subarray(1);\n                }\n                break;\n        }\n        return this.toBER(sizeOnly);\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const resultOffset = super.fromBER(inputBuffer, inputOffset, inputLength);\n        if (resultOffset === -1) {\n            return resultOffset;\n        }\n        this.setValueHex();\n        return resultOffset;\n    }\n    toBER(sizeOnly) {\n        return sizeOnly ? new ArrayBuffer(this.valueHexView.length) : this.valueHexView.slice().buffer;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            valueDec: this.valueDec\n        };\n    }\n    toString() {\n        const firstBit = this.valueHexView.length * 8 - 1;\n        let digits = new Uint8Array(this.valueHexView.length * 8 / 3);\n        let bitNumber = 0;\n        let currentByte;\n        const asn1View = this.valueHexView;\n        let result = \"\";\n        let flag = false;\n        for(let byteNumber = asn1View.byteLength - 1; byteNumber >= 0; byteNumber--){\n            currentByte = asn1View[byteNumber];\n            for(let i = 0; i < 8; i++){\n                if ((currentByte & 1) === 1) {\n                    switch(bitNumber){\n                        case firstBit:\n                            digits = viewSub(power2(bitNumber), digits);\n                            result = \"-\";\n                            break;\n                        default:\n                            digits = viewAdd(digits, power2(bitNumber));\n                    }\n                }\n                bitNumber++;\n                currentByte >>= 1;\n            }\n        }\n        for(let i = 0; i < digits.length; i++){\n            if (digits[i]) flag = true;\n            if (flag) result += digitsString.charAt(digits[i]);\n        }\n        if (flag === false) result += digitsString.charAt(0);\n        return result;\n    }\n}\n_a$p = LocalIntegerValueBlock;\nLocalIntegerValueBlock.NAME = \"IntegerValueBlock\";\n(()=>{\n    Object.defineProperty(_a$p.prototype, \"valueHex\", {\n        set: function(v) {\n            this.valueHexView = new Uint8Array(v);\n            this.setValueHex();\n        },\n        get: function() {\n            return this.valueHexView.slice().buffer;\n        }\n    });\n})();\nvar _a$o;\nclass Integer extends BaseBlock {\n    constructor(parameters = {}){\n        super(parameters, LocalIntegerValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 2;\n    }\n    toBigInt() {\n        assertBigInt();\n        return BigInt(this.valueBlock.toString());\n    }\n    static fromBigInt(value) {\n        assertBigInt();\n        const bigIntValue = BigInt(value);\n        const writer = new ViewWriter();\n        const hex = bigIntValue.toString(16).replace(/^-/, \"\");\n        const view = new Uint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromHex(hex));\n        if (bigIntValue < 0) {\n            const first = new Uint8Array(view.length + (view[0] & 0x80 ? 1 : 0));\n            first[0] |= 0x80;\n            const firstInt = BigInt(`0x${pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(first)}`);\n            const secondInt = firstInt + bigIntValue;\n            const second = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromHex(secondInt.toString(16)));\n            second[0] |= 0x80;\n            writer.write(second);\n        } else {\n            if (view[0] & 0x80) {\n                writer.write(new Uint8Array([\n                    0\n                ]));\n            }\n            writer.write(view);\n        }\n        const res = new Integer({\n            valueHex: writer.final()\n        });\n        return res;\n    }\n    convertToDER() {\n        const integer = new Integer({\n            valueHex: this.valueBlock.valueHexView\n        });\n        integer.valueBlock.toDER();\n        return integer;\n    }\n    convertFromDER() {\n        return new Integer({\n            valueHex: this.valueBlock.valueHexView[0] === 0 ? this.valueBlock.valueHexView.subarray(1) : this.valueBlock.valueHexView\n        });\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.valueBlock.toString()}`;\n    }\n}\n_a$o = Integer;\n(()=>{\n    typeStore.Integer = _a$o;\n})();\nInteger.NAME = \"INTEGER\";\nvar _a$n;\nclass Enumerated extends Integer {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 10;\n    }\n}\n_a$n = Enumerated;\n(()=>{\n    typeStore.Enumerated = _a$n;\n})();\nEnumerated.NAME = \"ENUMERATED\";\nclass LocalSidValueBlock extends HexBlock(ValueBlock) {\n    constructor({ valueDec = -1, isFirstSid = false, ...parameters } = {}){\n        super(parameters);\n        this.valueDec = valueDec;\n        this.isFirstSid = isFirstSid;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (!inputLength) {\n            return inputOffset;\n        }\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        this.valueHexView = new Uint8Array(inputLength);\n        for(let i = 0; i < inputLength; i++){\n            this.valueHexView[i] = intBuffer[i] & 0x7F;\n            this.blockLength++;\n            if ((intBuffer[i] & 0x80) === 0x00) break;\n        }\n        const tempView = new Uint8Array(this.blockLength);\n        for(let i = 0; i < this.blockLength; i++){\n            tempView[i] = this.valueHexView[i];\n        }\n        this.valueHexView = tempView;\n        if ((intBuffer[this.blockLength - 1] & 0x80) !== 0x00) {\n            this.error = \"End of input reached before message was fully decoded\";\n            return -1;\n        }\n        if (this.valueHexView[0] === 0x00) this.warnings.push(\"Needlessly long format of SID encoding\");\n        if (this.blockLength <= 8) this.valueDec = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(this.valueHexView, 7);\n        else {\n            this.isHexOnly = true;\n            this.warnings.push(\"Too big SID for decoding, hex only\");\n        }\n        return inputOffset + this.blockLength;\n    }\n    set valueBigInt(value) {\n        assertBigInt();\n        let bits = BigInt(value).toString(2);\n        while(bits.length % 7){\n            bits = \"0\" + bits;\n        }\n        const bytes = new Uint8Array(bits.length / 7);\n        for(let i = 0; i < bytes.length; i++){\n            bytes[i] = parseInt(bits.slice(i * 7, i * 7 + 7), 2) + (i + 1 < bytes.length ? 0x80 : 0);\n        }\n        this.fromBER(bytes.buffer, 0, bytes.length);\n    }\n    toBER(sizeOnly) {\n        if (this.isHexOnly) {\n            if (sizeOnly) return new ArrayBuffer(this.valueHexView.byteLength);\n            const curView = this.valueHexView;\n            const retView = new Uint8Array(this.blockLength);\n            for(let i = 0; i < this.blockLength - 1; i++)retView[i] = curView[i] | 0x80;\n            retView[this.blockLength - 1] = curView[this.blockLength - 1];\n            return retView.buffer;\n        }\n        const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.valueDec, 7);\n        if (encodedBuf.byteLength === 0) {\n            this.error = \"Error during encoding SID value\";\n            return EMPTY_BUFFER;\n        }\n        const retView = new Uint8Array(encodedBuf.byteLength);\n        if (!sizeOnly) {\n            const encodedView = new Uint8Array(encodedBuf);\n            const len = encodedBuf.byteLength - 1;\n            for(let i = 0; i < len; i++)retView[i] = encodedView[i] | 0x80;\n            retView[len] = encodedView[len];\n        }\n        return retView;\n    }\n    toString() {\n        let result = \"\";\n        if (this.isHexOnly) result = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueHexView);\n        else {\n            if (this.isFirstSid) {\n                let sidValue = this.valueDec;\n                if (this.valueDec <= 39) result = \"0.\";\n                else {\n                    if (this.valueDec <= 79) {\n                        result = \"1.\";\n                        sidValue -= 40;\n                    } else {\n                        result = \"2.\";\n                        sidValue -= 80;\n                    }\n                }\n                result += sidValue.toString();\n            } else result = this.valueDec.toString();\n        }\n        return result;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            valueDec: this.valueDec,\n            isFirstSid: this.isFirstSid\n        };\n    }\n}\nLocalSidValueBlock.NAME = \"sidBlock\";\nclass LocalObjectIdentifierValueBlock extends ValueBlock {\n    constructor({ value = EMPTY_STRING, ...parameters } = {}){\n        super(parameters);\n        this.value = [];\n        if (value) {\n            this.fromString(value);\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        let resultOffset = inputOffset;\n        while(inputLength > 0){\n            const sidBlock = new LocalSidValueBlock();\n            resultOffset = sidBlock.fromBER(inputBuffer, resultOffset, inputLength);\n            if (resultOffset === -1) {\n                this.blockLength = 0;\n                this.error = sidBlock.error;\n                return resultOffset;\n            }\n            if (this.value.length === 0) sidBlock.isFirstSid = true;\n            this.blockLength += sidBlock.blockLength;\n            inputLength -= sidBlock.blockLength;\n            this.value.push(sidBlock);\n        }\n        return resultOffset;\n    }\n    toBER(sizeOnly) {\n        const retBuffers = [];\n        for(let i = 0; i < this.value.length; i++){\n            const valueBuf = this.value[i].toBER(sizeOnly);\n            if (valueBuf.byteLength === 0) {\n                this.error = this.value[i].error;\n                return EMPTY_BUFFER;\n            }\n            retBuffers.push(valueBuf);\n        }\n        return concat(retBuffers);\n    }\n    fromString(string) {\n        this.value = [];\n        let pos1 = 0;\n        let pos2 = 0;\n        let sid = \"\";\n        let flag = false;\n        do {\n            pos2 = string.indexOf(\".\", pos1);\n            if (pos2 === -1) sid = string.substring(pos1);\n            else sid = string.substring(pos1, pos2);\n            pos1 = pos2 + 1;\n            if (flag) {\n                const sidBlock = this.value[0];\n                let plus = 0;\n                switch(sidBlock.valueDec){\n                    case 0:\n                        break;\n                    case 1:\n                        plus = 40;\n                        break;\n                    case 2:\n                        plus = 80;\n                        break;\n                    default:\n                        this.value = [];\n                        return;\n                }\n                const parsedSID = parseInt(sid, 10);\n                if (isNaN(parsedSID)) return;\n                sidBlock.valueDec = parsedSID + plus;\n                flag = false;\n            } else {\n                const sidBlock = new LocalSidValueBlock();\n                if (sid > Number.MAX_SAFE_INTEGER) {\n                    assertBigInt();\n                    const sidValue = BigInt(sid);\n                    sidBlock.valueBigInt = sidValue;\n                } else {\n                    sidBlock.valueDec = parseInt(sid, 10);\n                    if (isNaN(sidBlock.valueDec)) return;\n                }\n                if (!this.value.length) {\n                    sidBlock.isFirstSid = true;\n                    flag = true;\n                }\n                this.value.push(sidBlock);\n            }\n        }while (pos2 !== -1);\n    }\n    toString() {\n        let result = \"\";\n        let isHexOnly = false;\n        for(let i = 0; i < this.value.length; i++){\n            isHexOnly = this.value[i].isHexOnly;\n            let sidStr = this.value[i].toString();\n            if (i !== 0) result = `${result}.`;\n            if (isHexOnly) {\n                sidStr = `{${sidStr}}`;\n                if (this.value[i].isFirstSid) result = `2.{${sidStr} - 80}`;\n                else result += sidStr;\n            } else result += sidStr;\n        }\n        return result;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            value: this.toString(),\n            sidArray: []\n        };\n        for(let i = 0; i < this.value.length; i++){\n            object.sidArray.push(this.value[i].toJSON());\n        }\n        return object;\n    }\n}\nLocalObjectIdentifierValueBlock.NAME = \"ObjectIdentifierValueBlock\";\nvar _a$m;\nclass ObjectIdentifier extends BaseBlock {\n    constructor(parameters = {}){\n        super(parameters, LocalObjectIdentifierValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 6;\n    }\n    getValue() {\n        return this.valueBlock.toString();\n    }\n    setValue(value) {\n        this.valueBlock.fromString(value);\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.valueBlock.toString() || \"empty\"}`;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.getValue()\n        };\n    }\n}\n_a$m = ObjectIdentifier;\n(()=>{\n    typeStore.ObjectIdentifier = _a$m;\n})();\nObjectIdentifier.NAME = \"OBJECT IDENTIFIER\";\nclass LocalRelativeSidValueBlock extends HexBlock(LocalBaseBlock) {\n    constructor({ valueDec = 0, ...parameters } = {}){\n        super(parameters);\n        this.valueDec = valueDec;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (inputLength === 0) return inputOffset;\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) return -1;\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        this.valueHexView = new Uint8Array(inputLength);\n        for(let i = 0; i < inputLength; i++){\n            this.valueHexView[i] = intBuffer[i] & 0x7F;\n            this.blockLength++;\n            if ((intBuffer[i] & 0x80) === 0x00) break;\n        }\n        const tempView = new Uint8Array(this.blockLength);\n        for(let i = 0; i < this.blockLength; i++)tempView[i] = this.valueHexView[i];\n        this.valueHexView = tempView;\n        if ((intBuffer[this.blockLength - 1] & 0x80) !== 0x00) {\n            this.error = \"End of input reached before message was fully decoded\";\n            return -1;\n        }\n        if (this.valueHexView[0] === 0x00) this.warnings.push(\"Needlessly long format of SID encoding\");\n        if (this.blockLength <= 8) this.valueDec = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(this.valueHexView, 7);\n        else {\n            this.isHexOnly = true;\n            this.warnings.push(\"Too big SID for decoding, hex only\");\n        }\n        return inputOffset + this.blockLength;\n    }\n    toBER(sizeOnly) {\n        if (this.isHexOnly) {\n            if (sizeOnly) return new ArrayBuffer(this.valueHexView.byteLength);\n            const curView = this.valueHexView;\n            const retView = new Uint8Array(this.blockLength);\n            for(let i = 0; i < this.blockLength - 1; i++)retView[i] = curView[i] | 0x80;\n            retView[this.blockLength - 1] = curView[this.blockLength - 1];\n            return retView.buffer;\n        }\n        const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.valueDec, 7);\n        if (encodedBuf.byteLength === 0) {\n            this.error = \"Error during encoding SID value\";\n            return EMPTY_BUFFER;\n        }\n        const retView = new Uint8Array(encodedBuf.byteLength);\n        if (!sizeOnly) {\n            const encodedView = new Uint8Array(encodedBuf);\n            const len = encodedBuf.byteLength - 1;\n            for(let i = 0; i < len; i++)retView[i] = encodedView[i] | 0x80;\n            retView[len] = encodedView[len];\n        }\n        return retView.buffer;\n    }\n    toString() {\n        let result = \"\";\n        if (this.isHexOnly) result = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueHexView);\n        else {\n            result = this.valueDec.toString();\n        }\n        return result;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            valueDec: this.valueDec\n        };\n    }\n}\nLocalRelativeSidValueBlock.NAME = \"relativeSidBlock\";\nclass LocalRelativeObjectIdentifierValueBlock extends ValueBlock {\n    constructor({ value = EMPTY_STRING, ...parameters } = {}){\n        super(parameters);\n        this.value = [];\n        if (value) {\n            this.fromString(value);\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        let resultOffset = inputOffset;\n        while(inputLength > 0){\n            const sidBlock = new LocalRelativeSidValueBlock();\n            resultOffset = sidBlock.fromBER(inputBuffer, resultOffset, inputLength);\n            if (resultOffset === -1) {\n                this.blockLength = 0;\n                this.error = sidBlock.error;\n                return resultOffset;\n            }\n            this.blockLength += sidBlock.blockLength;\n            inputLength -= sidBlock.blockLength;\n            this.value.push(sidBlock);\n        }\n        return resultOffset;\n    }\n    toBER(sizeOnly, writer) {\n        const retBuffers = [];\n        for(let i = 0; i < this.value.length; i++){\n            const valueBuf = this.value[i].toBER(sizeOnly);\n            if (valueBuf.byteLength === 0) {\n                this.error = this.value[i].error;\n                return EMPTY_BUFFER;\n            }\n            retBuffers.push(valueBuf);\n        }\n        return concat(retBuffers);\n    }\n    fromString(string) {\n        this.value = [];\n        let pos1 = 0;\n        let pos2 = 0;\n        let sid = \"\";\n        do {\n            pos2 = string.indexOf(\".\", pos1);\n            if (pos2 === -1) sid = string.substring(pos1);\n            else sid = string.substring(pos1, pos2);\n            pos1 = pos2 + 1;\n            const sidBlock = new LocalRelativeSidValueBlock();\n            sidBlock.valueDec = parseInt(sid, 10);\n            if (isNaN(sidBlock.valueDec)) return true;\n            this.value.push(sidBlock);\n        }while (pos2 !== -1);\n        return true;\n    }\n    toString() {\n        let result = \"\";\n        let isHexOnly = false;\n        for(let i = 0; i < this.value.length; i++){\n            isHexOnly = this.value[i].isHexOnly;\n            let sidStr = this.value[i].toString();\n            if (i !== 0) result = `${result}.`;\n            if (isHexOnly) {\n                sidStr = `{${sidStr}}`;\n                result += sidStr;\n            } else result += sidStr;\n        }\n        return result;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            value: this.toString(),\n            sidArray: []\n        };\n        for(let i = 0; i < this.value.length; i++)object.sidArray.push(this.value[i].toJSON());\n        return object;\n    }\n}\nLocalRelativeObjectIdentifierValueBlock.NAME = \"RelativeObjectIdentifierValueBlock\";\nvar _a$l;\nclass RelativeObjectIdentifier extends BaseBlock {\n    constructor(parameters = {}){\n        super(parameters, LocalRelativeObjectIdentifierValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 13;\n    }\n    getValue() {\n        return this.valueBlock.toString();\n    }\n    setValue(value) {\n        this.valueBlock.fromString(value);\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.valueBlock.toString() || \"empty\"}`;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.getValue()\n        };\n    }\n}\n_a$l = RelativeObjectIdentifier;\n(()=>{\n    typeStore.RelativeObjectIdentifier = _a$l;\n})();\nRelativeObjectIdentifier.NAME = \"RelativeObjectIdentifier\";\nvar _a$k;\nclass Sequence extends Constructed {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 16;\n    }\n}\n_a$k = Sequence;\n(()=>{\n    typeStore.Sequence = _a$k;\n})();\nSequence.NAME = \"SEQUENCE\";\nvar _a$j;\nclass Set extends Constructed {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 17;\n    }\n}\n_a$j = Set;\n(()=>{\n    typeStore.Set = _a$j;\n})();\nSet.NAME = \"SET\";\nclass LocalStringValueBlock extends HexBlock(ValueBlock) {\n    constructor({ ...parameters } = {}){\n        super(parameters);\n        this.isHexOnly = true;\n        this.value = EMPTY_STRING;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.value\n        };\n    }\n}\nLocalStringValueBlock.NAME = \"StringValueBlock\";\nclass LocalSimpleStringValueBlock extends LocalStringValueBlock {\n}\nLocalSimpleStringValueBlock.NAME = \"SimpleStringValueBlock\";\nclass LocalSimpleStringBlock extends BaseStringBlock {\n    constructor({ ...parameters } = {}){\n        super(parameters, LocalSimpleStringValueBlock);\n    }\n    fromBuffer(inputBuffer) {\n        this.valueBlock.value = String.fromCharCode.apply(null, pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer));\n    }\n    fromString(inputString) {\n        const strLen = inputString.length;\n        const view = this.valueBlock.valueHexView = new Uint8Array(strLen);\n        for(let i = 0; i < strLen; i++)view[i] = inputString.charCodeAt(i);\n        this.valueBlock.value = inputString;\n    }\n}\nLocalSimpleStringBlock.NAME = \"SIMPLE STRING\";\nclass LocalUtf8StringValueBlock extends LocalSimpleStringBlock {\n    fromBuffer(inputBuffer) {\n        this.valueBlock.valueHexView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        try {\n            this.valueBlock.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToUtf8String(inputBuffer);\n        } catch (ex) {\n            this.warnings.push(`Error during \"decodeURIComponent\": ${ex}, using raw string`);\n            this.valueBlock.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBinary(inputBuffer);\n        }\n    }\n    fromString(inputString) {\n        this.valueBlock.valueHexView = new Uint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromUtf8String(inputString));\n        this.valueBlock.value = inputString;\n    }\n}\nLocalUtf8StringValueBlock.NAME = \"Utf8StringValueBlock\";\nvar _a$i;\nclass Utf8String extends LocalUtf8StringValueBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 12;\n    }\n}\n_a$i = Utf8String;\n(()=>{\n    typeStore.Utf8String = _a$i;\n})();\nUtf8String.NAME = \"UTF8String\";\nclass LocalBmpStringValueBlock extends LocalSimpleStringBlock {\n    fromBuffer(inputBuffer) {\n        this.valueBlock.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToUtf16String(inputBuffer);\n        this.valueBlock.valueHexView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n    }\n    fromString(inputString) {\n        this.valueBlock.value = inputString;\n        this.valueBlock.valueHexView = new Uint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromUtf16String(inputString));\n    }\n}\nLocalBmpStringValueBlock.NAME = \"BmpStringValueBlock\";\nvar _a$h;\nclass BmpString extends LocalBmpStringValueBlock {\n    constructor({ ...parameters } = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 30;\n    }\n}\n_a$h = BmpString;\n(()=>{\n    typeStore.BmpString = _a$h;\n})();\nBmpString.NAME = \"BMPString\";\nclass LocalUniversalStringValueBlock extends LocalSimpleStringBlock {\n    fromBuffer(inputBuffer) {\n        const copyBuffer = ArrayBuffer.isView(inputBuffer) ? inputBuffer.slice().buffer : inputBuffer.slice(0);\n        const valueView = new Uint8Array(copyBuffer);\n        for(let i = 0; i < valueView.length; i += 4){\n            valueView[i] = valueView[i + 3];\n            valueView[i + 1] = valueView[i + 2];\n            valueView[i + 2] = 0x00;\n            valueView[i + 3] = 0x00;\n        }\n        this.valueBlock.value = String.fromCharCode.apply(null, new Uint32Array(copyBuffer));\n    }\n    fromString(inputString) {\n        const strLength = inputString.length;\n        const valueHexView = this.valueBlock.valueHexView = new Uint8Array(strLength * 4);\n        for(let i = 0; i < strLength; i++){\n            const codeBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(inputString.charCodeAt(i), 8);\n            const codeView = new Uint8Array(codeBuf);\n            if (codeView.length > 4) continue;\n            const dif = 4 - codeView.length;\n            for(let j = codeView.length - 1; j >= 0; j--)valueHexView[i * 4 + j + dif] = codeView[j];\n        }\n        this.valueBlock.value = inputString;\n    }\n}\nLocalUniversalStringValueBlock.NAME = \"UniversalStringValueBlock\";\nvar _a$g;\nclass UniversalString extends LocalUniversalStringValueBlock {\n    constructor({ ...parameters } = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 28;\n    }\n}\n_a$g = UniversalString;\n(()=>{\n    typeStore.UniversalString = _a$g;\n})();\nUniversalString.NAME = \"UniversalString\";\nvar _a$f;\nclass NumericString extends LocalSimpleStringBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 18;\n    }\n}\n_a$f = NumericString;\n(()=>{\n    typeStore.NumericString = _a$f;\n})();\nNumericString.NAME = \"NumericString\";\nvar _a$e;\nclass PrintableString extends LocalSimpleStringBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 19;\n    }\n}\n_a$e = PrintableString;\n(()=>{\n    typeStore.PrintableString = _a$e;\n})();\nPrintableString.NAME = \"PrintableString\";\nvar _a$d;\nclass TeletexString extends LocalSimpleStringBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 20;\n    }\n}\n_a$d = TeletexString;\n(()=>{\n    typeStore.TeletexString = _a$d;\n})();\nTeletexString.NAME = \"TeletexString\";\nvar _a$c;\nclass VideotexString extends LocalSimpleStringBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 21;\n    }\n}\n_a$c = VideotexString;\n(()=>{\n    typeStore.VideotexString = _a$c;\n})();\nVideotexString.NAME = \"VideotexString\";\nvar _a$b;\nclass IA5String extends LocalSimpleStringBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 22;\n    }\n}\n_a$b = IA5String;\n(()=>{\n    typeStore.IA5String = _a$b;\n})();\nIA5String.NAME = \"IA5String\";\nvar _a$a;\nclass GraphicString extends LocalSimpleStringBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 25;\n    }\n}\n_a$a = GraphicString;\n(()=>{\n    typeStore.GraphicString = _a$a;\n})();\nGraphicString.NAME = \"GraphicString\";\nvar _a$9;\nclass VisibleString extends LocalSimpleStringBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 26;\n    }\n}\n_a$9 = VisibleString;\n(()=>{\n    typeStore.VisibleString = _a$9;\n})();\nVisibleString.NAME = \"VisibleString\";\nvar _a$8;\nclass GeneralString extends LocalSimpleStringBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 27;\n    }\n}\n_a$8 = GeneralString;\n(()=>{\n    typeStore.GeneralString = _a$8;\n})();\nGeneralString.NAME = \"GeneralString\";\nvar _a$7;\nclass CharacterString extends LocalSimpleStringBlock {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 29;\n    }\n}\n_a$7 = CharacterString;\n(()=>{\n    typeStore.CharacterString = _a$7;\n})();\nCharacterString.NAME = \"CharacterString\";\nvar _a$6;\nclass UTCTime extends VisibleString {\n    constructor({ value, valueDate, ...parameters } = {}){\n        super(parameters);\n        this.year = 0;\n        this.month = 0;\n        this.day = 0;\n        this.hour = 0;\n        this.minute = 0;\n        this.second = 0;\n        if (value) {\n            this.fromString(value);\n            this.valueBlock.valueHexView = new Uint8Array(value.length);\n            for(let i = 0; i < value.length; i++)this.valueBlock.valueHexView[i] = value.charCodeAt(i);\n        }\n        if (valueDate) {\n            this.fromDate(valueDate);\n            this.valueBlock.valueHexView = new Uint8Array(this.toBuffer());\n        }\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 23;\n    }\n    fromBuffer(inputBuffer) {\n        this.fromString(String.fromCharCode.apply(null, pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer)));\n    }\n    toBuffer() {\n        const str = this.toString();\n        const buffer = new ArrayBuffer(str.length);\n        const view = new Uint8Array(buffer);\n        for(let i = 0; i < str.length; i++)view[i] = str.charCodeAt(i);\n        return buffer;\n    }\n    fromDate(inputDate) {\n        this.year = inputDate.getUTCFullYear();\n        this.month = inputDate.getUTCMonth() + 1;\n        this.day = inputDate.getUTCDate();\n        this.hour = inputDate.getUTCHours();\n        this.minute = inputDate.getUTCMinutes();\n        this.second = inputDate.getUTCSeconds();\n    }\n    toDate() {\n        return new Date(Date.UTC(this.year, this.month - 1, this.day, this.hour, this.minute, this.second));\n    }\n    fromString(inputString) {\n        const parser = /(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})Z/ig;\n        const parserArray = parser.exec(inputString);\n        if (parserArray === null) {\n            this.error = \"Wrong input string for conversion\";\n            return;\n        }\n        const year = parseInt(parserArray[1], 10);\n        if (year >= 50) this.year = 1900 + year;\n        else this.year = 2000 + year;\n        this.month = parseInt(parserArray[2], 10);\n        this.day = parseInt(parserArray[3], 10);\n        this.hour = parseInt(parserArray[4], 10);\n        this.minute = parseInt(parserArray[5], 10);\n        this.second = parseInt(parserArray[6], 10);\n    }\n    toString(encoding = \"iso\") {\n        if (encoding === \"iso\") {\n            const outputArray = new Array(7);\n            outputArray[0] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.year < 2000 ? this.year - 1900 : this.year - 2000, 2);\n            outputArray[1] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.month, 2);\n            outputArray[2] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.day, 2);\n            outputArray[3] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.hour, 2);\n            outputArray[4] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.minute, 2);\n            outputArray[5] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.second, 2);\n            outputArray[6] = \"Z\";\n            return outputArray.join(\"\");\n        }\n        return super.toString(encoding);\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.toDate().toISOString()}`;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            year: this.year,\n            month: this.month,\n            day: this.day,\n            hour: this.hour,\n            minute: this.minute,\n            second: this.second\n        };\n    }\n}\n_a$6 = UTCTime;\n(()=>{\n    typeStore.UTCTime = _a$6;\n})();\nUTCTime.NAME = \"UTCTime\";\nvar _a$5;\nclass GeneralizedTime extends UTCTime {\n    constructor(parameters = {}){\n        var _b;\n        super(parameters);\n        (_b = this.millisecond) !== null && _b !== void 0 ? _b : this.millisecond = 0;\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 24;\n    }\n    fromDate(inputDate) {\n        super.fromDate(inputDate);\n        this.millisecond = inputDate.getUTCMilliseconds();\n    }\n    toDate() {\n        return new Date(Date.UTC(this.year, this.month - 1, this.day, this.hour, this.minute, this.second, this.millisecond));\n    }\n    fromString(inputString) {\n        let isUTC = false;\n        let timeString = \"\";\n        let dateTimeString = \"\";\n        let fractionPart = 0;\n        let parser;\n        let hourDifference = 0;\n        let minuteDifference = 0;\n        if (inputString[inputString.length - 1] === \"Z\") {\n            timeString = inputString.substring(0, inputString.length - 1);\n            isUTC = true;\n        } else {\n            const number = new Number(inputString[inputString.length - 1]);\n            if (isNaN(number.valueOf())) throw new Error(\"Wrong input string for conversion\");\n            timeString = inputString;\n        }\n        if (isUTC) {\n            if (timeString.indexOf(\"+\") !== -1) throw new Error(\"Wrong input string for conversion\");\n            if (timeString.indexOf(\"-\") !== -1) throw new Error(\"Wrong input string for conversion\");\n        } else {\n            let multiplier = 1;\n            let differencePosition = timeString.indexOf(\"+\");\n            let differenceString = \"\";\n            if (differencePosition === -1) {\n                differencePosition = timeString.indexOf(\"-\");\n                multiplier = -1;\n            }\n            if (differencePosition !== -1) {\n                differenceString = timeString.substring(differencePosition + 1);\n                timeString = timeString.substring(0, differencePosition);\n                if (differenceString.length !== 2 && differenceString.length !== 4) throw new Error(\"Wrong input string for conversion\");\n                let number = parseInt(differenceString.substring(0, 2), 10);\n                if (isNaN(number.valueOf())) throw new Error(\"Wrong input string for conversion\");\n                hourDifference = multiplier * number;\n                if (differenceString.length === 4) {\n                    number = parseInt(differenceString.substring(2, 4), 10);\n                    if (isNaN(number.valueOf())) throw new Error(\"Wrong input string for conversion\");\n                    minuteDifference = multiplier * number;\n                }\n            }\n        }\n        let fractionPointPosition = timeString.indexOf(\".\");\n        if (fractionPointPosition === -1) fractionPointPosition = timeString.indexOf(\",\");\n        if (fractionPointPosition !== -1) {\n            const fractionPartCheck = new Number(`0${timeString.substring(fractionPointPosition)}`);\n            if (isNaN(fractionPartCheck.valueOf())) throw new Error(\"Wrong input string for conversion\");\n            fractionPart = fractionPartCheck.valueOf();\n            dateTimeString = timeString.substring(0, fractionPointPosition);\n        } else dateTimeString = timeString;\n        switch(true){\n            case dateTimeString.length === 8:\n                parser = /(\\d{4})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) throw new Error(\"Wrong input string for conversion\");\n                break;\n            case dateTimeString.length === 10:\n                parser = /(\\d{4})(\\d{2})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) {\n                    let fractionResult = 60 * fractionPart;\n                    this.minute = Math.floor(fractionResult);\n                    fractionResult = 60 * (fractionResult - this.minute);\n                    this.second = Math.floor(fractionResult);\n                    fractionResult = 1000 * (fractionResult - this.second);\n                    this.millisecond = Math.floor(fractionResult);\n                }\n                break;\n            case dateTimeString.length === 12:\n                parser = /(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) {\n                    let fractionResult = 60 * fractionPart;\n                    this.second = Math.floor(fractionResult);\n                    fractionResult = 1000 * (fractionResult - this.second);\n                    this.millisecond = Math.floor(fractionResult);\n                }\n                break;\n            case dateTimeString.length === 14:\n                parser = /(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) {\n                    const fractionResult = 1000 * fractionPart;\n                    this.millisecond = Math.floor(fractionResult);\n                }\n                break;\n            default:\n                throw new Error(\"Wrong input string for conversion\");\n        }\n        const parserArray = parser.exec(dateTimeString);\n        if (parserArray === null) throw new Error(\"Wrong input string for conversion\");\n        for(let j = 1; j < parserArray.length; j++){\n            switch(j){\n                case 1:\n                    this.year = parseInt(parserArray[j], 10);\n                    break;\n                case 2:\n                    this.month = parseInt(parserArray[j], 10);\n                    break;\n                case 3:\n                    this.day = parseInt(parserArray[j], 10);\n                    break;\n                case 4:\n                    this.hour = parseInt(parserArray[j], 10) + hourDifference;\n                    break;\n                case 5:\n                    this.minute = parseInt(parserArray[j], 10) + minuteDifference;\n                    break;\n                case 6:\n                    this.second = parseInt(parserArray[j], 10);\n                    break;\n                default:\n                    throw new Error(\"Wrong input string for conversion\");\n            }\n        }\n        if (isUTC === false) {\n            const tempDate = new Date(this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n            this.year = tempDate.getUTCFullYear();\n            this.month = tempDate.getUTCMonth();\n            this.day = tempDate.getUTCDay();\n            this.hour = tempDate.getUTCHours();\n            this.minute = tempDate.getUTCMinutes();\n            this.second = tempDate.getUTCSeconds();\n            this.millisecond = tempDate.getUTCMilliseconds();\n        }\n    }\n    toString(encoding = \"iso\") {\n        if (encoding === \"iso\") {\n            const outputArray = [];\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.year, 4));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.month, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.day, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.hour, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.minute, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.second, 2));\n            if (this.millisecond !== 0) {\n                outputArray.push(\".\");\n                outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.millisecond, 3));\n            }\n            outputArray.push(\"Z\");\n            return outputArray.join(\"\");\n        }\n        return super.toString(encoding);\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            millisecond: this.millisecond\n        };\n    }\n}\n_a$5 = GeneralizedTime;\n(()=>{\n    typeStore.GeneralizedTime = _a$5;\n})();\nGeneralizedTime.NAME = \"GeneralizedTime\";\nvar _a$4;\nclass DATE extends Utf8String {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 31;\n    }\n}\n_a$4 = DATE;\n(()=>{\n    typeStore.DATE = _a$4;\n})();\nDATE.NAME = \"DATE\";\nvar _a$3;\nclass TimeOfDay extends Utf8String {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 32;\n    }\n}\n_a$3 = TimeOfDay;\n(()=>{\n    typeStore.TimeOfDay = _a$3;\n})();\nTimeOfDay.NAME = \"TimeOfDay\";\nvar _a$2;\nclass DateTime extends Utf8String {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 33;\n    }\n}\n_a$2 = DateTime;\n(()=>{\n    typeStore.DateTime = _a$2;\n})();\nDateTime.NAME = \"DateTime\";\nvar _a$1;\nclass Duration extends Utf8String {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 34;\n    }\n}\n_a$1 = Duration;\n(()=>{\n    typeStore.Duration = _a$1;\n})();\nDuration.NAME = \"Duration\";\nvar _a;\nclass TIME extends Utf8String {\n    constructor(parameters = {}){\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 14;\n    }\n}\n_a = TIME;\n(()=>{\n    typeStore.TIME = _a;\n})();\nTIME.NAME = \"TIME\";\nclass Any {\n    constructor({ name = EMPTY_STRING, optional = false } = {}){\n        this.name = name;\n        this.optional = optional;\n    }\n}\nclass Choice extends Any {\n    constructor({ value = [], ...parameters } = {}){\n        super(parameters);\n        this.value = value;\n    }\n}\nclass Repeated extends Any {\n    constructor({ value = new Any(), local = false, ...parameters } = {}){\n        super(parameters);\n        this.value = value;\n        this.local = local;\n    }\n}\nclass RawData {\n    constructor({ data = EMPTY_VIEW } = {}){\n        this.dataView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(data);\n    }\n    get data() {\n        return this.dataView.slice().buffer;\n    }\n    set data(value) {\n        this.dataView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(value);\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const endLength = inputOffset + inputLength;\n        this.dataView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer).subarray(inputOffset, endLength);\n        return endLength;\n    }\n    toBER(sizeOnly) {\n        return this.dataView.slice().buffer;\n    }\n}\nfunction compareSchema(root, inputData, inputSchema) {\n    if (inputSchema instanceof Choice) {\n        for(let j = 0; j < inputSchema.value.length; j++){\n            const result = compareSchema(root, inputData, inputSchema.value[j]);\n            if (result.verified) {\n                return {\n                    verified: true,\n                    result: root\n                };\n            }\n        }\n        {\n            const _result = {\n                verified: false,\n                result: {\n                    error: \"Wrong values for Choice type\"\n                }\n            };\n            if (inputSchema.hasOwnProperty(NAME)) _result.name = inputSchema.name;\n            return _result;\n        }\n    }\n    if (inputSchema instanceof Any) {\n        if (inputSchema.hasOwnProperty(NAME)) root[inputSchema.name] = inputData;\n        return {\n            verified: true,\n            result: root\n        };\n    }\n    if (root instanceof Object === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong root object\"\n            }\n        };\n    }\n    if (inputData instanceof Object === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 data\"\n            }\n        };\n    }\n    if (inputSchema instanceof Object === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 schema\"\n            }\n        };\n    }\n    if (ID_BLOCK in inputSchema === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 schema\"\n            }\n        };\n    }\n    if (FROM_BER in inputSchema.idBlock === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 schema\"\n            }\n        };\n    }\n    if (TO_BER in inputSchema.idBlock === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 schema\"\n            }\n        };\n    }\n    const encodedId = inputSchema.idBlock.toBER(false);\n    if (encodedId.byteLength === 0) {\n        return {\n            verified: false,\n            result: {\n                error: \"Error encoding idBlock for ASN.1 schema\"\n            }\n        };\n    }\n    const decodedOffset = inputSchema.idBlock.fromBER(encodedId, 0, encodedId.byteLength);\n    if (decodedOffset === -1) {\n        return {\n            verified: false,\n            result: {\n                error: \"Error decoding idBlock for ASN.1 schema\"\n            }\n        };\n    }\n    if (inputSchema.idBlock.hasOwnProperty(TAG_CLASS) === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 schema\"\n            }\n        };\n    }\n    if (inputSchema.idBlock.tagClass !== inputData.idBlock.tagClass) {\n        return {\n            verified: false,\n            result: root\n        };\n    }\n    if (inputSchema.idBlock.hasOwnProperty(TAG_NUMBER) === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 schema\"\n            }\n        };\n    }\n    if (inputSchema.idBlock.tagNumber !== inputData.idBlock.tagNumber) {\n        return {\n            verified: false,\n            result: root\n        };\n    }\n    if (inputSchema.idBlock.hasOwnProperty(IS_CONSTRUCTED) === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 schema\"\n            }\n        };\n    }\n    if (inputSchema.idBlock.isConstructed !== inputData.idBlock.isConstructed) {\n        return {\n            verified: false,\n            result: root\n        };\n    }\n    if (!(IS_HEX_ONLY in inputSchema.idBlock)) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 schema\"\n            }\n        };\n    }\n    if (inputSchema.idBlock.isHexOnly !== inputData.idBlock.isHexOnly) {\n        return {\n            verified: false,\n            result: root\n        };\n    }\n    if (inputSchema.idBlock.isHexOnly) {\n        if (VALUE_HEX_VIEW in inputSchema.idBlock === false) {\n            return {\n                verified: false,\n                result: {\n                    error: \"Wrong ASN.1 schema\"\n                }\n            };\n        }\n        const schemaView = inputSchema.idBlock.valueHexView;\n        const asn1View = inputData.idBlock.valueHexView;\n        if (schemaView.length !== asn1View.length) {\n            return {\n                verified: false,\n                result: root\n            };\n        }\n        for(let i = 0; i < schemaView.length; i++){\n            if (schemaView[i] !== asn1View[1]) {\n                return {\n                    verified: false,\n                    result: root\n                };\n            }\n        }\n    }\n    if (inputSchema.name) {\n        inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n        if (inputSchema.name) root[inputSchema.name] = inputData;\n    }\n    if (inputSchema instanceof typeStore.Constructed) {\n        let admission = 0;\n        let result = {\n            verified: false,\n            result: {\n                error: \"Unknown error\"\n            }\n        };\n        let maxLength = inputSchema.valueBlock.value.length;\n        if (maxLength > 0) {\n            if (inputSchema.valueBlock.value[0] instanceof Repeated) {\n                maxLength = inputData.valueBlock.value.length;\n            }\n        }\n        if (maxLength === 0) {\n            return {\n                verified: true,\n                result: root\n            };\n        }\n        if (inputData.valueBlock.value.length === 0 && inputSchema.valueBlock.value.length !== 0) {\n            let _optional = true;\n            for(let i = 0; i < inputSchema.valueBlock.value.length; i++)_optional = _optional && (inputSchema.valueBlock.value[i].optional || false);\n            if (_optional) {\n                return {\n                    verified: true,\n                    result: root\n                };\n            }\n            if (inputSchema.name) {\n                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                if (inputSchema.name) delete root[inputSchema.name];\n            }\n            root.error = \"Inconsistent object length\";\n            return {\n                verified: false,\n                result: root\n            };\n        }\n        for(let i = 0; i < maxLength; i++){\n            if (i - admission >= inputData.valueBlock.value.length) {\n                if (inputSchema.valueBlock.value[i].optional === false) {\n                    const _result = {\n                        verified: false,\n                        result: root\n                    };\n                    root.error = \"Inconsistent length between ASN.1 data and schema\";\n                    if (inputSchema.name) {\n                        inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                        if (inputSchema.name) {\n                            delete root[inputSchema.name];\n                            _result.name = inputSchema.name;\n                        }\n                    }\n                    return _result;\n                }\n            } else {\n                if (inputSchema.valueBlock.value[0] instanceof Repeated) {\n                    result = compareSchema(root, inputData.valueBlock.value[i], inputSchema.valueBlock.value[0].value);\n                    if (result.verified === false) {\n                        if (inputSchema.valueBlock.value[0].optional) admission++;\n                        else {\n                            if (inputSchema.name) {\n                                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                                if (inputSchema.name) delete root[inputSchema.name];\n                            }\n                            return result;\n                        }\n                    }\n                    if (NAME in inputSchema.valueBlock.value[0] && inputSchema.valueBlock.value[0].name.length > 0) {\n                        let arrayRoot = {};\n                        if (LOCAL in inputSchema.valueBlock.value[0] && inputSchema.valueBlock.value[0].local) arrayRoot = inputData;\n                        else arrayRoot = root;\n                        if (typeof arrayRoot[inputSchema.valueBlock.value[0].name] === \"undefined\") arrayRoot[inputSchema.valueBlock.value[0].name] = [];\n                        arrayRoot[inputSchema.valueBlock.value[0].name].push(inputData.valueBlock.value[i]);\n                    }\n                } else {\n                    result = compareSchema(root, inputData.valueBlock.value[i - admission], inputSchema.valueBlock.value[i]);\n                    if (result.verified === false) {\n                        if (inputSchema.valueBlock.value[i].optional) admission++;\n                        else {\n                            if (inputSchema.name) {\n                                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                                if (inputSchema.name) delete root[inputSchema.name];\n                            }\n                            return result;\n                        }\n                    }\n                }\n            }\n        }\n        if (result.verified === false) {\n            const _result = {\n                verified: false,\n                result: root\n            };\n            if (inputSchema.name) {\n                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                if (inputSchema.name) {\n                    delete root[inputSchema.name];\n                    _result.name = inputSchema.name;\n                }\n            }\n            return _result;\n        }\n        return {\n            verified: true,\n            result: root\n        };\n    }\n    if (inputSchema.primitiveSchema && VALUE_HEX_VIEW in inputData.valueBlock) {\n        const asn1 = localFromBER(inputData.valueBlock.valueHexView);\n        if (asn1.offset === -1) {\n            const _result = {\n                verified: false,\n                result: asn1.result\n            };\n            if (inputSchema.name) {\n                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                if (inputSchema.name) {\n                    delete root[inputSchema.name];\n                    _result.name = inputSchema.name;\n                }\n            }\n            return _result;\n        }\n        return compareSchema(root, asn1.result, inputSchema.primitiveSchema);\n    }\n    return {\n        verified: true,\n        result: root\n    };\n}\nfunction verifySchema(inputBuffer, inputSchema) {\n    if (inputSchema instanceof Object === false) {\n        return {\n            verified: false,\n            result: {\n                error: \"Wrong ASN.1 schema type\"\n            }\n        };\n    }\n    const asn1 = localFromBER(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer));\n    if (asn1.offset === -1) {\n        return {\n            verified: false,\n            result: asn1.result\n        };\n    }\n    return compareSchema(asn1.result, asn1.result, inputSchema);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/asn1js/build/index.es.js\n");

/***/ })

};
;