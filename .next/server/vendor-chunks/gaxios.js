"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gaxios";
exports.ids = ["vendor-chunks/gaxios"];
exports.modules = {

/***/ "(rsc)/./node_modules/gaxios/build/src/common.js":
/*!*************************************************!*\
  !*** ./node_modules/gaxios/build/src/common.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GaxiosError = exports.GAXIOS_ERROR_SYMBOL = void 0;\nexports.defaultErrorRedactor = defaultErrorRedactor;\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/gaxios/build/src/util.js\");\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\n/**\n * Support `instanceof` operator for `GaxiosError`s in different versions of this library.\n *\n * @see {@link GaxiosError[Symbol.hasInstance]}\n */ exports.GAXIOS_ERROR_SYMBOL = Symbol.for(`${util_1.pkg.name}-gaxios-error`);\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */ class GaxiosError extends Error {\n    /**\n     * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n     *\n     * @see {@link GAXIOS_ERROR_SYMBOL}\n     * @see {@link GaxiosError[GAXIOS_ERROR_SYMBOL]}\n     */ static [(_a = exports.GAXIOS_ERROR_SYMBOL, Symbol.hasInstance)](instance) {\n        if (instance && typeof instance === \"object\" && exports.GAXIOS_ERROR_SYMBOL in instance && instance[exports.GAXIOS_ERROR_SYMBOL] === util_1.pkg.version) {\n            return true;\n        }\n        // fallback to native\n        return Function.prototype[Symbol.hasInstance].call(GaxiosError, instance);\n    }\n    constructor(message, config, response, error){\n        var _b;\n        super(message);\n        this.config = config;\n        this.response = response;\n        this.error = error;\n        /**\n         * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n         *\n         * @see {@link GAXIOS_ERROR_SYMBOL}\n         * @see {@link GaxiosError[Symbol.hasInstance]}\n         * @see {@link https://github.com/microsoft/TypeScript/issues/13965#issuecomment-278570200}\n         * @see {@link https://stackoverflow.com/questions/46618852/require-and-instanceof}\n         * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/@@hasInstance#reverting_to_default_instanceof_behavior}\n         */ this[_a] = util_1.pkg.version;\n        // deep-copy config as we do not want to mutate\n        // the existing config for future retries/use\n        this.config = (0, extend_1.default)(true, {}, config);\n        if (this.response) {\n            this.response.config = (0, extend_1.default)(true, {}, this.response.config);\n        }\n        if (this.response) {\n            try {\n                this.response.data = translateData(this.config.responseType, (_b = this.response) === null || _b === void 0 ? void 0 : _b.data);\n            } catch (_c) {\n            // best effort - don't throw an error within an error\n            // we could set `this.response.config.responseType = 'unknown'`, but\n            // that would mutate future calls with this config object.\n            }\n            this.status = this.response.status;\n        }\n        if (error && \"code\" in error && error.code) {\n            this.code = error.code;\n        }\n        if (config.errorRedactor) {\n            config.errorRedactor({\n                config: this.config,\n                response: this.response\n            });\n        }\n    }\n}\nexports.GaxiosError = GaxiosError;\nfunction translateData(responseType, data) {\n    switch(responseType){\n        case \"stream\":\n            return data;\n        case \"json\":\n            return JSON.parse(JSON.stringify(data));\n        case \"arraybuffer\":\n            return JSON.parse(Buffer.from(data).toString(\"utf8\"));\n        case \"blob\":\n            return JSON.parse(data.text());\n        default:\n            return data;\n    }\n}\n/**\n * An experimental error redactor.\n *\n * @param config Config to potentially redact properties of\n * @param response Config to potentially redact properties of\n *\n * @experimental\n */ function defaultErrorRedactor(data) {\n    const REDACT = \"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.\";\n    function redactHeaders(headers) {\n        if (!headers) return;\n        for (const key of Object.keys(headers)){\n            // any casing of `Authentication`\n            if (/^authentication$/i.test(key)) {\n                headers[key] = REDACT;\n            }\n            // any casing of `Authorization`\n            if (/^authorization$/i.test(key)) {\n                headers[key] = REDACT;\n            }\n            // anything containing secret, such as 'client secret'\n            if (/secret/i.test(key)) {\n                headers[key] = REDACT;\n            }\n        }\n    }\n    function redactString(obj, key) {\n        if (typeof obj === \"object\" && obj !== null && typeof obj[key] === \"string\") {\n            const text = obj[key];\n            if (/grant_type=/i.test(text) || /assertion=/i.test(text) || /secret/i.test(text)) {\n                obj[key] = REDACT;\n            }\n        }\n    }\n    function redactObject(obj) {\n        if (typeof obj === \"object\" && obj !== null) {\n            if (\"grant_type\" in obj) {\n                obj[\"grant_type\"] = REDACT;\n            }\n            if (\"assertion\" in obj) {\n                obj[\"assertion\"] = REDACT;\n            }\n            if (\"client_secret\" in obj) {\n                obj[\"client_secret\"] = REDACT;\n            }\n        }\n    }\n    if (data.config) {\n        redactHeaders(data.config.headers);\n        redactString(data.config, \"data\");\n        redactObject(data.config.data);\n        redactString(data.config, \"body\");\n        redactObject(data.config.body);\n        try {\n            const url = new url_1.URL(\"\", data.config.url);\n            if (url.searchParams.has(\"token\")) {\n                url.searchParams.set(\"token\", REDACT);\n            }\n            if (url.searchParams.has(\"client_secret\")) {\n                url.searchParams.set(\"client_secret\", REDACT);\n            }\n            data.config.url = url.toString();\n        } catch (_b) {\n        // ignore error - no need to parse an invalid URL\n        }\n    }\n    if (data.response) {\n        defaultErrorRedactor({\n            config: data.response.config\n        });\n        redactHeaders(data.response.headers);\n        redactString(data.response, \"data\");\n        redactObject(data.response.data);\n    }\n    return data;\n} //# sourceMappingURL=common.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/gaxios.js":
/*!*************************************************!*\
  !*** ./node_modules/gaxios/build/src/gaxios.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __classPrivateFieldGet = (void 0) && (void 0).__classPrivateFieldGet || function(receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (void 0) && (void 0).__classPrivateFieldSet || function(receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nvar _Gaxios_instances, _a, _Gaxios_urlMayUseProxy, _Gaxios_applyRequestInterceptors, _Gaxios_applyResponseInterceptors, _Gaxios_prepareRequest, _Gaxios_proxyAgent, _Gaxios_getProxyAgent;\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Gaxios = void 0;\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\nconst https_1 = __webpack_require__(/*! https */ \"https\");\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/node-fetch/lib/index.mjs\"));\nconst querystring_1 = __importDefault(__webpack_require__(/*! querystring */ \"querystring\"));\nconst is_stream_1 = __importDefault(__webpack_require__(/*! is-stream */ \"(rsc)/./node_modules/is-stream/index.js\"));\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/gaxios/build/src/common.js\");\nconst retry_1 = __webpack_require__(/*! ./retry */ \"(rsc)/./node_modules/gaxios/build/src/retry.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst uuid_1 = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/index.js\");\nconst interceptor_1 = __webpack_require__(/*! ./interceptor */ \"(rsc)/./node_modules/gaxios/build/src/interceptor.js\");\n/* eslint-disable @typescript-eslint/no-explicit-any */ const fetch = hasFetch() ? window.fetch : node_fetch_1.default;\nfunction hasWindow() {\n    return  false && 0;\n}\nfunction hasFetch() {\n    return hasWindow() && !!window.fetch;\n}\nfunction hasBuffer() {\n    return typeof Buffer !== \"undefined\";\n}\nfunction hasHeader(options, header) {\n    return !!getHeader(options, header);\n}\nfunction getHeader(options, header) {\n    header = header.toLowerCase();\n    for (const key of Object.keys((options === null || options === void 0 ? void 0 : options.headers) || {})){\n        if (header === key.toLowerCase()) {\n            return options.headers[key];\n        }\n    }\n    return undefined;\n}\nclass Gaxios {\n    /**\n     * The Gaxios class is responsible for making HTTP requests.\n     * @param defaults The default set of options to be used for this instance.\n     */ constructor(defaults){\n        _Gaxios_instances.add(this);\n        this.agentCache = new Map();\n        this.defaults = defaults || {};\n        this.interceptors = {\n            request: new interceptor_1.GaxiosInterceptorManager(),\n            response: new interceptor_1.GaxiosInterceptorManager()\n        };\n    }\n    /**\n     * Perform an HTTP request with the given options.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */ async request(opts = {}) {\n        opts = await __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_prepareRequest).call(this, opts);\n        opts = await __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_applyRequestInterceptors).call(this, opts);\n        return __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_applyResponseInterceptors).call(this, this._request(opts));\n    }\n    async _defaultAdapter(opts) {\n        const fetchImpl = opts.fetchImplementation || fetch;\n        const res = await fetchImpl(opts.url, opts);\n        const data = await this.getResponseData(opts, res);\n        return this.translateResponse(opts, res, data);\n    }\n    /**\n     * Internal, retryable version of the `request` method.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */ async _request(opts = {}) {\n        var _b;\n        try {\n            let translatedResponse;\n            if (opts.adapter) {\n                translatedResponse = await opts.adapter(opts, this._defaultAdapter.bind(this));\n            } else {\n                translatedResponse = await this._defaultAdapter(opts);\n            }\n            if (!opts.validateStatus(translatedResponse.status)) {\n                if (opts.responseType === \"stream\") {\n                    let response = \"\";\n                    await new Promise((resolve)=>{\n                        (translatedResponse === null || translatedResponse === void 0 ? void 0 : translatedResponse.data).on(\"data\", (chunk)=>{\n                            response += chunk;\n                        });\n                        (translatedResponse === null || translatedResponse === void 0 ? void 0 : translatedResponse.data).on(\"end\", resolve);\n                    });\n                    translatedResponse.data = response;\n                }\n                throw new common_1.GaxiosError(`Request failed with status code ${translatedResponse.status}`, opts, translatedResponse);\n            }\n            return translatedResponse;\n        } catch (e) {\n            const err = e instanceof common_1.GaxiosError ? e : new common_1.GaxiosError(e.message, opts, undefined, e);\n            const { shouldRetry, config } = await (0, retry_1.getRetryConfig)(err);\n            if (shouldRetry && config) {\n                err.config.retryConfig.currentRetryAttempt = config.retryConfig.currentRetryAttempt;\n                // The error's config could be redacted - therefore we only want to\n                // copy the retry state over to the existing config\n                opts.retryConfig = (_b = err.config) === null || _b === void 0 ? void 0 : _b.retryConfig;\n                return this._request(opts);\n            }\n            throw err;\n        }\n    }\n    async getResponseData(opts, res) {\n        switch(opts.responseType){\n            case \"stream\":\n                return res.body;\n            case \"json\":\n                {\n                    let data = await res.text();\n                    try {\n                        data = JSON.parse(data);\n                    } catch (_b) {\n                    // continue\n                    }\n                    return data;\n                }\n            case \"arraybuffer\":\n                return res.arrayBuffer();\n            case \"blob\":\n                return res.blob();\n            case \"text\":\n                return res.text();\n            default:\n                return this.getResponseDataFromContentType(res);\n        }\n    }\n    /**\n     * By default, throw for any non-2xx status code\n     * @param status status code from the HTTP response\n     */ validateStatus(status) {\n        return status >= 200 && status < 300;\n    }\n    /**\n     * Encode a set of key/value pars into a querystring format (?foo=bar&baz=boo)\n     * @param params key value pars to encode\n     */ paramsSerializer(params) {\n        return querystring_1.default.stringify(params);\n    }\n    translateResponse(opts, res, data) {\n        // headers need to be converted from a map to an obj\n        const headers = {};\n        res.headers.forEach((value, key)=>{\n            headers[key] = value;\n        });\n        return {\n            config: opts,\n            data: data,\n            headers,\n            status: res.status,\n            statusText: res.statusText,\n            // XMLHttpRequestLike\n            request: {\n                responseURL: res.url\n            }\n        };\n    }\n    /**\n     * Attempts to parse a response by looking at the Content-Type header.\n     * @param {FetchResponse} response the HTTP response.\n     * @returns {Promise<any>} a promise that resolves to the response data.\n     */ async getResponseDataFromContentType(response) {\n        let contentType = response.headers.get(\"Content-Type\");\n        if (contentType === null) {\n            // Maintain existing functionality by calling text()\n            return response.text();\n        }\n        contentType = contentType.toLowerCase();\n        if (contentType.includes(\"application/json\")) {\n            let data = await response.text();\n            try {\n                data = JSON.parse(data);\n            } catch (_b) {\n            // continue\n            }\n            return data;\n        } else if (contentType.match(/^text\\//)) {\n            return response.text();\n        } else {\n            // If the content type is something not easily handled, just return the raw data (blob)\n            return response.blob();\n        }\n    }\n    /**\n     * Creates an async generator that yields the pieces of a multipart/related request body.\n     * This implementation follows the spec: https://www.ietf.org/rfc/rfc2387.txt. However, recursive\n     * multipart/related requests are not currently supported.\n     *\n     * @param {GaxioMultipartOptions[]} multipartOptions the pieces to turn into a multipart/related body.\n     * @param {string} boundary the boundary string to be placed between each part.\n     */ async *getMultipartRequest(multipartOptions, boundary) {\n        const finale = `--${boundary}--`;\n        for (const currentPart of multipartOptions){\n            const partContentType = currentPart.headers[\"Content-Type\"] || \"application/octet-stream\";\n            const preamble = `--${boundary}\\r\\nContent-Type: ${partContentType}\\r\\n\\r\\n`;\n            yield preamble;\n            if (typeof currentPart.content === \"string\") {\n                yield currentPart.content;\n            } else {\n                yield* currentPart.content;\n            }\n            yield \"\\r\\n\";\n        }\n        yield finale;\n    }\n}\nexports.Gaxios = Gaxios;\n_a = Gaxios, _Gaxios_instances = new WeakSet(), _Gaxios_urlMayUseProxy = function _Gaxios_urlMayUseProxy(url, noProxy = []) {\n    var _b, _c;\n    const candidate = new url_1.URL(url);\n    const noProxyList = [\n        ...noProxy\n    ];\n    const noProxyEnvList = ((_c = (_b = process.env.NO_PROXY) !== null && _b !== void 0 ? _b : process.env.no_proxy) === null || _c === void 0 ? void 0 : _c.split(\",\")) || [];\n    for (const rule of noProxyEnvList){\n        noProxyList.push(rule.trim());\n    }\n    for (const rule of noProxyList){\n        // Match regex\n        if (rule instanceof RegExp) {\n            if (rule.test(candidate.toString())) {\n                return false;\n            }\n        } else if (rule instanceof url_1.URL) {\n            if (rule.origin === candidate.origin) {\n                return false;\n            }\n        } else if (rule.startsWith(\"*.\") || rule.startsWith(\".\")) {\n            const cleanedRule = rule.replace(/^\\*\\./, \".\");\n            if (candidate.hostname.endsWith(cleanedRule)) {\n                return false;\n            }\n        } else if (rule === candidate.origin || rule === candidate.hostname || rule === candidate.href) {\n            return false;\n        }\n    }\n    return true;\n}, _Gaxios_applyRequestInterceptors = /**\n * Applies the request interceptors. The request interceptors are applied after the\n * call to prepareRequest is completed.\n *\n * @param {GaxiosOptions} options The current set of options.\n *\n * @returns {Promise<GaxiosOptions>} Promise that resolves to the set of options or response after interceptors are applied.\n */ async function _Gaxios_applyRequestInterceptors(options) {\n    let promiseChain = Promise.resolve(options);\n    for (const interceptor of this.interceptors.request.values()){\n        if (interceptor) {\n            promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n        }\n    }\n    return promiseChain;\n}, _Gaxios_applyResponseInterceptors = /**\n * Applies the response interceptors. The response interceptors are applied after the\n * call to request is made.\n *\n * @param {GaxiosOptions} options The current set of options.\n *\n * @returns {Promise<GaxiosOptions>} Promise that resolves to the set of options or response after interceptors are applied.\n */ async function _Gaxios_applyResponseInterceptors(response) {\n    let promiseChain = Promise.resolve(response);\n    for (const interceptor of this.interceptors.response.values()){\n        if (interceptor) {\n            promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n        }\n    }\n    return promiseChain;\n}, _Gaxios_prepareRequest = /**\n * Validates the options, merges them with defaults, and prepare request.\n *\n * @param options The original options passed from the client.\n * @returns Prepared options, ready to make a request\n */ async function _Gaxios_prepareRequest(options) {\n    var _b, _c, _d, _e;\n    const opts = (0, extend_1.default)(true, {}, this.defaults, options);\n    if (!opts.url) {\n        throw new Error(\"URL is required.\");\n    }\n    // baseUrl has been deprecated, remove in 2.0\n    const baseUrl = opts.baseUrl || opts.baseURL;\n    if (baseUrl) {\n        opts.url = baseUrl.toString() + opts.url;\n    }\n    opts.paramsSerializer = opts.paramsSerializer || this.paramsSerializer;\n    if (opts.params && Object.keys(opts.params).length > 0) {\n        let additionalQueryParams = opts.paramsSerializer(opts.params);\n        if (additionalQueryParams.startsWith(\"?\")) {\n            additionalQueryParams = additionalQueryParams.slice(1);\n        }\n        const prefix = opts.url.toString().includes(\"?\") ? \"&\" : \"?\";\n        opts.url = opts.url + prefix + additionalQueryParams;\n    }\n    if (typeof options.maxContentLength === \"number\") {\n        opts.size = options.maxContentLength;\n    }\n    if (typeof options.maxRedirects === \"number\") {\n        opts.follow = options.maxRedirects;\n    }\n    opts.headers = opts.headers || {};\n    if (opts.multipart === undefined && opts.data) {\n        const isFormData = typeof FormData === \"undefined\" ? false : (opts === null || opts === void 0 ? void 0 : opts.data) instanceof FormData;\n        if (is_stream_1.default.readable(opts.data)) {\n            opts.body = opts.data;\n        } else if (hasBuffer() && Buffer.isBuffer(opts.data)) {\n            // Do not attempt to JSON.stringify() a Buffer:\n            opts.body = opts.data;\n            if (!hasHeader(opts, \"Content-Type\")) {\n                opts.headers[\"Content-Type\"] = \"application/json\";\n            }\n        } else if (typeof opts.data === \"object\") {\n            // If www-form-urlencoded content type has been set, but data is\n            // provided as an object, serialize the content using querystring:\n            if (!isFormData) {\n                if (getHeader(opts, \"content-type\") === \"application/x-www-form-urlencoded\") {\n                    opts.body = opts.paramsSerializer(opts.data);\n                } else {\n                    // } else if (!(opts.data instanceof FormData)) {\n                    if (!hasHeader(opts, \"Content-Type\")) {\n                        opts.headers[\"Content-Type\"] = \"application/json\";\n                    }\n                    opts.body = JSON.stringify(opts.data);\n                }\n            }\n        } else {\n            opts.body = opts.data;\n        }\n    } else if (opts.multipart && opts.multipart.length > 0) {\n        // note: once the minimum version reaches Node 16,\n        // this can be replaced with randomUUID() function from crypto\n        // and the dependency on UUID removed\n        const boundary = (0, uuid_1.v4)();\n        opts.headers[\"Content-Type\"] = `multipart/related; boundary=${boundary}`;\n        const bodyStream = new stream_1.PassThrough();\n        opts.body = bodyStream;\n        (0, stream_1.pipeline)(this.getMultipartRequest(opts.multipart, boundary), bodyStream, ()=>{});\n    }\n    opts.validateStatus = opts.validateStatus || this.validateStatus;\n    opts.responseType = opts.responseType || \"unknown\";\n    if (!opts.headers[\"Accept\"] && opts.responseType === \"json\") {\n        opts.headers[\"Accept\"] = \"application/json\";\n    }\n    opts.method = opts.method || \"GET\";\n    const proxy = opts.proxy || ((_b = process === null || process === void 0 ? void 0 : process.env) === null || _b === void 0 ? void 0 : _b.HTTPS_PROXY) || ((_c = process === null || process === void 0 ? void 0 : process.env) === null || _c === void 0 ? void 0 : _c.https_proxy) || ((_d = process === null || process === void 0 ? void 0 : process.env) === null || _d === void 0 ? void 0 : _d.HTTP_PROXY) || ((_e = process === null || process === void 0 ? void 0 : process.env) === null || _e === void 0 ? void 0 : _e.http_proxy);\n    const urlMayUseProxy = __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_urlMayUseProxy).call(this, opts.url, opts.noProxy);\n    if (opts.agent) {\n    // don't do any of the following options - use the user-provided agent.\n    } else if (proxy && urlMayUseProxy) {\n        const HttpsProxyAgent = await __classPrivateFieldGet(_a, _a, \"m\", _Gaxios_getProxyAgent).call(_a);\n        if (this.agentCache.has(proxy)) {\n            opts.agent = this.agentCache.get(proxy);\n        } else {\n            opts.agent = new HttpsProxyAgent(proxy, {\n                cert: opts.cert,\n                key: opts.key\n            });\n            this.agentCache.set(proxy, opts.agent);\n        }\n    } else if (opts.cert && opts.key) {\n        // Configure client for mTLS\n        if (this.agentCache.has(opts.key)) {\n            opts.agent = this.agentCache.get(opts.key);\n        } else {\n            opts.agent = new https_1.Agent({\n                cert: opts.cert,\n                key: opts.key\n            });\n            this.agentCache.set(opts.key, opts.agent);\n        }\n    }\n    if (typeof opts.errorRedactor !== \"function\" && opts.errorRedactor !== false) {\n        opts.errorRedactor = common_1.defaultErrorRedactor;\n    }\n    return opts;\n}, _Gaxios_getProxyAgent = async function _Gaxios_getProxyAgent() {\n    __classPrivateFieldSet(this, _a, __classPrivateFieldGet(this, _a, \"f\", _Gaxios_proxyAgent) || (await Promise.resolve().then(()=>__importStar(__webpack_require__(/*! https-proxy-agent */ \"(rsc)/./node_modules/https-proxy-agent/dist/index.js\")))).HttpsProxyAgent, \"f\", _Gaxios_proxyAgent);\n    return __classPrivateFieldGet(this, _a, \"f\", _Gaxios_proxyAgent);\n};\n/**\n * A cache for the lazily-loaded proxy agent.\n *\n * Should use {@link Gaxios[#getProxyAgent]} to retrieve.\n */ // using `import` to dynamically import the types here\n_Gaxios_proxyAgent = {\n    value: void 0\n}; //# sourceMappingURL=gaxios.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/gaxios.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/index.js":
/*!************************************************!*\
  !*** ./node_modules/gaxios/build/src/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.instance = exports.Gaxios = exports.GaxiosError = void 0;\nexports.request = request;\nconst gaxios_1 = __webpack_require__(/*! ./gaxios */ \"(rsc)/./node_modules/gaxios/build/src/gaxios.js\");\nObject.defineProperty(exports, \"Gaxios\", ({\n    enumerable: true,\n    get: function() {\n        return gaxios_1.Gaxios;\n    }\n}));\nvar common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/gaxios/build/src/common.js\");\nObject.defineProperty(exports, \"GaxiosError\", ({\n    enumerable: true,\n    get: function() {\n        return common_1.GaxiosError;\n    }\n}));\n__exportStar(__webpack_require__(/*! ./interceptor */ \"(rsc)/./node_modules/gaxios/build/src/interceptor.js\"), exports);\n/**\n * The default instance used when the `request` method is directly\n * invoked.\n */ exports.instance = new gaxios_1.Gaxios();\n/**\n * Make an HTTP request using the given options.\n * @param opts Options for the request\n */ async function request(opts) {\n    return exports.instance.request(opts);\n} //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/interceptor.js":
/*!******************************************************!*\
  !*** ./node_modules/gaxios/build/src/interceptor.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GaxiosInterceptorManager = void 0;\n/**\n * Class to manage collections of GaxiosInterceptors for both requests and responses.\n */ class GaxiosInterceptorManager extends Set {\n}\nexports.GaxiosInterceptorManager = GaxiosInterceptorManager; //# sourceMappingURL=interceptor.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/interceptor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/retry.js":
/*!************************************************!*\
  !*** ./node_modules/gaxios/build/src/retry.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getRetryConfig = getRetryConfig;\nasync function getRetryConfig(err) {\n    let config = getConfig(err);\n    if (!err || !err.config || !config && !err.config.retry) {\n        return {\n            shouldRetry: false\n        };\n    }\n    config = config || {};\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    config.retry = config.retry === undefined || config.retry === null ? 3 : config.retry;\n    config.httpMethodsToRetry = config.httpMethodsToRetry || [\n        \"GET\",\n        \"HEAD\",\n        \"PUT\",\n        \"OPTIONS\",\n        \"DELETE\"\n    ];\n    config.noResponseRetries = config.noResponseRetries === undefined || config.noResponseRetries === null ? 2 : config.noResponseRetries;\n    config.retryDelayMultiplier = config.retryDelayMultiplier ? config.retryDelayMultiplier : 2;\n    config.timeOfFirstRequest = config.timeOfFirstRequest ? config.timeOfFirstRequest : Date.now();\n    config.totalTimeout = config.totalTimeout ? config.totalTimeout : Number.MAX_SAFE_INTEGER;\n    config.maxRetryDelay = config.maxRetryDelay ? config.maxRetryDelay : Number.MAX_SAFE_INTEGER;\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    const retryRanges = [\n        // https://en.wikipedia.org/wiki/List_of_HTTP_status_codes\n        // 1xx - Retry (Informational, request still processing)\n        // 2xx - Do not retry (Success)\n        // 3xx - Do not retry (Redirect)\n        // 4xx - Do not retry (Client errors)\n        // 408 - Retry (\"Request Timeout\")\n        // 429 - Retry (\"Too Many Requests\")\n        // 5xx - Retry (Server errors)\n        [\n            100,\n            199\n        ],\n        [\n            408,\n            408\n        ],\n        [\n            429,\n            429\n        ],\n        [\n            500,\n            599\n        ]\n    ];\n    config.statusCodesToRetry = config.statusCodesToRetry || retryRanges;\n    // Put the config back into the err\n    err.config.retryConfig = config;\n    // Determine if we should retry the request\n    const shouldRetryFn = config.shouldRetry || shouldRetryRequest;\n    if (!await shouldRetryFn(err)) {\n        return {\n            shouldRetry: false,\n            config: err.config\n        };\n    }\n    const delay = getNextRetryDelay(config);\n    // We're going to retry!  Incremenent the counter.\n    err.config.retryConfig.currentRetryAttempt += 1;\n    // Create a promise that invokes the retry after the backOffDelay\n    const backoff = config.retryBackoff ? config.retryBackoff(err, delay) : new Promise((resolve)=>{\n        setTimeout(resolve, delay);\n    });\n    // Notify the user if they added an `onRetryAttempt` handler\n    if (config.onRetryAttempt) {\n        config.onRetryAttempt(err);\n    }\n    // Return the promise in which recalls Gaxios to retry the request\n    await backoff;\n    return {\n        shouldRetry: true,\n        config: err.config\n    };\n}\n/**\n * Determine based on config if we should retry the request.\n * @param err The GaxiosError passed to the interceptor.\n */ function shouldRetryRequest(err) {\n    var _a;\n    const config = getConfig(err);\n    // node-fetch raises an AbortError if signaled:\n    // https://github.com/bitinn/node-fetch#request-cancellation-with-abortsignal\n    if (err.name === \"AbortError\" || ((_a = err.error) === null || _a === void 0 ? void 0 : _a.name) === \"AbortError\") {\n        return false;\n    }\n    // If there's no config, or retries are disabled, return.\n    if (!config || config.retry === 0) {\n        return false;\n    }\n    // Check if this error has no response (ETIMEDOUT, ENOTFOUND, etc)\n    if (!err.response && (config.currentRetryAttempt || 0) >= config.noResponseRetries) {\n        return false;\n    }\n    // Only retry with configured HttpMethods.\n    if (!err.config.method || config.httpMethodsToRetry.indexOf(err.config.method.toUpperCase()) < 0) {\n        return false;\n    }\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    if (err.response && err.response.status) {\n        let isInRange = false;\n        for (const [min, max] of config.statusCodesToRetry){\n            const status = err.response.status;\n            if (status >= min && status <= max) {\n                isInRange = true;\n                break;\n            }\n        }\n        if (!isInRange) {\n            return false;\n        }\n    }\n    // If we are out of retry attempts, return\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    if (config.currentRetryAttempt >= config.retry) {\n        return false;\n    }\n    return true;\n}\n/**\n * Acquire the raxConfig object from an GaxiosError if available.\n * @param err The Gaxios error with a config object.\n */ function getConfig(err) {\n    if (err && err.config && err.config.retryConfig) {\n        return err.config.retryConfig;\n    }\n    return;\n}\n/**\n * Gets the delay to wait before the next retry.\n *\n * @param {RetryConfig} config The current set of retry options\n * @returns {number} the amount of ms to wait before the next retry attempt.\n */ function getNextRetryDelay(config) {\n    var _a;\n    // Calculate time to wait with exponential backoff.\n    // If this is the first retry, look for a configured retryDelay.\n    const retryDelay = config.currentRetryAttempt ? 0 : (_a = config.retryDelay) !== null && _a !== void 0 ? _a : 100;\n    // Formula: retryDelay + ((retryDelayMultiplier^currentRetryAttempt - 1 / 2) * 1000)\n    const calculatedDelay = retryDelay + (Math.pow(config.retryDelayMultiplier, config.currentRetryAttempt) - 1) / 2 * 1000;\n    const maxAllowableDelay = config.totalTimeout - (Date.now() - config.timeOfFirstRequest);\n    return Math.min(calculatedDelay, maxAllowableDelay, config.maxRetryDelay);\n} //# sourceMappingURL=retry.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/retry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/src/util.js":
/*!***********************************************!*\
  !*** ./node_modules/gaxios/build/src/util.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2023 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.pkg = void 0;\nexports.pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/gaxios/package.json\"); //# sourceMappingURL=util.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL3NyYy91dGlsLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsNEJBQTRCO0FBQzVCLGtFQUFrRTtBQUNsRSxtRUFBbUU7QUFDbkUsMENBQTBDO0FBQzFDLEVBQUU7QUFDRixnREFBZ0Q7QUFDaEQsRUFBRTtBQUNGLHNFQUFzRTtBQUN0RSxvRUFBb0U7QUFDcEUsMkVBQTJFO0FBQzNFLHNFQUFzRTtBQUN0RSxpQ0FBaUM7QUFDakNBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBQzdERCxXQUFXLEdBQUcsS0FBSztBQUNuQkEsdUdBQXNCLEVBQ3RCLGdDQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL2N1c3RvbS1ncm91cC1jcmVhdG9yLy4vbm9kZV9tb2R1bGVzL2dheGlvcy9idWlsZC9zcmMvdXRpbC5qcz81ZGM5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gQ29weXJpZ2h0IDIwMjMgR29vZ2xlIExMQ1xuLy8gTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbi8vIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbi8vIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuLy9cbi8vICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuLy9cbi8vIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbi8vIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbi8vIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuLy8gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuLy8gbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnBrZyA9IHZvaWQgMDtcbmV4cG9ydHMucGtnID0gcmVxdWlyZSgnLi4vLi4vcGFja2FnZS5qc29uJyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dGlsLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsInBrZyIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/src/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/package.json":
/*!******************************************!*\
  !*** ./node_modules/gaxios/package.json ***!
  \******************************************/
/***/ ((module) => {

module.exports = JSON.parse('{"name":"gaxios","version":"6.7.1","description":"A simple common HTTP client specifically for Google APIs and services.","main":"build/src/index.js","types":"build/src/index.d.ts","files":["build/src"],"scripts":{"lint":"gts check","test":"c8 mocha build/test","presystem-test":"npm run compile","system-test":"mocha build/system-test --timeout 80000","compile":"tsc -p .","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","webpack":"webpack","prebrowser-test":"npm run compile","browser-test":"node build/browser-test/browser-test-runner.js","docs":"compodoc src/","docs-test":"linkinator docs","predocs-test":"npm run docs","samples-test":"cd samples/ && npm link ../ && npm test && cd ../","prelint":"cd samples; npm link ../; npm install","clean":"gts clean","precompile":"gts clean"},"repository":"googleapis/gaxios","keywords":["google"],"engines":{"node":">=14"},"author":"Google, LLC","license":"Apache-2.0","devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@compodoc/compodoc":"1.1.19","@types/cors":"^2.8.6","@types/express":"^4.16.1","@types/extend":"^3.0.1","@types/mocha":"^9.0.0","@types/multiparty":"0.0.36","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/node":"^20.0.0","@types/node-fetch":"^2.5.7","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","@types/uuid":"^10.0.0","abort-controller":"^3.0.0","assert":"^2.0.0","browserify":"^17.0.0","c8":"^8.0.0","cheerio":"1.0.0-rc.10","cors":"^2.8.5","execa":"^5.0.0","express":"^4.16.4","form-data":"^4.0.0","gts":"^5.0.0","is-docker":"^2.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"5.0.0","linkinator":"^3.0.0","mocha":"^8.0.0","multiparty":"^4.2.1","mv":"^2.1.1","ncp":"^2.0.0","nock":"^13.0.0","null-loader":"^4.0.0","puppeteer":"^19.0.0","sinon":"^18.0.0","stream-browserify":"^3.0.0","tmp":"0.2.3","ts-loader":"^8.0.0","typescript":"^5.1.6","webpack":"^5.35.0","webpack-cli":"^4.0.0"},"dependencies":{"extend":"^3.0.2","https-proxy-agent":"^7.0.1","is-stream":"^2.0.0","node-fetch":"^2.6.9","uuid":"^9.0.1"}}');

/***/ })

};
;