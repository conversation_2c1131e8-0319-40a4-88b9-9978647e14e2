"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/agent-base";
exports.ids = ["vendor-chunks/agent-base"];
exports.modules = {

/***/ "(rsc)/./node_modules/agent-base/dist/helpers.js":
/*!*************************************************!*\
  !*** ./node_modules/agent-base/dist/helpers.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.req = exports.json = exports.toBuffer = void 0;\nconst http = __importStar(__webpack_require__(/*! http */ \"http\"));\nconst https = __importStar(__webpack_require__(/*! https */ \"https\"));\nasync function toBuffer(stream) {\n    let length = 0;\n    const chunks = [];\n    for await (const chunk of stream){\n        length += chunk.length;\n        chunks.push(chunk);\n    }\n    return Buffer.concat(chunks, length);\n}\nexports.toBuffer = toBuffer;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nasync function json(stream) {\n    const buf = await toBuffer(stream);\n    const str = buf.toString(\"utf8\");\n    try {\n        return JSON.parse(str);\n    } catch (_err) {\n        const err = _err;\n        err.message += ` (input: ${str})`;\n        throw err;\n    }\n}\nexports.json = json;\nfunction req(url, opts = {}) {\n    const href = typeof url === \"string\" ? url : url.href;\n    const req = (href.startsWith(\"https:\") ? https : http).request(url, opts);\n    const promise = new Promise((resolve, reject)=>{\n        req.once(\"response\", resolve).once(\"error\", reject).end();\n    });\n    req.then = promise.then.bind(promise);\n    return req;\n}\nexports.req = req; //# sourceMappingURL=helpers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agent-base/dist/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agent-base/dist/index.js":
/*!***********************************************!*\
  !*** ./node_modules/agent-base/dist/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k in mod)if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Agent = void 0;\nconst net = __importStar(__webpack_require__(/*! net */ \"net\"));\nconst http = __importStar(__webpack_require__(/*! http */ \"http\"));\nconst https_1 = __webpack_require__(/*! https */ \"https\");\n__exportStar(__webpack_require__(/*! ./helpers */ \"(rsc)/./node_modules/agent-base/dist/helpers.js\"), exports);\nconst INTERNAL = Symbol(\"AgentBaseInternalState\");\nclass Agent extends http.Agent {\n    constructor(opts){\n        super(opts);\n        this[INTERNAL] = {};\n    }\n    /**\n     * Determine whether this is an `http` or `https` request.\n     */ isSecureEndpoint(options) {\n        if (options) {\n            // First check the `secureEndpoint` property explicitly, since this\n            // means that a parent `Agent` is \"passing through\" to this instance.\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            if (typeof options.secureEndpoint === \"boolean\") {\n                return options.secureEndpoint;\n            }\n            // If no explicit `secure` endpoint, check if `protocol` property is\n            // set. This will usually be the case since using a full string URL\n            // or `URL` instance should be the most common usage.\n            if (typeof options.protocol === \"string\") {\n                return options.protocol === \"https:\";\n            }\n        }\n        // Finally, if no `protocol` property was set, then fall back to\n        // checking the stack trace of the current call stack, and try to\n        // detect the \"https\" module.\n        const { stack } = new Error();\n        if (typeof stack !== \"string\") return false;\n        return stack.split(\"\\n\").some((l)=>l.indexOf(\"(https.js:\") !== -1 || l.indexOf(\"node:https:\") !== -1);\n    }\n    // In order to support async signatures in `connect()` and Node's native\n    // connection pooling in `http.Agent`, the array of sockets for each origin\n    // has to be updated synchronously. This is so the length of the array is\n    // accurate when `addRequest()` is next called. We achieve this by creating a\n    // fake socket and adding it to `sockets[origin]` and incrementing\n    // `totalSocketCount`.\n    incrementSockets(name) {\n        // If `maxSockets` and `maxTotalSockets` are both Infinity then there is no\n        // need to create a fake socket because Node.js native connection pooling\n        // will never be invoked.\n        if (this.maxSockets === Infinity && this.maxTotalSockets === Infinity) {\n            return null;\n        }\n        // All instances of `sockets` are expected TypeScript errors. The\n        // alternative is to add it as a private property of this class but that\n        // will break TypeScript subclassing.\n        if (!this.sockets[name]) {\n            // @ts-expect-error `sockets` is readonly in `@types/node`\n            this.sockets[name] = [];\n        }\n        const fakeSocket = new net.Socket({\n            writable: false\n        });\n        this.sockets[name].push(fakeSocket);\n        // @ts-expect-error `totalSocketCount` isn't defined in `@types/node`\n        this.totalSocketCount++;\n        return fakeSocket;\n    }\n    decrementSockets(name, socket) {\n        if (!this.sockets[name] || socket === null) {\n            return;\n        }\n        const sockets = this.sockets[name];\n        const index = sockets.indexOf(socket);\n        if (index !== -1) {\n            sockets.splice(index, 1);\n            // @ts-expect-error  `totalSocketCount` isn't defined in `@types/node`\n            this.totalSocketCount--;\n            if (sockets.length === 0) {\n                // @ts-expect-error `sockets` is readonly in `@types/node`\n                delete this.sockets[name];\n            }\n        }\n    }\n    // In order to properly update the socket pool, we need to call `getName()` on\n    // the core `https.Agent` if it is a secureEndpoint.\n    getName(options) {\n        const secureEndpoint = typeof options.secureEndpoint === \"boolean\" ? options.secureEndpoint : this.isSecureEndpoint(options);\n        if (secureEndpoint) {\n            // @ts-expect-error `getName()` isn't defined in `@types/node`\n            return https_1.Agent.prototype.getName.call(this, options);\n        }\n        // @ts-expect-error `getName()` isn't defined in `@types/node`\n        return super.getName(options);\n    }\n    createSocket(req, options, cb) {\n        const connectOpts = {\n            ...options,\n            secureEndpoint: this.isSecureEndpoint(options)\n        };\n        const name = this.getName(connectOpts);\n        const fakeSocket = this.incrementSockets(name);\n        Promise.resolve().then(()=>this.connect(req, connectOpts)).then((socket)=>{\n            this.decrementSockets(name, fakeSocket);\n            if (socket instanceof http.Agent) {\n                try {\n                    // @ts-expect-error `addRequest()` isn't defined in `@types/node`\n                    return socket.addRequest(req, connectOpts);\n                } catch (err) {\n                    return cb(err);\n                }\n            }\n            this[INTERNAL].currentSocket = socket;\n            // @ts-expect-error `createSocket()` isn't defined in `@types/node`\n            super.createSocket(req, options, cb);\n        }, (err)=>{\n            this.decrementSockets(name, fakeSocket);\n            cb(err);\n        });\n    }\n    createConnection() {\n        const socket = this[INTERNAL].currentSocket;\n        this[INTERNAL].currentSocket = undefined;\n        if (!socket) {\n            throw new Error(\"No socket was returned in the `connect()` function\");\n        }\n        return socket;\n    }\n    get defaultPort() {\n        return this[INTERNAL].defaultPort ?? (this.protocol === \"https:\" ? 443 : 80);\n    }\n    set defaultPort(v) {\n        if (this[INTERNAL]) {\n            this[INTERNAL].defaultPort = v;\n        }\n    }\n    get protocol() {\n        return this[INTERNAL].protocol ?? (this.isSecureEndpoint() ? \"https:\" : \"http:\");\n    }\n    set protocol(v) {\n        if (this[INTERNAL]) {\n            this[INTERNAL].protocol = v;\n        }\n    }\n}\nexports.Agent = Agent; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agent-base/dist/index.js\n");

/***/ })

};
;