"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bignumber.js";
exports.ids = ["vendor-chunks/bignumber.js"];
exports.modules = {

/***/ "(rsc)/./node_modules/bignumber.js/bignumber.js":
/*!************************************************!*\
  !*** ./node_modules/bignumber.js/bignumber.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("var __WEBPACK_AMD_DEFINE_RESULT__;\n(function(globalObject) {\n    \"use strict\";\n    /*\r\n *      bignumber.js v9.2.1\r\n *      A JavaScript library for arbitrary-precision arithmetic.\r\n *      https://github.com/MikeMcl/bignumber.js\r\n *      Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>\r\n *      MIT Licensed.\r\n *\r\n *      BigNumber.prototype methods     |  BigNumber methods\r\n *                                      |\r\n *      absoluteValue            abs    |  clone\r\n *      comparedTo                      |  config               set\r\n *      decimalPlaces            dp     |      DECIMAL_PLACES\r\n *      dividedBy                div    |      ROUNDING_MODE\r\n *      dividedToIntegerBy       idiv   |      EXPONENTIAL_AT\r\n *      exponentiatedBy          pow    |      RANGE\r\n *      integerValue                    |      CRYPTO\r\n *      isEqualTo                eq     |      MODULO_MODE\r\n *      isFinite                        |      POW_PRECISION\r\n *      isGreaterThan            gt     |      FORMAT\r\n *      isGreaterThanOrEqualTo   gte    |      ALPHABET\r\n *      isInteger                       |  isBigNumber\r\n *      isLessThan               lt     |  maximum              max\r\n *      isLessThanOrEqualTo      lte    |  minimum              min\r\n *      isNaN                           |  random\r\n *      isNegative                      |  sum\r\n *      isPositive                      |\r\n *      isZero                          |\r\n *      minus                           |\r\n *      modulo                   mod    |\r\n *      multipliedBy             times  |\r\n *      negated                         |\r\n *      plus                            |\r\n *      precision                sd     |\r\n *      shiftedBy                       |\r\n *      squareRoot               sqrt   |\r\n *      toExponential                   |\r\n *      toFixed                         |\r\n *      toFormat                        |\r\n *      toFraction                      |\r\n *      toJSON                          |\r\n *      toNumber                        |\r\n *      toPrecision                     |\r\n *      toString                        |\r\n *      valueOf                         |\r\n *\r\n */ var BigNumber, isNumeric = /^-?(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?$/i, mathceil = Math.ceil, mathfloor = Math.floor, bignumberError = \"[BigNumber Error] \", tooManyDigits = bignumberError + \"Number primitive has more than 15 significant digits: \", BASE = 1e14, LOG_BASE = 14, MAX_SAFE_INTEGER = 0x1fffffffffffff, // MAX_INT32 = 0x7fffffff,                   // 2^31 - 1\n    POWS_TEN = [\n        1,\n        10,\n        100,\n        1e3,\n        1e4,\n        1e5,\n        1e6,\n        1e7,\n        1e8,\n        1e9,\n        1e10,\n        1e11,\n        1e12,\n        1e13\n    ], SQRT_BASE = 1e7, // EDITABLE\n    // The limit on the value of DECIMAL_PLACES, TO_EXP_NEG, TO_EXP_POS, MIN_EXP, MAX_EXP, and\n    // the arguments to toExponential, toFixed, toFormat, and toPrecision.\n    MAX = 1E9; // 0 to MAX_INT32\n    /*\r\n   * Create and return a BigNumber constructor.\r\n   */ function clone(configObject) {\n        var div, convertBase, parseNumeric, P = BigNumber.prototype = {\n            constructor: BigNumber,\n            toString: null,\n            valueOf: null\n        }, ONE = new BigNumber(1), //----------------------------- EDITABLE CONFIG DEFAULTS -------------------------------\n        // The default values below must be integers within the inclusive ranges stated.\n        // The values can also be changed at run-time using BigNumber.set.\n        // The maximum number of decimal places for operations involving division.\n        DECIMAL_PLACES = 20, // The rounding mode used when rounding to the above decimal places, and when using\n        // toExponential, toFixed, toFormat and toPrecision, and round (default value).\n        // UP         0 Away from zero.\n        // DOWN       1 Towards zero.\n        // CEIL       2 Towards +Infinity.\n        // FLOOR      3 Towards -Infinity.\n        // HALF_UP    4 Towards nearest neighbour. If equidistant, up.\n        // HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\n        // HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\n        // HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\n        // HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\n        ROUNDING_MODE = 4, // EXPONENTIAL_AT : [TO_EXP_NEG , TO_EXP_POS]\n        // The exponent value at and beneath which toString returns exponential notation.\n        // Number type: -7\n        TO_EXP_NEG = -7, // The exponent value at and above which toString returns exponential notation.\n        // Number type: 21\n        TO_EXP_POS = 21, // RANGE : [MIN_EXP, MAX_EXP]\n        // The minimum exponent value, beneath which underflow to zero occurs.\n        // Number type: -324  (5e-324)\n        MIN_EXP = -1e7, // The maximum exponent value, above which overflow to Infinity occurs.\n        // Number type:  308  (1.7976931348623157e+308)\n        // For MAX_EXP > 1e7, e.g. new BigNumber('1e100000000').plus(1) may be slow.\n        MAX_EXP = 1e7, // Whether to use cryptographically-secure random number generation, if available.\n        CRYPTO = false, // The modulo mode used when calculating the modulus: a mod n.\n        // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\n        // The remainder (r) is calculated as: r = a - n * q.\n        //\n        // UP        0 The remainder is positive if the dividend is negative, else is negative.\n        // DOWN      1 The remainder has the same sign as the dividend.\n        //             This modulo mode is commonly known as 'truncated division' and is\n        //             equivalent to (a % n) in JavaScript.\n        // FLOOR     3 The remainder has the same sign as the divisor (Python %).\n        // HALF_EVEN 6 This modulo mode implements the IEEE 754 remainder function.\n        // EUCLID    9 Euclidian division. q = sign(n) * floor(a / abs(n)).\n        //             The remainder is always positive.\n        //\n        // The truncated division, floored division, Euclidian division and IEEE 754 remainder\n        // modes are commonly used for the modulus operation.\n        // Although the other rounding modes can also be used, they may not give useful results.\n        MODULO_MODE = 1, // The maximum number of significant digits of the result of the exponentiatedBy operation.\n        // If POW_PRECISION is 0, there will be unlimited significant digits.\n        POW_PRECISION = 0, // The format specification used by the BigNumber.prototype.toFormat method.\n        FORMAT = {\n            prefix: \"\",\n            groupSize: 3,\n            secondaryGroupSize: 0,\n            groupSeparator: \",\",\n            decimalSeparator: \".\",\n            fractionGroupSize: 0,\n            fractionGroupSeparator: \"\\xa0\",\n            suffix: \"\"\n        }, // The alphabet used for base conversion. It must be at least 2 characters long, with no '+',\n        // '-', '.', whitespace, or repeated character.\n        // '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_'\n        ALPHABET = \"0123456789abcdefghijklmnopqrstuvwxyz\", alphabetHasNormalDecimalDigits = true;\n        //------------------------------------------------------------------------------------------\n        // CONSTRUCTOR\n        /*\r\n     * The BigNumber constructor and exported function.\r\n     * Create and return a new instance of a BigNumber object.\r\n     *\r\n     * v {number|string|BigNumber} A numeric value.\r\n     * [b] {number} The base of v. Integer, 2 to ALPHABET.length inclusive.\r\n     */ function BigNumber(v, b) {\n            var alphabet, c, caseChanged, e, i, isNum, len, str, x = this;\n            // Enable constructor call without `new`.\n            if (!(x instanceof BigNumber)) return new BigNumber(v, b);\n            if (b == null) {\n                if (v && v._isBigNumber === true) {\n                    x.s = v.s;\n                    if (!v.c || v.e > MAX_EXP) {\n                        x.c = x.e = null;\n                    } else if (v.e < MIN_EXP) {\n                        x.c = [\n                            x.e = 0\n                        ];\n                    } else {\n                        x.e = v.e;\n                        x.c = v.c.slice();\n                    }\n                    return;\n                }\n                if ((isNum = typeof v == \"number\") && v * 0 == 0) {\n                    // Use `1 / n` to handle minus zero also.\n                    x.s = 1 / v < 0 ? (v = -v, -1) : 1;\n                    // Fast path for integers, where n < 2147483648 (2**31).\n                    if (v === ~~v) {\n                        for(e = 0, i = v; i >= 10; i /= 10, e++);\n                        if (e > MAX_EXP) {\n                            x.c = x.e = null;\n                        } else {\n                            x.e = e;\n                            x.c = [\n                                v\n                            ];\n                        }\n                        return;\n                    }\n                    str = String(v);\n                } else {\n                    if (!isNumeric.test(str = String(v))) return parseNumeric(x, str, isNum);\n                    x.s = str.charCodeAt(0) == 45 ? (str = str.slice(1), -1) : 1;\n                }\n                // Decimal point?\n                if ((e = str.indexOf(\".\")) > -1) str = str.replace(\".\", \"\");\n                // Exponential form?\n                if ((i = str.search(/e/i)) > 0) {\n                    // Determine exponent.\n                    if (e < 0) e = i;\n                    e += +str.slice(i + 1);\n                    str = str.substring(0, i);\n                } else if (e < 0) {\n                    // Integer.\n                    e = str.length;\n                }\n            } else {\n                // '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\n                intCheck(b, 2, ALPHABET.length, \"Base\");\n                // Allow exponential notation to be used with base 10 argument, while\n                // also rounding to DECIMAL_PLACES as with other bases.\n                if (b == 10 && alphabetHasNormalDecimalDigits) {\n                    x = new BigNumber(v);\n                    return round(x, DECIMAL_PLACES + x.e + 1, ROUNDING_MODE);\n                }\n                str = String(v);\n                if (isNum = typeof v == \"number\") {\n                    // Avoid potential interpretation of Infinity and NaN as base 44+ values.\n                    if (v * 0 != 0) return parseNumeric(x, str, isNum, b);\n                    x.s = 1 / v < 0 ? (str = str.slice(1), -1) : 1;\n                    // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\n                    if (BigNumber.DEBUG && str.replace(/^0\\.0*|\\./, \"\").length > 15) {\n                        throw Error(tooManyDigits + v);\n                    }\n                } else {\n                    x.s = str.charCodeAt(0) === 45 ? (str = str.slice(1), -1) : 1;\n                }\n                alphabet = ALPHABET.slice(0, b);\n                e = i = 0;\n                // Check that str is a valid base b number.\n                // Don't use RegExp, so alphabet can contain special characters.\n                for(len = str.length; i < len; i++){\n                    if (alphabet.indexOf(c = str.charAt(i)) < 0) {\n                        if (c == \".\") {\n                            // If '.' is not the first character and it has not be found before.\n                            if (i > e) {\n                                e = len;\n                                continue;\n                            }\n                        } else if (!caseChanged) {\n                            // Allow e.g. hexadecimal 'FF' as well as 'ff'.\n                            if (str == str.toUpperCase() && (str = str.toLowerCase()) || str == str.toLowerCase() && (str = str.toUpperCase())) {\n                                caseChanged = true;\n                                i = -1;\n                                e = 0;\n                                continue;\n                            }\n                        }\n                        return parseNumeric(x, String(v), isNum, b);\n                    }\n                }\n                // Prevent later check for length on converted number.\n                isNum = false;\n                str = convertBase(str, b, 10, x.s);\n                // Decimal point?\n                if ((e = str.indexOf(\".\")) > -1) str = str.replace(\".\", \"\");\n                else e = str.length;\n            }\n            // Determine leading zeros.\n            for(i = 0; str.charCodeAt(i) === 48; i++);\n            // Determine trailing zeros.\n            for(len = str.length; str.charCodeAt(--len) === 48;);\n            if (str = str.slice(i, ++len)) {\n                len -= i;\n                // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\n                if (isNum && BigNumber.DEBUG && len > 15 && (v > MAX_SAFE_INTEGER || v !== mathfloor(v))) {\n                    throw Error(tooManyDigits + x.s * v);\n                }\n                // Overflow?\n                if ((e = e - i - 1) > MAX_EXP) {\n                    // Infinity.\n                    x.c = x.e = null;\n                // Underflow?\n                } else if (e < MIN_EXP) {\n                    // Zero.\n                    x.c = [\n                        x.e = 0\n                    ];\n                } else {\n                    x.e = e;\n                    x.c = [];\n                    // Transform base\n                    // e is the base 10 exponent.\n                    // i is where to slice str to get the first element of the coefficient array.\n                    i = (e + 1) % LOG_BASE;\n                    if (e < 0) i += LOG_BASE; // i < 1\n                    if (i < len) {\n                        if (i) x.c.push(+str.slice(0, i));\n                        for(len -= LOG_BASE; i < len;){\n                            x.c.push(+str.slice(i, i += LOG_BASE));\n                        }\n                        i = LOG_BASE - (str = str.slice(i)).length;\n                    } else {\n                        i -= len;\n                    }\n                    for(; i--; str += \"0\");\n                    x.c.push(+str);\n                }\n            } else {\n                // Zero.\n                x.c = [\n                    x.e = 0\n                ];\n            }\n        }\n        // CONSTRUCTOR PROPERTIES\n        BigNumber.clone = clone;\n        BigNumber.ROUND_UP = 0;\n        BigNumber.ROUND_DOWN = 1;\n        BigNumber.ROUND_CEIL = 2;\n        BigNumber.ROUND_FLOOR = 3;\n        BigNumber.ROUND_HALF_UP = 4;\n        BigNumber.ROUND_HALF_DOWN = 5;\n        BigNumber.ROUND_HALF_EVEN = 6;\n        BigNumber.ROUND_HALF_CEIL = 7;\n        BigNumber.ROUND_HALF_FLOOR = 8;\n        BigNumber.EUCLID = 9;\n        /*\r\n     * Configure infrequently-changing library-wide settings.\r\n     *\r\n     * Accept an object with the following optional properties (if the value of a property is\r\n     * a number, it must be an integer within the inclusive range stated):\r\n     *\r\n     *   DECIMAL_PLACES   {number}           0 to MAX\r\n     *   ROUNDING_MODE    {number}           0 to 8\r\n     *   EXPONENTIAL_AT   {number|number[]}  -MAX to MAX  or  [-MAX to 0, 0 to MAX]\r\n     *   RANGE            {number|number[]}  -MAX to MAX (not zero)  or  [-MAX to -1, 1 to MAX]\r\n     *   CRYPTO           {boolean}          true or false\r\n     *   MODULO_MODE      {number}           0 to 9\r\n     *   POW_PRECISION       {number}           0 to MAX\r\n     *   ALPHABET         {string}           A string of two or more unique characters which does\r\n     *                                       not contain '.'.\r\n     *   FORMAT           {object}           An object with some of the following properties:\r\n     *     prefix                 {string}\r\n     *     groupSize              {number}\r\n     *     secondaryGroupSize     {number}\r\n     *     groupSeparator         {string}\r\n     *     decimalSeparator       {string}\r\n     *     fractionGroupSize      {number}\r\n     *     fractionGroupSeparator {string}\r\n     *     suffix                 {string}\r\n     *\r\n     * (The values assigned to the above FORMAT object properties are not checked for validity.)\r\n     *\r\n     * E.g.\r\n     * BigNumber.config({ DECIMAL_PLACES : 20, ROUNDING_MODE : 4 })\r\n     *\r\n     * Ignore properties/parameters set to null or undefined, except for ALPHABET.\r\n     *\r\n     * Return an object with the properties current values.\r\n     */ BigNumber.config = BigNumber.set = function(obj) {\n            var p, v;\n            if (obj != null) {\n                if (typeof obj == \"object\") {\n                    // DECIMAL_PLACES {number} Integer, 0 to MAX inclusive.\n                    // '[BigNumber Error] DECIMAL_PLACES {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"DECIMAL_PLACES\")) {\n                        v = obj[p];\n                        intCheck(v, 0, MAX, p);\n                        DECIMAL_PLACES = v;\n                    }\n                    // ROUNDING_MODE {number} Integer, 0 to 8 inclusive.\n                    // '[BigNumber Error] ROUNDING_MODE {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"ROUNDING_MODE\")) {\n                        v = obj[p];\n                        intCheck(v, 0, 8, p);\n                        ROUNDING_MODE = v;\n                    }\n                    // EXPONENTIAL_AT {number|number[]}\n                    // Integer, -MAX to MAX inclusive or\n                    // [integer -MAX to 0 inclusive, 0 to MAX inclusive].\n                    // '[BigNumber Error] EXPONENTIAL_AT {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"EXPONENTIAL_AT\")) {\n                        v = obj[p];\n                        if (v && v.pop) {\n                            intCheck(v[0], -MAX, 0, p);\n                            intCheck(v[1], 0, MAX, p);\n                            TO_EXP_NEG = v[0];\n                            TO_EXP_POS = v[1];\n                        } else {\n                            intCheck(v, -MAX, MAX, p);\n                            TO_EXP_NEG = -(TO_EXP_POS = v < 0 ? -v : v);\n                        }\n                    }\n                    // RANGE {number|number[]} Non-zero integer, -MAX to MAX inclusive or\n                    // [integer -MAX to -1 inclusive, integer 1 to MAX inclusive].\n                    // '[BigNumber Error] RANGE {not a primitive number|not an integer|out of range|cannot be zero}: {v}'\n                    if (obj.hasOwnProperty(p = \"RANGE\")) {\n                        v = obj[p];\n                        if (v && v.pop) {\n                            intCheck(v[0], -MAX, -1, p);\n                            intCheck(v[1], 1, MAX, p);\n                            MIN_EXP = v[0];\n                            MAX_EXP = v[1];\n                        } else {\n                            intCheck(v, -MAX, MAX, p);\n                            if (v) {\n                                MIN_EXP = -(MAX_EXP = v < 0 ? -v : v);\n                            } else {\n                                throw Error(bignumberError + p + \" cannot be zero: \" + v);\n                            }\n                        }\n                    }\n                    // CRYPTO {boolean} true or false.\n                    // '[BigNumber Error] CRYPTO not true or false: {v}'\n                    // '[BigNumber Error] crypto unavailable'\n                    if (obj.hasOwnProperty(p = \"CRYPTO\")) {\n                        v = obj[p];\n                        if (v === !!v) {\n                            if (v) {\n                                if (typeof crypto != \"undefined\" && crypto && (crypto.getRandomValues || crypto.randomBytes)) {\n                                    CRYPTO = v;\n                                } else {\n                                    CRYPTO = !v;\n                                    throw Error(bignumberError + \"crypto unavailable\");\n                                }\n                            } else {\n                                CRYPTO = v;\n                            }\n                        } else {\n                            throw Error(bignumberError + p + \" not true or false: \" + v);\n                        }\n                    }\n                    // MODULO_MODE {number} Integer, 0 to 9 inclusive.\n                    // '[BigNumber Error] MODULO_MODE {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"MODULO_MODE\")) {\n                        v = obj[p];\n                        intCheck(v, 0, 9, p);\n                        MODULO_MODE = v;\n                    }\n                    // POW_PRECISION {number} Integer, 0 to MAX inclusive.\n                    // '[BigNumber Error] POW_PRECISION {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"POW_PRECISION\")) {\n                        v = obj[p];\n                        intCheck(v, 0, MAX, p);\n                        POW_PRECISION = v;\n                    }\n                    // FORMAT {object}\n                    // '[BigNumber Error] FORMAT not an object: {v}'\n                    if (obj.hasOwnProperty(p = \"FORMAT\")) {\n                        v = obj[p];\n                        if (typeof v == \"object\") FORMAT = v;\n                        else throw Error(bignumberError + p + \" not an object: \" + v);\n                    }\n                    // ALPHABET {string}\n                    // '[BigNumber Error] ALPHABET invalid: {v}'\n                    if (obj.hasOwnProperty(p = \"ALPHABET\")) {\n                        v = obj[p];\n                        // Disallow if less than two characters,\n                        // or if it contains '+', '-', '.', whitespace, or a repeated character.\n                        if (typeof v == \"string\" && !/^.?$|[+\\-.\\s]|(.).*\\1/.test(v)) {\n                            alphabetHasNormalDecimalDigits = v.slice(0, 10) == \"0123456789\";\n                            ALPHABET = v;\n                        } else {\n                            throw Error(bignumberError + p + \" invalid: \" + v);\n                        }\n                    }\n                } else {\n                    // '[BigNumber Error] Object expected: {v}'\n                    throw Error(bignumberError + \"Object expected: \" + obj);\n                }\n            }\n            return {\n                DECIMAL_PLACES: DECIMAL_PLACES,\n                ROUNDING_MODE: ROUNDING_MODE,\n                EXPONENTIAL_AT: [\n                    TO_EXP_NEG,\n                    TO_EXP_POS\n                ],\n                RANGE: [\n                    MIN_EXP,\n                    MAX_EXP\n                ],\n                CRYPTO: CRYPTO,\n                MODULO_MODE: MODULO_MODE,\n                POW_PRECISION: POW_PRECISION,\n                FORMAT: FORMAT,\n                ALPHABET: ALPHABET\n            };\n        };\n        /*\r\n     * Return true if v is a BigNumber instance, otherwise return false.\r\n     *\r\n     * If BigNumber.DEBUG is true, throw if a BigNumber instance is not well-formed.\r\n     *\r\n     * v {any}\r\n     *\r\n     * '[BigNumber Error] Invalid BigNumber: {v}'\r\n     */ BigNumber.isBigNumber = function(v) {\n            if (!v || v._isBigNumber !== true) return false;\n            if (!BigNumber.DEBUG) return true;\n            var i, n, c = v.c, e = v.e, s = v.s;\n            out: if (({}).toString.call(c) == \"[object Array]\") {\n                if ((s === 1 || s === -1) && e >= -MAX && e <= MAX && e === mathfloor(e)) {\n                    // If the first element is zero, the BigNumber value must be zero.\n                    if (c[0] === 0) {\n                        if (e === 0 && c.length === 1) return true;\n                        break out;\n                    }\n                    // Calculate number of digits that c[0] should have, based on the exponent.\n                    i = (e + 1) % LOG_BASE;\n                    if (i < 1) i += LOG_BASE;\n                    // Calculate number of digits of c[0].\n                    //if (Math.ceil(Math.log(c[0] + 1) / Math.LN10) == i) {\n                    if (String(c[0]).length == i) {\n                        for(i = 0; i < c.length; i++){\n                            n = c[i];\n                            if (n < 0 || n >= BASE || n !== mathfloor(n)) break out;\n                        }\n                        // Last element cannot be zero, unless it is the only element.\n                        if (n !== 0) return true;\n                    }\n                }\n            // Infinity/NaN\n            } else if (c === null && e === null && (s === null || s === 1 || s === -1)) {\n                return true;\n            }\n            throw Error(bignumberError + \"Invalid BigNumber: \" + v);\n        };\n        /*\r\n     * Return a new BigNumber whose value is the maximum of the arguments.\r\n     *\r\n     * arguments {number|string|BigNumber}\r\n     */ BigNumber.maximum = BigNumber.max = function() {\n            return maxOrMin(arguments, -1);\n        };\n        /*\r\n     * Return a new BigNumber whose value is the minimum of the arguments.\r\n     *\r\n     * arguments {number|string|BigNumber}\r\n     */ BigNumber.minimum = BigNumber.min = function() {\n            return maxOrMin(arguments, 1);\n        };\n        /*\r\n     * Return a new BigNumber with a random value equal to or greater than 0 and less than 1,\r\n     * and with dp, or DECIMAL_PLACES if dp is omitted, decimal places (or less if trailing\r\n     * zeros are produced).\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp}'\r\n     * '[BigNumber Error] crypto unavailable'\r\n     */ BigNumber.random = function() {\n            var pow2_53 = 0x20000000000000;\n            // Return a 53 bit integer n, where 0 <= n < 9007199254740992.\n            // Check if Math.random() produces more than 32 bits of randomness.\n            // If it does, assume at least 53 bits are produced, otherwise assume at least 30 bits.\n            // 0x40000000 is 2^30, 0x800000 is 2^23, 0x1fffff is 2^21 - 1.\n            var random53bitInt = Math.random() * pow2_53 & 0x1fffff ? function() {\n                return mathfloor(Math.random() * pow2_53);\n            } : function() {\n                return (Math.random() * 0x40000000 | 0) * 0x800000 + (Math.random() * 0x800000 | 0);\n            };\n            return function(dp) {\n                var a, b, e, k, v, i = 0, c = [], rand = new BigNumber(ONE);\n                if (dp == null) dp = DECIMAL_PLACES;\n                else intCheck(dp, 0, MAX);\n                k = mathceil(dp / LOG_BASE);\n                if (CRYPTO) {\n                    // Browsers supporting crypto.getRandomValues.\n                    if (crypto.getRandomValues) {\n                        a = crypto.getRandomValues(new Uint32Array(k *= 2));\n                        for(; i < k;){\n                            // 53 bits:\n                            // ((Math.pow(2, 32) - 1) * Math.pow(2, 21)).toString(2)\n                            // 11111 11111111 11111111 11111111 11100000 00000000 00000000\n                            // ((Math.pow(2, 32) - 1) >>> 11).toString(2)\n                            //                                     11111 11111111 11111111\n                            // 0x20000 is 2^21.\n                            v = a[i] * 0x20000 + (a[i + 1] >>> 11);\n                            // Rejection sampling:\n                            // 0 <= v < 9007199254740992\n                            // Probability that v >= 9e15, is\n                            // 7199254740992 / 9007199254740992 ~= 0.0008, i.e. 1 in 1251\n                            if (v >= 9e15) {\n                                b = crypto.getRandomValues(new Uint32Array(2));\n                                a[i] = b[0];\n                                a[i + 1] = b[1];\n                            } else {\n                                // 0 <= v <= 8999999999999999\n                                // 0 <= (v % 1e14) <= 99999999999999\n                                c.push(v % 1e14);\n                                i += 2;\n                            }\n                        }\n                        i = k / 2;\n                    // Node.js supporting crypto.randomBytes.\n                    } else if (crypto.randomBytes) {\n                        // buffer\n                        a = crypto.randomBytes(k *= 7);\n                        for(; i < k;){\n                            // 0x1000000000000 is 2^48, 0x10000000000 is 2^40\n                            // 0x100000000 is 2^32, 0x1000000 is 2^24\n                            // 11111 11111111 11111111 11111111 11111111 11111111 11111111\n                            // 0 <= v < 9007199254740992\n                            v = (a[i] & 31) * 0x1000000000000 + a[i + 1] * 0x10000000000 + a[i + 2] * 0x100000000 + a[i + 3] * 0x1000000 + (a[i + 4] << 16) + (a[i + 5] << 8) + a[i + 6];\n                            if (v >= 9e15) {\n                                crypto.randomBytes(7).copy(a, i);\n                            } else {\n                                // 0 <= (v % 1e14) <= 99999999999999\n                                c.push(v % 1e14);\n                                i += 7;\n                            }\n                        }\n                        i = k / 7;\n                    } else {\n                        CRYPTO = false;\n                        throw Error(bignumberError + \"crypto unavailable\");\n                    }\n                }\n                // Use Math.random.\n                if (!CRYPTO) {\n                    for(; i < k;){\n                        v = random53bitInt();\n                        if (v < 9e15) c[i++] = v % 1e14;\n                    }\n                }\n                k = c[--i];\n                dp %= LOG_BASE;\n                // Convert trailing digits to zeros according to dp.\n                if (k && dp) {\n                    v = POWS_TEN[LOG_BASE - dp];\n                    c[i] = mathfloor(k / v) * v;\n                }\n                // Remove trailing elements which are zero.\n                for(; c[i] === 0; c.pop(), i--);\n                // Zero?\n                if (i < 0) {\n                    c = [\n                        e = 0\n                    ];\n                } else {\n                    // Remove leading elements which are zero and adjust exponent accordingly.\n                    for(e = -1; c[0] === 0; c.splice(0, 1), e -= LOG_BASE);\n                    // Count the digits of the first element of c to determine leading zeros, and...\n                    for(i = 1, v = c[0]; v >= 10; v /= 10, i++);\n                    // adjust the exponent accordingly.\n                    if (i < LOG_BASE) e -= LOG_BASE - i;\n                }\n                rand.e = e;\n                rand.c = c;\n                return rand;\n            };\n        }();\n        /*\r\n     * Return a BigNumber whose value is the sum of the arguments.\r\n     *\r\n     * arguments {number|string|BigNumber}\r\n     */ BigNumber.sum = function() {\n            var i = 1, args = arguments, sum = new BigNumber(args[0]);\n            for(; i < args.length;)sum = sum.plus(args[i++]);\n            return sum;\n        };\n        // PRIVATE FUNCTIONS\n        // Called by BigNumber and BigNumber.prototype.toString.\n        convertBase = function() {\n            var decimal = \"0123456789\";\n            /*\r\n       * Convert string of baseIn to an array of numbers of baseOut.\r\n       * Eg. toBaseOut('255', 10, 16) returns [15, 15].\r\n       * Eg. toBaseOut('ff', 16, 10) returns [2, 5, 5].\r\n       */ function toBaseOut(str, baseIn, baseOut, alphabet) {\n                var j, arr = [\n                    0\n                ], arrL, i = 0, len = str.length;\n                for(; i < len;){\n                    for(arrL = arr.length; arrL--; arr[arrL] *= baseIn);\n                    arr[0] += alphabet.indexOf(str.charAt(i++));\n                    for(j = 0; j < arr.length; j++){\n                        if (arr[j] > baseOut - 1) {\n                            if (arr[j + 1] == null) arr[j + 1] = 0;\n                            arr[j + 1] += arr[j] / baseOut | 0;\n                            arr[j] %= baseOut;\n                        }\n                    }\n                }\n                return arr.reverse();\n            }\n            // Convert a numeric string of baseIn to a numeric string of baseOut.\n            // If the caller is toString, we are converting from base 10 to baseOut.\n            // If the caller is BigNumber, we are converting from baseIn to base 10.\n            return function(str, baseIn, baseOut, sign, callerIsToString) {\n                var alphabet, d, e, k, r, x, xc, y, i = str.indexOf(\".\"), dp = DECIMAL_PLACES, rm = ROUNDING_MODE;\n                // Non-integer.\n                if (i >= 0) {\n                    k = POW_PRECISION;\n                    // Unlimited precision.\n                    POW_PRECISION = 0;\n                    str = str.replace(\".\", \"\");\n                    y = new BigNumber(baseIn);\n                    x = y.pow(str.length - i);\n                    POW_PRECISION = k;\n                    // Convert str as if an integer, then restore the fraction part by dividing the\n                    // result by its base raised to a power.\n                    y.c = toBaseOut(toFixedPoint(coeffToString(x.c), x.e, \"0\"), 10, baseOut, decimal);\n                    y.e = y.c.length;\n                }\n                // Convert the number as integer.\n                xc = toBaseOut(str, baseIn, baseOut, callerIsToString ? (alphabet = ALPHABET, decimal) : (alphabet = decimal, ALPHABET));\n                // xc now represents str as an integer and converted to baseOut. e is the exponent.\n                e = k = xc.length;\n                // Remove trailing zeros.\n                for(; xc[--k] == 0; xc.pop());\n                // Zero?\n                if (!xc[0]) return alphabet.charAt(0);\n                // Does str represent an integer? If so, no need for the division.\n                if (i < 0) {\n                    --e;\n                } else {\n                    x.c = xc;\n                    x.e = e;\n                    // The sign is needed for correct rounding.\n                    x.s = sign;\n                    x = div(x, y, dp, rm, baseOut);\n                    xc = x.c;\n                    r = x.r;\n                    e = x.e;\n                }\n                // xc now represents str converted to baseOut.\n                // The index of the rounding digit.\n                d = e + dp + 1;\n                // The rounding digit: the digit to the right of the digit that may be rounded up.\n                i = xc[d];\n                // Look at the rounding digits and mode to determine whether to round up.\n                k = baseOut / 2;\n                r = r || d < 0 || xc[d + 1] != null;\n                r = rm < 4 ? (i != null || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : i > k || i == k && (rm == 4 || r || rm == 6 && xc[d - 1] & 1 || rm == (x.s < 0 ? 8 : 7));\n                // If the index of the rounding digit is not greater than zero, or xc represents\n                // zero, then the result of the base conversion is zero or, if rounding up, a value\n                // such as 0.00001.\n                if (d < 1 || !xc[0]) {\n                    // 1^-dp or 0\n                    str = r ? toFixedPoint(alphabet.charAt(1), -dp, alphabet.charAt(0)) : alphabet.charAt(0);\n                } else {\n                    // Truncate xc to the required number of decimal places.\n                    xc.length = d;\n                    // Round up?\n                    if (r) {\n                        // Rounding up may mean the previous digit has to be rounded up and so on.\n                        for(--baseOut; ++xc[--d] > baseOut;){\n                            xc[d] = 0;\n                            if (!d) {\n                                ++e;\n                                xc = [\n                                    1\n                                ].concat(xc);\n                            }\n                        }\n                    }\n                    // Determine trailing zeros.\n                    for(k = xc.length; !xc[--k];);\n                    // E.g. [4, 11, 15] becomes 4bf.\n                    for(i = 0, str = \"\"; i <= k; str += alphabet.charAt(xc[i++]));\n                    // Add leading zeros, decimal point and trailing zeros as required.\n                    str = toFixedPoint(str, e, alphabet.charAt(0));\n                }\n                // The caller will add the sign.\n                return str;\n            };\n        }();\n        // Perform division in the specified base. Called by div and convertBase.\n        div = function() {\n            // Assume non-zero x and k.\n            function multiply(x, k, base) {\n                var m, temp, xlo, xhi, carry = 0, i = x.length, klo = k % SQRT_BASE, khi = k / SQRT_BASE | 0;\n                for(x = x.slice(); i--;){\n                    xlo = x[i] % SQRT_BASE;\n                    xhi = x[i] / SQRT_BASE | 0;\n                    m = khi * xlo + xhi * klo;\n                    temp = klo * xlo + m % SQRT_BASE * SQRT_BASE + carry;\n                    carry = (temp / base | 0) + (m / SQRT_BASE | 0) + khi * xhi;\n                    x[i] = temp % base;\n                }\n                if (carry) x = [\n                    carry\n                ].concat(x);\n                return x;\n            }\n            function compare(a, b, aL, bL) {\n                var i, cmp;\n                if (aL != bL) {\n                    cmp = aL > bL ? 1 : -1;\n                } else {\n                    for(i = cmp = 0; i < aL; i++){\n                        if (a[i] != b[i]) {\n                            cmp = a[i] > b[i] ? 1 : -1;\n                            break;\n                        }\n                    }\n                }\n                return cmp;\n            }\n            function subtract(a, b, aL, base) {\n                var i = 0;\n                // Subtract b from a.\n                for(; aL--;){\n                    a[aL] -= i;\n                    i = a[aL] < b[aL] ? 1 : 0;\n                    a[aL] = i * base + a[aL] - b[aL];\n                }\n                // Remove leading zeros.\n                for(; !a[0] && a.length > 1; a.splice(0, 1));\n            }\n            // x: dividend, y: divisor.\n            return function(x, y, dp, rm, base) {\n                var cmp, e, i, more, n, prod, prodL, q, qc, rem, remL, rem0, xi, xL, yc0, yL, yz, s = x.s == y.s ? 1 : -1, xc = x.c, yc = y.c;\n                // Either NaN, Infinity or 0?\n                if (!xc || !xc[0] || !yc || !yc[0]) {\n                    return new BigNumber(// Return NaN if either NaN, or both Infinity or 0.\n                    !x.s || !y.s || (xc ? yc && xc[0] == yc[0] : !yc) ? NaN : // Return ±0 if x is ±0 or y is ±Infinity, or return ±Infinity as y is ±0.\n                    xc && xc[0] == 0 || !yc ? s * 0 : s / 0);\n                }\n                q = new BigNumber(s);\n                qc = q.c = [];\n                e = x.e - y.e;\n                s = dp + e + 1;\n                if (!base) {\n                    base = BASE;\n                    e = bitFloor(x.e / LOG_BASE) - bitFloor(y.e / LOG_BASE);\n                    s = s / LOG_BASE | 0;\n                }\n                // Result exponent may be one less then the current value of e.\n                // The coefficients of the BigNumbers from convertBase may have trailing zeros.\n                for(i = 0; yc[i] == (xc[i] || 0); i++);\n                if (yc[i] > (xc[i] || 0)) e--;\n                if (s < 0) {\n                    qc.push(1);\n                    more = true;\n                } else {\n                    xL = xc.length;\n                    yL = yc.length;\n                    i = 0;\n                    s += 2;\n                    // Normalise xc and yc so highest order digit of yc is >= base / 2.\n                    n = mathfloor(base / (yc[0] + 1));\n                    // Not necessary, but to handle odd bases where yc[0] == (base / 2) - 1.\n                    // if (n > 1 || n++ == 1 && yc[0] < base / 2) {\n                    if (n > 1) {\n                        yc = multiply(yc, n, base);\n                        xc = multiply(xc, n, base);\n                        yL = yc.length;\n                        xL = xc.length;\n                    }\n                    xi = yL;\n                    rem = xc.slice(0, yL);\n                    remL = rem.length;\n                    // Add zeros to make remainder as long as divisor.\n                    for(; remL < yL; rem[remL++] = 0);\n                    yz = yc.slice();\n                    yz = [\n                        0\n                    ].concat(yz);\n                    yc0 = yc[0];\n                    if (yc[1] >= base / 2) yc0++;\n                    // Not necessary, but to prevent trial digit n > base, when using base 3.\n                    // else if (base == 3 && yc0 == 1) yc0 = 1 + 1e-15;\n                    do {\n                        n = 0;\n                        // Compare divisor and remainder.\n                        cmp = compare(yc, rem, yL, remL);\n                        // If divisor < remainder.\n                        if (cmp < 0) {\n                            // Calculate trial digit, n.\n                            rem0 = rem[0];\n                            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\n                            // n is how many times the divisor goes into the current remainder.\n                            n = mathfloor(rem0 / yc0);\n                            //  Algorithm:\n                            //  product = divisor multiplied by trial digit (n).\n                            //  Compare product and remainder.\n                            //  If product is greater than remainder:\n                            //    Subtract divisor from product, decrement trial digit.\n                            //  Subtract product from remainder.\n                            //  If product was less than remainder at the last compare:\n                            //    Compare new remainder and divisor.\n                            //    If remainder is greater than divisor:\n                            //      Subtract divisor from remainder, increment trial digit.\n                            if (n > 1) {\n                                // n may be > base only when base is 3.\n                                if (n >= base) n = base - 1;\n                                // product = divisor * trial digit.\n                                prod = multiply(yc, n, base);\n                                prodL = prod.length;\n                                remL = rem.length;\n                                // Compare product and remainder.\n                                // If product > remainder then trial digit n too high.\n                                // n is 1 too high about 5% of the time, and is not known to have\n                                // ever been more than 1 too high.\n                                while(compare(prod, rem, prodL, remL) == 1){\n                                    n--;\n                                    // Subtract divisor from product.\n                                    subtract(prod, yL < prodL ? yz : yc, prodL, base);\n                                    prodL = prod.length;\n                                    cmp = 1;\n                                }\n                            } else {\n                                // n is 0 or 1, cmp is -1.\n                                // If n is 0, there is no need to compare yc and rem again below,\n                                // so change cmp to 1 to avoid it.\n                                // If n is 1, leave cmp as -1, so yc and rem are compared again.\n                                if (n == 0) {\n                                    // divisor < remainder, so n must be at least 1.\n                                    cmp = n = 1;\n                                }\n                                // product = divisor\n                                prod = yc.slice();\n                                prodL = prod.length;\n                            }\n                            if (prodL < remL) prod = [\n                                0\n                            ].concat(prod);\n                            // Subtract product from remainder.\n                            subtract(rem, prod, remL, base);\n                            remL = rem.length;\n                            // If product was < remainder.\n                            if (cmp == -1) {\n                                // Compare divisor and new remainder.\n                                // If divisor < new remainder, subtract divisor from remainder.\n                                // Trial digit n too low.\n                                // n is 1 too low about 5% of the time, and very rarely 2 too low.\n                                while(compare(yc, rem, yL, remL) < 1){\n                                    n++;\n                                    // Subtract divisor from remainder.\n                                    subtract(rem, yL < remL ? yz : yc, remL, base);\n                                    remL = rem.length;\n                                }\n                            }\n                        } else if (cmp === 0) {\n                            n++;\n                            rem = [\n                                0\n                            ];\n                        } // else cmp === 1 and n will be 0\n                        // Add the next digit, n, to the result array.\n                        qc[i++] = n;\n                        // Update the remainder.\n                        if (rem[0]) {\n                            rem[remL++] = xc[xi] || 0;\n                        } else {\n                            rem = [\n                                xc[xi]\n                            ];\n                            remL = 1;\n                        }\n                    }while ((xi++ < xL || rem[0] != null) && s--);\n                    more = rem[0] != null;\n                    // Leading zero?\n                    if (!qc[0]) qc.splice(0, 1);\n                }\n                if (base == BASE) {\n                    // To calculate q.e, first get the number of digits of qc[0].\n                    for(i = 1, s = qc[0]; s >= 10; s /= 10, i++);\n                    round(q, dp + (q.e = i + e * LOG_BASE - 1) + 1, rm, more);\n                // Caller is convertBase.\n                } else {\n                    q.e = e;\n                    q.r = +more;\n                }\n                return q;\n            };\n        }();\n        /*\r\n     * Return a string representing the value of BigNumber n in fixed-point or exponential\r\n     * notation rounded to the specified decimal places or significant digits.\r\n     *\r\n     * n: a BigNumber.\r\n     * i: the index of the last digit required (i.e. the digit that may be rounded up).\r\n     * rm: the rounding mode.\r\n     * id: 1 (toExponential) or 2 (toPrecision).\r\n     */ function format(n, i, rm, id) {\n            var c0, e, ne, len, str;\n            if (rm == null) rm = ROUNDING_MODE;\n            else intCheck(rm, 0, 8);\n            if (!n.c) return n.toString();\n            c0 = n.c[0];\n            ne = n.e;\n            if (i == null) {\n                str = coeffToString(n.c);\n                str = id == 1 || id == 2 && (ne <= TO_EXP_NEG || ne >= TO_EXP_POS) ? toExponential(str, ne) : toFixedPoint(str, ne, \"0\");\n            } else {\n                n = round(new BigNumber(n), i, rm);\n                // n.e may have changed if the value was rounded up.\n                e = n.e;\n                str = coeffToString(n.c);\n                len = str.length;\n                // toPrecision returns exponential notation if the number of significant digits\n                // specified is less than the number of digits necessary to represent the integer\n                // part of the value in fixed-point notation.\n                // Exponential notation.\n                if (id == 1 || id == 2 && (i <= e || e <= TO_EXP_NEG)) {\n                    // Append zeros?\n                    for(; len < i; str += \"0\", len++);\n                    str = toExponential(str, e);\n                // Fixed-point notation.\n                } else {\n                    i -= ne;\n                    str = toFixedPoint(str, e, \"0\");\n                    // Append zeros?\n                    if (e + 1 > len) {\n                        if (--i > 0) for(str += \".\"; i--; str += \"0\");\n                    } else {\n                        i += e - len;\n                        if (i > 0) {\n                            if (e + 1 == len) str += \".\";\n                            for(; i--; str += \"0\");\n                        }\n                    }\n                }\n            }\n            return n.s < 0 && c0 ? \"-\" + str : str;\n        }\n        // Handle BigNumber.max and BigNumber.min.\n        // If any number is NaN, return NaN.\n        function maxOrMin(args, n) {\n            var k, y, i = 1, x = new BigNumber(args[0]);\n            for(; i < args.length; i++){\n                y = new BigNumber(args[i]);\n                if (!y.s || (k = compare(x, y)) === n || k === 0 && x.s === n) {\n                    x = y;\n                }\n            }\n            return x;\n        }\n        /*\r\n     * Strip trailing zeros, calculate base 10 exponent and check against MIN_EXP and MAX_EXP.\r\n     * Called by minus, plus and times.\r\n     */ function normalise(n, c, e) {\n            var i = 1, j = c.length;\n            // Remove trailing zeros.\n            for(; !c[--j]; c.pop());\n            // Calculate the base 10 exponent. First get the number of digits of c[0].\n            for(j = c[0]; j >= 10; j /= 10, i++);\n            // Overflow?\n            if ((e = i + e * LOG_BASE - 1) > MAX_EXP) {\n                // Infinity.\n                n.c = n.e = null;\n            // Underflow?\n            } else if (e < MIN_EXP) {\n                // Zero.\n                n.c = [\n                    n.e = 0\n                ];\n            } else {\n                n.e = e;\n                n.c = c;\n            }\n            return n;\n        }\n        // Handle values that fail the validity test in BigNumber.\n        parseNumeric = function() {\n            var basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i, dotAfter = /^([^.]+)\\.$/, dotBefore = /^\\.([^.]+)$/, isInfinityOrNaN = /^-?(Infinity|NaN)$/, whitespaceOrPlus = /^\\s*\\+(?=[\\w.])|^\\s+|\\s+$/g;\n            return function(x, str, isNum, b) {\n                var base, s = isNum ? str : str.replace(whitespaceOrPlus, \"\");\n                // No exception on ±Infinity or NaN.\n                if (isInfinityOrNaN.test(s)) {\n                    x.s = isNaN(s) ? null : s < 0 ? -1 : 1;\n                } else {\n                    if (!isNum) {\n                        // basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i\n                        s = s.replace(basePrefix, function(m, p1, p2) {\n                            base = (p2 = p2.toLowerCase()) == \"x\" ? 16 : p2 == \"b\" ? 2 : 8;\n                            return !b || b == base ? p1 : m;\n                        });\n                        if (b) {\n                            base = b;\n                            // E.g. '1.' to '1', '.1' to '0.1'\n                            s = s.replace(dotAfter, \"$1\").replace(dotBefore, \"0.$1\");\n                        }\n                        if (str != s) return new BigNumber(s, base);\n                    }\n                    // '[BigNumber Error] Not a number: {n}'\n                    // '[BigNumber Error] Not a base {b} number: {n}'\n                    if (BigNumber.DEBUG) {\n                        throw Error(bignumberError + \"Not a\" + (b ? \" base \" + b : \"\") + \" number: \" + str);\n                    }\n                    // NaN\n                    x.s = null;\n                }\n                x.c = x.e = null;\n            };\n        }();\n        /*\r\n     * Round x to sd significant digits using rounding mode rm. Check for over/under-flow.\r\n     * If r is truthy, it is known that there are more digits after the rounding digit.\r\n     */ function round(x, sd, rm, r) {\n            var d, i, j, k, n, ni, rd, xc = x.c, pows10 = POWS_TEN;\n            // if x is not Infinity or NaN...\n            if (xc) {\n                // rd is the rounding digit, i.e. the digit after the digit that may be rounded up.\n                // n is a base 1e14 number, the value of the element of array x.c containing rd.\n                // ni is the index of n within x.c.\n                // d is the number of digits of n.\n                // i is the index of rd within n including leading zeros.\n                // j is the actual index of rd within n (if < 0, rd is a leading zero).\n                out: {\n                    // Get the number of digits of the first element of xc.\n                    for(d = 1, k = xc[0]; k >= 10; k /= 10, d++);\n                    i = sd - d;\n                    // If the rounding digit is in the first element of xc...\n                    if (i < 0) {\n                        i += LOG_BASE;\n                        j = sd;\n                        n = xc[ni = 0];\n                        // Get the rounding digit at index j of n.\n                        rd = mathfloor(n / pows10[d - j - 1] % 10);\n                    } else {\n                        ni = mathceil((i + 1) / LOG_BASE);\n                        if (ni >= xc.length) {\n                            if (r) {\n                                // Needed by sqrt.\n                                for(; xc.length <= ni; xc.push(0));\n                                n = rd = 0;\n                                d = 1;\n                                i %= LOG_BASE;\n                                j = i - LOG_BASE + 1;\n                            } else {\n                                break out;\n                            }\n                        } else {\n                            n = k = xc[ni];\n                            // Get the number of digits of n.\n                            for(d = 1; k >= 10; k /= 10, d++);\n                            // Get the index of rd within n.\n                            i %= LOG_BASE;\n                            // Get the index of rd within n, adjusted for leading zeros.\n                            // The number of leading zeros of n is given by LOG_BASE - d.\n                            j = i - LOG_BASE + d;\n                            // Get the rounding digit at index j of n.\n                            rd = j < 0 ? 0 : mathfloor(n / pows10[d - j - 1] % 10);\n                        }\n                    }\n                    r = r || sd < 0 || // Are there any non-zero digits after the rounding digit?\n                    // The expression  n % pows10[d - j - 1]  returns all digits of n to the right\n                    // of the digit at j, e.g. if n is 908714 and j is 2, the expression gives 714.\n                    xc[ni + 1] != null || (j < 0 ? n : n % pows10[d - j - 1]);\n                    r = rm < 4 ? (rd || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : rd > 5 || rd == 5 && (rm == 4 || r || rm == 6 && (i > 0 ? j > 0 ? n / pows10[d - j] : 0 : xc[ni - 1]) % 10 & 1 || rm == (x.s < 0 ? 8 : 7));\n                    if (sd < 1 || !xc[0]) {\n                        xc.length = 0;\n                        if (r) {\n                            // Convert sd to decimal places.\n                            sd -= x.e + 1;\n                            // 1, 0.1, 0.01, 0.001, 0.0001 etc.\n                            xc[0] = pows10[(LOG_BASE - sd % LOG_BASE) % LOG_BASE];\n                            x.e = -sd || 0;\n                        } else {\n                            // Zero.\n                            xc[0] = x.e = 0;\n                        }\n                        return x;\n                    }\n                    // Remove excess digits.\n                    if (i == 0) {\n                        xc.length = ni;\n                        k = 1;\n                        ni--;\n                    } else {\n                        xc.length = ni + 1;\n                        k = pows10[LOG_BASE - i];\n                        // E.g. 56700 becomes 56000 if 7 is the rounding digit.\n                        // j > 0 means i > number of leading zeros of n.\n                        xc[ni] = j > 0 ? mathfloor(n / pows10[d - j] % pows10[j]) * k : 0;\n                    }\n                    // Round up?\n                    if (r) {\n                        for(;;){\n                            // If the digit to be rounded up is in the first element of xc...\n                            if (ni == 0) {\n                                // i will be the length of xc[0] before k is added.\n                                for(i = 1, j = xc[0]; j >= 10; j /= 10, i++);\n                                j = xc[0] += k;\n                                for(k = 1; j >= 10; j /= 10, k++);\n                                // if i != k the length has increased.\n                                if (i != k) {\n                                    x.e++;\n                                    if (xc[0] == BASE) xc[0] = 1;\n                                }\n                                break;\n                            } else {\n                                xc[ni] += k;\n                                if (xc[ni] != BASE) break;\n                                xc[ni--] = 0;\n                                k = 1;\n                            }\n                        }\n                    }\n                    // Remove trailing zeros.\n                    for(i = xc.length; xc[--i] === 0; xc.pop());\n                }\n                // Overflow? Infinity.\n                if (x.e > MAX_EXP) {\n                    x.c = x.e = null;\n                // Underflow? Zero.\n                } else if (x.e < MIN_EXP) {\n                    x.c = [\n                        x.e = 0\n                    ];\n                }\n            }\n            return x;\n        }\n        function valueOf(n) {\n            var str, e = n.e;\n            if (e === null) return n.toString();\n            str = coeffToString(n.c);\n            str = e <= TO_EXP_NEG || e >= TO_EXP_POS ? toExponential(str, e) : toFixedPoint(str, e, \"0\");\n            return n.s < 0 ? \"-\" + str : str;\n        }\n        // PROTOTYPE/INSTANCE METHODS\n        /*\r\n     * Return a new BigNumber whose value is the absolute value of this BigNumber.\r\n     */ P.absoluteValue = P.abs = function() {\n            var x = new BigNumber(this);\n            if (x.s < 0) x.s = 1;\n            return x;\n        };\n        /*\r\n     * Return\r\n     *   1 if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n     *   -1 if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n     *   0 if they have the same value,\r\n     *   or null if the value of either is NaN.\r\n     */ P.comparedTo = function(y, b) {\n            return compare(this, new BigNumber(y, b));\n        };\n        /*\r\n     * If dp is undefined or null or true or false, return the number of decimal places of the\r\n     * value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n     *\r\n     * Otherwise, if dp is a number, return a new BigNumber whose value is the value of this\r\n     * BigNumber rounded to a maximum of dp decimal places using rounding mode rm, or\r\n     * ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * [dp] {number} Decimal places: integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     */ P.decimalPlaces = P.dp = function(dp, rm) {\n            var c, n, v, x = this;\n            if (dp != null) {\n                intCheck(dp, 0, MAX);\n                if (rm == null) rm = ROUNDING_MODE;\n                else intCheck(rm, 0, 8);\n                return round(new BigNumber(x), dp + x.e + 1, rm);\n            }\n            if (!(c = x.c)) return null;\n            n = ((v = c.length - 1) - bitFloor(this.e / LOG_BASE)) * LOG_BASE;\n            // Subtract the number of trailing zeros of the last number.\n            if (v = c[v]) for(; v % 10 == 0; v /= 10, n--);\n            if (n < 0) n = 0;\n            return n;\n        };\n        /*\r\n     *  n / 0 = I\r\n     *  n / N = N\r\n     *  n / I = 0\r\n     *  0 / n = 0\r\n     *  0 / 0 = N\r\n     *  0 / N = N\r\n     *  0 / I = 0\r\n     *  N / n = N\r\n     *  N / 0 = N\r\n     *  N / N = N\r\n     *  N / I = N\r\n     *  I / n = I\r\n     *  I / 0 = I\r\n     *  I / N = N\r\n     *  I / I = N\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber divided by the value of\r\n     * BigNumber(y, b), rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n     */ P.dividedBy = P.div = function(y, b) {\n            return div(this, new BigNumber(y, b), DECIMAL_PLACES, ROUNDING_MODE);\n        };\n        /*\r\n     * Return a new BigNumber whose value is the integer part of dividing the value of this\r\n     * BigNumber by the value of BigNumber(y, b).\r\n     */ P.dividedToIntegerBy = P.idiv = function(y, b) {\n            return div(this, new BigNumber(y, b), 0, 1);\n        };\n        /*\r\n     * Return a BigNumber whose value is the value of this BigNumber exponentiated by n.\r\n     *\r\n     * If m is present, return the result modulo m.\r\n     * If n is negative round according to DECIMAL_PLACES and ROUNDING_MODE.\r\n     * If POW_PRECISION is non-zero and m is not present, round to POW_PRECISION using ROUNDING_MODE.\r\n     *\r\n     * The modular power operation works efficiently when x, n, and m are integers, otherwise it\r\n     * is equivalent to calculating x.exponentiatedBy(n).modulo(m) with a POW_PRECISION of 0.\r\n     *\r\n     * n {number|string|BigNumber} The exponent. An integer.\r\n     * [m] {number|string|BigNumber} The modulus.\r\n     *\r\n     * '[BigNumber Error] Exponent not an integer: {n}'\r\n     */ P.exponentiatedBy = P.pow = function(n, m) {\n            var half, isModExp, i, k, more, nIsBig, nIsNeg, nIsOdd, y, x = this;\n            n = new BigNumber(n);\n            // Allow NaN and ±Infinity, but not other non-integers.\n            if (n.c && !n.isInteger()) {\n                throw Error(bignumberError + \"Exponent not an integer: \" + valueOf(n));\n            }\n            if (m != null) m = new BigNumber(m);\n            // Exponent of MAX_SAFE_INTEGER is 15.\n            nIsBig = n.e > 14;\n            // If x is NaN, ±Infinity, ±0 or ±1, or n is ±Infinity, NaN or ±0.\n            if (!x.c || !x.c[0] || x.c[0] == 1 && !x.e && x.c.length == 1 || !n.c || !n.c[0]) {\n                // The sign of the result of pow when x is negative depends on the evenness of n.\n                // If +n overflows to ±Infinity, the evenness of n would be not be known.\n                y = new BigNumber(Math.pow(+valueOf(x), nIsBig ? n.s * (2 - isOdd(n)) : +valueOf(n)));\n                return m ? y.mod(m) : y;\n            }\n            nIsNeg = n.s < 0;\n            if (m) {\n                // x % m returns NaN if abs(m) is zero, or m is NaN.\n                if (m.c ? !m.c[0] : !m.s) return new BigNumber(NaN);\n                isModExp = !nIsNeg && x.isInteger() && m.isInteger();\n                if (isModExp) x = x.mod(m);\n            // Overflow to ±Infinity: >=2**1e10 or >=1.0000024**1e15.\n            // Underflow to ±0: <=0.79**1e10 or <=0.9999975**1e15.\n            } else if (n.e > 9 && (x.e > 0 || x.e < -1 || (x.e == 0 ? x.c[0] > 1 || nIsBig && x.c[1] >= 24e7 : x.c[0] < 8e13 || nIsBig && x.c[0] <= 9999975e7))) {\n                // If x is negative and n is odd, k = -0, else k = 0.\n                k = x.s < 0 && isOdd(n) ? -0 : 0;\n                // If x >= 1, k = ±Infinity.\n                if (x.e > -1) k = 1 / k;\n                // If n is negative return ±0, else return ±Infinity.\n                return new BigNumber(nIsNeg ? 1 / k : k);\n            } else if (POW_PRECISION) {\n                // Truncating each coefficient array to a length of k after each multiplication\n                // equates to truncating significant digits to POW_PRECISION + [28, 41],\n                // i.e. there will be a minimum of 28 guard digits retained.\n                k = mathceil(POW_PRECISION / LOG_BASE + 2);\n            }\n            if (nIsBig) {\n                half = new BigNumber(0.5);\n                if (nIsNeg) n.s = 1;\n                nIsOdd = isOdd(n);\n            } else {\n                i = Math.abs(+valueOf(n));\n                nIsOdd = i % 2;\n            }\n            y = new BigNumber(ONE);\n            // Performs 54 loop iterations for n of 9007199254740991.\n            for(;;){\n                if (nIsOdd) {\n                    y = y.times(x);\n                    if (!y.c) break;\n                    if (k) {\n                        if (y.c.length > k) y.c.length = k;\n                    } else if (isModExp) {\n                        y = y.mod(m); //y = y.minus(div(y, m, 0, MODULO_MODE).times(m));\n                    }\n                }\n                if (i) {\n                    i = mathfloor(i / 2);\n                    if (i === 0) break;\n                    nIsOdd = i % 2;\n                } else {\n                    n = n.times(half);\n                    round(n, n.e + 1, 1);\n                    if (n.e > 14) {\n                        nIsOdd = isOdd(n);\n                    } else {\n                        i = +valueOf(n);\n                        if (i === 0) break;\n                        nIsOdd = i % 2;\n                    }\n                }\n                x = x.times(x);\n                if (k) {\n                    if (x.c && x.c.length > k) x.c.length = k;\n                } else if (isModExp) {\n                    x = x.mod(m); //x = x.minus(div(x, m, 0, MODULO_MODE).times(m));\n                }\n            }\n            if (isModExp) return y;\n            if (nIsNeg) y = ONE.div(y);\n            return m ? y.mod(m) : k ? round(y, POW_PRECISION, ROUNDING_MODE, more) : y;\n        };\n        /*\r\n     * Return a new BigNumber whose value is the value of this BigNumber rounded to an integer\r\n     * using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {rm}'\r\n     */ P.integerValue = function(rm) {\n            var n = new BigNumber(this);\n            if (rm == null) rm = ROUNDING_MODE;\n            else intCheck(rm, 0, 8);\n            return round(n, n.e + 1, rm);\n        };\n        /*\r\n     * Return true if the value of this BigNumber is equal to the value of BigNumber(y, b),\r\n     * otherwise return false.\r\n     */ P.isEqualTo = P.eq = function(y, b) {\n            return compare(this, new BigNumber(y, b)) === 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is a finite number, otherwise return false.\r\n     */ P.isFinite = function() {\n            return !!this.c;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n     * otherwise return false.\r\n     */ P.isGreaterThan = P.gt = function(y, b) {\n            return compare(this, new BigNumber(y, b)) > 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is greater than or equal to the value of\r\n     * BigNumber(y, b), otherwise return false.\r\n     */ P.isGreaterThanOrEqualTo = P.gte = function(y, b) {\n            return (b = compare(this, new BigNumber(y, b))) === 1 || b === 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is an integer, otherwise return false.\r\n     */ P.isInteger = function() {\n            return !!this.c && bitFloor(this.e / LOG_BASE) > this.c.length - 2;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n     * otherwise return false.\r\n     */ P.isLessThan = P.lt = function(y, b) {\n            return compare(this, new BigNumber(y, b)) < 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is less than or equal to the value of\r\n     * BigNumber(y, b), otherwise return false.\r\n     */ P.isLessThanOrEqualTo = P.lte = function(y, b) {\n            return (b = compare(this, new BigNumber(y, b))) === -1 || b === 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is NaN, otherwise return false.\r\n     */ P.isNaN = function() {\n            return !this.s;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is negative, otherwise return false.\r\n     */ P.isNegative = function() {\n            return this.s < 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is positive, otherwise return false.\r\n     */ P.isPositive = function() {\n            return this.s > 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is 0 or -0, otherwise return false.\r\n     */ P.isZero = function() {\n            return !!this.c && this.c[0] == 0;\n        };\n        /*\r\n     *  n - 0 = n\r\n     *  n - N = N\r\n     *  n - I = -I\r\n     *  0 - n = -n\r\n     *  0 - 0 = 0\r\n     *  0 - N = N\r\n     *  0 - I = -I\r\n     *  N - n = N\r\n     *  N - 0 = N\r\n     *  N - N = N\r\n     *  N - I = N\r\n     *  I - n = I\r\n     *  I - 0 = I\r\n     *  I - N = N\r\n     *  I - I = N\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber minus the value of\r\n     * BigNumber(y, b).\r\n     */ P.minus = function(y, b) {\n            var i, j, t, xLTy, x = this, a = x.s;\n            y = new BigNumber(y, b);\n            b = y.s;\n            // Either NaN?\n            if (!a || !b) return new BigNumber(NaN);\n            // Signs differ?\n            if (a != b) {\n                y.s = -b;\n                return x.plus(y);\n            }\n            var xe = x.e / LOG_BASE, ye = y.e / LOG_BASE, xc = x.c, yc = y.c;\n            if (!xe || !ye) {\n                // Either Infinity?\n                if (!xc || !yc) return xc ? (y.s = -b, y) : new BigNumber(yc ? x : NaN);\n                // Either zero?\n                if (!xc[0] || !yc[0]) {\n                    // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\n                    return yc[0] ? (y.s = -b, y) : new BigNumber(xc[0] ? x : // IEEE 754 (2008) 6.3: n - n = -0 when rounding to -Infinity\n                    ROUNDING_MODE == 3 ? -0 : 0);\n                }\n            }\n            xe = bitFloor(xe);\n            ye = bitFloor(ye);\n            xc = xc.slice();\n            // Determine which is the bigger number.\n            if (a = xe - ye) {\n                if (xLTy = a < 0) {\n                    a = -a;\n                    t = xc;\n                } else {\n                    ye = xe;\n                    t = yc;\n                }\n                t.reverse();\n                // Prepend zeros to equalise exponents.\n                for(b = a; b--; t.push(0));\n                t.reverse();\n            } else {\n                // Exponents equal. Check digit by digit.\n                j = (xLTy = (a = xc.length) < (b = yc.length)) ? a : b;\n                for(a = b = 0; b < j; b++){\n                    if (xc[b] != yc[b]) {\n                        xLTy = xc[b] < yc[b];\n                        break;\n                    }\n                }\n            }\n            // x < y? Point xc to the array of the bigger number.\n            if (xLTy) {\n                t = xc;\n                xc = yc;\n                yc = t;\n                y.s = -y.s;\n            }\n            b = (j = yc.length) - (i = xc.length);\n            // Append zeros to xc if shorter.\n            // No need to add zeros to yc if shorter as subtract only needs to start at yc.length.\n            if (b > 0) for(; b--; xc[i++] = 0);\n            b = BASE - 1;\n            // Subtract yc from xc.\n            for(; j > a;){\n                if (xc[--j] < yc[j]) {\n                    for(i = j; i && !xc[--i]; xc[i] = b);\n                    --xc[i];\n                    xc[j] += BASE;\n                }\n                xc[j] -= yc[j];\n            }\n            // Remove leading zeros and adjust exponent accordingly.\n            for(; xc[0] == 0; xc.splice(0, 1), --ye);\n            // Zero?\n            if (!xc[0]) {\n                // Following IEEE 754 (2008) 6.3,\n                // n - n = +0  but  n - n = -0  when rounding towards -Infinity.\n                y.s = ROUNDING_MODE == 3 ? -1 : 1;\n                y.c = [\n                    y.e = 0\n                ];\n                return y;\n            }\n            // No need to check for Infinity as +x - +y != Infinity && -x - -y != Infinity\n            // for finite x and y.\n            return normalise(y, xc, ye);\n        };\n        /*\r\n     *   n % 0 =  N\r\n     *   n % N =  N\r\n     *   n % I =  n\r\n     *   0 % n =  0\r\n     *  -0 % n = -0\r\n     *   0 % 0 =  N\r\n     *   0 % N =  N\r\n     *   0 % I =  0\r\n     *   N % n =  N\r\n     *   N % 0 =  N\r\n     *   N % N =  N\r\n     *   N % I =  N\r\n     *   I % n =  N\r\n     *   I % 0 =  N\r\n     *   I % N =  N\r\n     *   I % I =  N\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber modulo the value of\r\n     * BigNumber(y, b). The result depends on the value of MODULO_MODE.\r\n     */ P.modulo = P.mod = function(y, b) {\n            var q, s, x = this;\n            y = new BigNumber(y, b);\n            // Return NaN if x is Infinity or NaN, or y is NaN or zero.\n            if (!x.c || !y.s || y.c && !y.c[0]) {\n                return new BigNumber(NaN);\n            // Return x if y is Infinity or x is zero.\n            } else if (!y.c || x.c && !x.c[0]) {\n                return new BigNumber(x);\n            }\n            if (MODULO_MODE == 9) {\n                // Euclidian division: q = sign(y) * floor(x / abs(y))\n                // r = x - qy    where  0 <= r < abs(y)\n                s = y.s;\n                y.s = 1;\n                q = div(x, y, 0, 3);\n                y.s = s;\n                q.s *= s;\n            } else {\n                q = div(x, y, 0, MODULO_MODE);\n            }\n            y = x.minus(q.times(y));\n            // To match JavaScript %, ensure sign of zero is sign of dividend.\n            if (!y.c[0] && MODULO_MODE == 1) y.s = x.s;\n            return y;\n        };\n        /*\r\n     *  n * 0 = 0\r\n     *  n * N = N\r\n     *  n * I = I\r\n     *  0 * n = 0\r\n     *  0 * 0 = 0\r\n     *  0 * N = N\r\n     *  0 * I = N\r\n     *  N * n = N\r\n     *  N * 0 = N\r\n     *  N * N = N\r\n     *  N * I = N\r\n     *  I * n = I\r\n     *  I * 0 = N\r\n     *  I * N = N\r\n     *  I * I = I\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber multiplied by the value\r\n     * of BigNumber(y, b).\r\n     */ P.multipliedBy = P.times = function(y, b) {\n            var c, e, i, j, k, m, xcL, xlo, xhi, ycL, ylo, yhi, zc, base, sqrtBase, x = this, xc = x.c, yc = (y = new BigNumber(y, b)).c;\n            // Either NaN, ±Infinity or ±0?\n            if (!xc || !yc || !xc[0] || !yc[0]) {\n                // Return NaN if either is NaN, or one is 0 and the other is Infinity.\n                if (!x.s || !y.s || xc && !xc[0] && !yc || yc && !yc[0] && !xc) {\n                    y.c = y.e = y.s = null;\n                } else {\n                    y.s *= x.s;\n                    // Return ±Infinity if either is ±Infinity.\n                    if (!xc || !yc) {\n                        y.c = y.e = null;\n                    // Return ±0 if either is ±0.\n                    } else {\n                        y.c = [\n                            0\n                        ];\n                        y.e = 0;\n                    }\n                }\n                return y;\n            }\n            e = bitFloor(x.e / LOG_BASE) + bitFloor(y.e / LOG_BASE);\n            y.s *= x.s;\n            xcL = xc.length;\n            ycL = yc.length;\n            // Ensure xc points to longer array and xcL to its length.\n            if (xcL < ycL) {\n                zc = xc;\n                xc = yc;\n                yc = zc;\n                i = xcL;\n                xcL = ycL;\n                ycL = i;\n            }\n            // Initialise the result array with zeros.\n            for(i = xcL + ycL, zc = []; i--; zc.push(0));\n            base = BASE;\n            sqrtBase = SQRT_BASE;\n            for(i = ycL; --i >= 0;){\n                c = 0;\n                ylo = yc[i] % sqrtBase;\n                yhi = yc[i] / sqrtBase | 0;\n                for(k = xcL, j = i + k; j > i;){\n                    xlo = xc[--k] % sqrtBase;\n                    xhi = xc[k] / sqrtBase | 0;\n                    m = yhi * xlo + xhi * ylo;\n                    xlo = ylo * xlo + m % sqrtBase * sqrtBase + zc[j] + c;\n                    c = (xlo / base | 0) + (m / sqrtBase | 0) + yhi * xhi;\n                    zc[j--] = xlo % base;\n                }\n                zc[j] = c;\n            }\n            if (c) {\n                ++e;\n            } else {\n                zc.splice(0, 1);\n            }\n            return normalise(y, zc, e);\n        };\n        /*\r\n     * Return a new BigNumber whose value is the value of this BigNumber negated,\r\n     * i.e. multiplied by -1.\r\n     */ P.negated = function() {\n            var x = new BigNumber(this);\n            x.s = -x.s || null;\n            return x;\n        };\n        /*\r\n     *  n + 0 = n\r\n     *  n + N = N\r\n     *  n + I = I\r\n     *  0 + n = n\r\n     *  0 + 0 = 0\r\n     *  0 + N = N\r\n     *  0 + I = I\r\n     *  N + n = N\r\n     *  N + 0 = N\r\n     *  N + N = N\r\n     *  N + I = N\r\n     *  I + n = I\r\n     *  I + 0 = I\r\n     *  I + N = N\r\n     *  I + I = I\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber plus the value of\r\n     * BigNumber(y, b).\r\n     */ P.plus = function(y, b) {\n            var t, x = this, a = x.s;\n            y = new BigNumber(y, b);\n            b = y.s;\n            // Either NaN?\n            if (!a || !b) return new BigNumber(NaN);\n            // Signs differ?\n            if (a != b) {\n                y.s = -b;\n                return x.minus(y);\n            }\n            var xe = x.e / LOG_BASE, ye = y.e / LOG_BASE, xc = x.c, yc = y.c;\n            if (!xe || !ye) {\n                // Return ±Infinity if either ±Infinity.\n                if (!xc || !yc) return new BigNumber(a / 0);\n                // Either zero?\n                // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\n                if (!xc[0] || !yc[0]) return yc[0] ? y : new BigNumber(xc[0] ? x : a * 0);\n            }\n            xe = bitFloor(xe);\n            ye = bitFloor(ye);\n            xc = xc.slice();\n            // Prepend zeros to equalise exponents. Faster to use reverse then do unshifts.\n            if (a = xe - ye) {\n                if (a > 0) {\n                    ye = xe;\n                    t = yc;\n                } else {\n                    a = -a;\n                    t = xc;\n                }\n                t.reverse();\n                for(; a--; t.push(0));\n                t.reverse();\n            }\n            a = xc.length;\n            b = yc.length;\n            // Point xc to the longer array, and b to the shorter length.\n            if (a - b < 0) {\n                t = yc;\n                yc = xc;\n                xc = t;\n                b = a;\n            }\n            // Only start adding at yc.length - 1 as the further digits of xc can be ignored.\n            for(a = 0; b;){\n                a = (xc[--b] = xc[b] + yc[b] + a) / BASE | 0;\n                xc[b] = BASE === xc[b] ? 0 : xc[b] % BASE;\n            }\n            if (a) {\n                xc = [\n                    a\n                ].concat(xc);\n                ++ye;\n            }\n            // No need to check for zero, as +x + +y != 0 && -x + -y != 0\n            // ye = MAX_EXP + 1 possible\n            return normalise(y, xc, ye);\n        };\n        /*\r\n     * If sd is undefined or null or true or false, return the number of significant digits of\r\n     * the value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n     * If sd is true include integer-part trailing zeros in the count.\r\n     *\r\n     * Otherwise, if sd is a number, return a new BigNumber whose value is the value of this\r\n     * BigNumber rounded to a maximum of sd significant digits using rounding mode rm, or\r\n     * ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * sd {number|boolean} number: significant digits: integer, 1 to MAX inclusive.\r\n     *                     boolean: whether to count integer-part trailing zeros: true or false.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n     */ P.precision = P.sd = function(sd, rm) {\n            var c, n, v, x = this;\n            if (sd != null && sd !== !!sd) {\n                intCheck(sd, 1, MAX);\n                if (rm == null) rm = ROUNDING_MODE;\n                else intCheck(rm, 0, 8);\n                return round(new BigNumber(x), sd, rm);\n            }\n            if (!(c = x.c)) return null;\n            v = c.length - 1;\n            n = v * LOG_BASE + 1;\n            if (v = c[v]) {\n                // Subtract the number of trailing zeros of the last element.\n                for(; v % 10 == 0; v /= 10, n--);\n                // Add the number of digits of the first element.\n                for(v = c[0]; v >= 10; v /= 10, n++);\n            }\n            if (sd && x.e + 1 > n) n = x.e + 1;\n            return n;\n        };\n        /*\r\n     * Return a new BigNumber whose value is the value of this BigNumber shifted by k places\r\n     * (powers of 10). Shift to the right if n > 0, and to the left if n < 0.\r\n     *\r\n     * k {number} Integer, -MAX_SAFE_INTEGER to MAX_SAFE_INTEGER inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {k}'\r\n     */ P.shiftedBy = function(k) {\n            intCheck(k, -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);\n            return this.times(\"1e\" + k);\n        };\n        /*\r\n     *  sqrt(-n) =  N\r\n     *  sqrt(N) =  N\r\n     *  sqrt(-I) =  N\r\n     *  sqrt(I) =  I\r\n     *  sqrt(0) =  0\r\n     *  sqrt(-0) = -0\r\n     *\r\n     * Return a new BigNumber whose value is the square root of the value of this BigNumber,\r\n     * rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n     */ P.squareRoot = P.sqrt = function() {\n            var m, n, r, rep, t, x = this, c = x.c, s = x.s, e = x.e, dp = DECIMAL_PLACES + 4, half = new BigNumber(\"0.5\");\n            // Negative/NaN/Infinity/zero?\n            if (s !== 1 || !c || !c[0]) {\n                return new BigNumber(!s || s < 0 && (!c || c[0]) ? NaN : c ? x : 1 / 0);\n            }\n            // Initial estimate.\n            s = Math.sqrt(+valueOf(x));\n            // Math.sqrt underflow/overflow?\n            // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\n            if (s == 0 || s == 1 / 0) {\n                n = coeffToString(c);\n                if ((n.length + e) % 2 == 0) n += \"0\";\n                s = Math.sqrt(+n);\n                e = bitFloor((e + 1) / 2) - (e < 0 || e % 2);\n                if (s == 1 / 0) {\n                    n = \"5e\" + e;\n                } else {\n                    n = s.toExponential();\n                    n = n.slice(0, n.indexOf(\"e\") + 1) + e;\n                }\n                r = new BigNumber(n);\n            } else {\n                r = new BigNumber(s + \"\");\n            }\n            // Check for zero.\n            // r could be zero if MIN_EXP is changed after the this value was created.\n            // This would cause a division by zero (x/t) and hence Infinity below, which would cause\n            // coeffToString to throw.\n            if (r.c[0]) {\n                e = r.e;\n                s = e + dp;\n                if (s < 3) s = 0;\n                // Newton-Raphson iteration.\n                for(;;){\n                    t = r;\n                    r = half.times(t.plus(div(x, t, dp, 1)));\n                    if (coeffToString(t.c).slice(0, s) === (n = coeffToString(r.c)).slice(0, s)) {\n                        // The exponent of r may here be one less than the final result exponent,\n                        // e.g 0.0009999 (e-4) --> 0.001 (e-3), so adjust s so the rounding digits\n                        // are indexed correctly.\n                        if (r.e < e) --s;\n                        n = n.slice(s - 3, s + 1);\n                        // The 4th rounding digit may be in error by -1 so if the 4 rounding digits\n                        // are 9999 or 4999 (i.e. approaching a rounding boundary) continue the\n                        // iteration.\n                        if (n == \"9999\" || !rep && n == \"4999\") {\n                            // On the first iteration only, check to see if rounding up gives the\n                            // exact result as the nines may infinitely repeat.\n                            if (!rep) {\n                                round(t, t.e + DECIMAL_PLACES + 2, 0);\n                                if (t.times(t).eq(x)) {\n                                    r = t;\n                                    break;\n                                }\n                            }\n                            dp += 4;\n                            s += 4;\n                            rep = 1;\n                        } else {\n                            // If rounding digits are null, 0{0,4} or 50{0,3}, check for exact\n                            // result. If not, then there are further digits and m will be truthy.\n                            if (!+n || !+n.slice(1) && n.charAt(0) == \"5\") {\n                                // Truncate to the first rounding digit.\n                                round(r, r.e + DECIMAL_PLACES + 2, 1);\n                                m = !r.times(r).eq(x);\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            return round(r, r.e + DECIMAL_PLACES + 1, ROUNDING_MODE, m);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber in exponential notation and\r\n     * rounded using ROUNDING_MODE to dp fixed decimal places.\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     */ P.toExponential = function(dp, rm) {\n            if (dp != null) {\n                intCheck(dp, 0, MAX);\n                dp++;\n            }\n            return format(this, dp, rm, 1);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber in fixed-point notation rounding\r\n     * to dp fixed decimal places using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * Note: as with JavaScript's number type, (-0).toFixed(0) is '0',\r\n     * but e.g. (-0.00001).toFixed(0) is '-0'.\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     */ P.toFixed = function(dp, rm) {\n            if (dp != null) {\n                intCheck(dp, 0, MAX);\n                dp = dp + this.e + 1;\n            }\n            return format(this, dp, rm);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber in fixed-point notation rounded\r\n     * using rm or ROUNDING_MODE to dp decimal places, and formatted according to the properties\r\n     * of the format or FORMAT object (see BigNumber.set).\r\n     *\r\n     * The formatting object may contain some or all of the properties shown below.\r\n     *\r\n     * FORMAT = {\r\n     *   prefix: '',\r\n     *   groupSize: 3,\r\n     *   secondaryGroupSize: 0,\r\n     *   groupSeparator: ',',\r\n     *   decimalSeparator: '.',\r\n     *   fractionGroupSize: 0,\r\n     *   fractionGroupSeparator: '\\xA0',      // non-breaking space\r\n     *   suffix: ''\r\n     * };\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     * [format] {object} Formatting options. See FORMAT pbject above.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     * '[BigNumber Error] Argument not an object: {format}'\r\n     */ P.toFormat = function(dp, rm, format) {\n            var str, x = this;\n            if (format == null) {\n                if (dp != null && rm && typeof rm == \"object\") {\n                    format = rm;\n                    rm = null;\n                } else if (dp && typeof dp == \"object\") {\n                    format = dp;\n                    dp = rm = null;\n                } else {\n                    format = FORMAT;\n                }\n            } else if (typeof format != \"object\") {\n                throw Error(bignumberError + \"Argument not an object: \" + format);\n            }\n            str = x.toFixed(dp, rm);\n            if (x.c) {\n                var i, arr = str.split(\".\"), g1 = +format.groupSize, g2 = +format.secondaryGroupSize, groupSeparator = format.groupSeparator || \"\", intPart = arr[0], fractionPart = arr[1], isNeg = x.s < 0, intDigits = isNeg ? intPart.slice(1) : intPart, len = intDigits.length;\n                if (g2) {\n                    i = g1;\n                    g1 = g2;\n                    g2 = i;\n                    len -= i;\n                }\n                if (g1 > 0 && len > 0) {\n                    i = len % g1 || g1;\n                    intPart = intDigits.substr(0, i);\n                    for(; i < len; i += g1)intPart += groupSeparator + intDigits.substr(i, g1);\n                    if (g2 > 0) intPart += groupSeparator + intDigits.slice(i);\n                    if (isNeg) intPart = \"-\" + intPart;\n                }\n                str = fractionPart ? intPart + (format.decimalSeparator || \"\") + ((g2 = +format.fractionGroupSize) ? fractionPart.replace(new RegExp(\"\\\\d{\" + g2 + \"}\\\\B\", \"g\"), \"$&\" + (format.fractionGroupSeparator || \"\")) : fractionPart) : intPart;\n            }\n            return (format.prefix || \"\") + str + (format.suffix || \"\");\n        };\n        /*\r\n     * Return an array of two BigNumbers representing the value of this BigNumber as a simple\r\n     * fraction with an integer numerator and an integer denominator.\r\n     * The denominator will be a positive non-zero value less than or equal to the specified\r\n     * maximum denominator. If a maximum denominator is not specified, the denominator will be\r\n     * the lowest value necessary to represent the number exactly.\r\n     *\r\n     * [md] {number|string|BigNumber} Integer >= 1, or Infinity. The maximum denominator.\r\n     *\r\n     * '[BigNumber Error] Argument {not an integer|out of range} : {md}'\r\n     */ P.toFraction = function(md) {\n            var d, d0, d1, d2, e, exp, n, n0, n1, q, r, s, x = this, xc = x.c;\n            if (md != null) {\n                n = new BigNumber(md);\n                // Throw if md is less than one or is not an integer, unless it is Infinity.\n                if (!n.isInteger() && (n.c || n.s !== 1) || n.lt(ONE)) {\n                    throw Error(bignumberError + \"Argument \" + (n.isInteger() ? \"out of range: \" : \"not an integer: \") + valueOf(n));\n                }\n            }\n            if (!xc) return new BigNumber(x);\n            d = new BigNumber(ONE);\n            n1 = d0 = new BigNumber(ONE);\n            d1 = n0 = new BigNumber(ONE);\n            s = coeffToString(xc);\n            // Determine initial denominator.\n            // d is a power of 10 and the minimum max denominator that specifies the value exactly.\n            e = d.e = s.length - x.e - 1;\n            d.c[0] = POWS_TEN[(exp = e % LOG_BASE) < 0 ? LOG_BASE + exp : exp];\n            md = !md || n.comparedTo(d) > 0 ? e > 0 ? d : n1 : n;\n            exp = MAX_EXP;\n            MAX_EXP = 1 / 0;\n            n = new BigNumber(s);\n            // n0 = d1 = 0\n            n0.c[0] = 0;\n            for(;;){\n                q = div(n, d, 0, 1);\n                d2 = d0.plus(q.times(d1));\n                if (d2.comparedTo(md) == 1) break;\n                d0 = d1;\n                d1 = d2;\n                n1 = n0.plus(q.times(d2 = n1));\n                n0 = d2;\n                d = n.minus(q.times(d2 = d));\n                n = d2;\n            }\n            d2 = div(md.minus(d0), d1, 0, 1);\n            n0 = n0.plus(d2.times(n1));\n            d0 = d0.plus(d2.times(d1));\n            n0.s = n1.s = x.s;\n            e = e * 2;\n            // Determine which fraction is closer to x, n0/d0 or n1/d1\n            r = div(n1, d1, e, ROUNDING_MODE).minus(x).abs().comparedTo(div(n0, d0, e, ROUNDING_MODE).minus(x).abs()) < 1 ? [\n                n1,\n                d1\n            ] : [\n                n0,\n                d0\n            ];\n            MAX_EXP = exp;\n            return r;\n        };\n        /*\r\n     * Return the value of this BigNumber converted to a number primitive.\r\n     */ P.toNumber = function() {\n            return +valueOf(this);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber rounded to sd significant digits\r\n     * using rounding mode rm or ROUNDING_MODE. If sd is less than the number of digits\r\n     * necessary to represent the integer part of the value in fixed-point notation, then use\r\n     * exponential notation.\r\n     *\r\n     * [sd] {number} Significant digits. Integer, 1 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n     */ P.toPrecision = function(sd, rm) {\n            if (sd != null) intCheck(sd, 1, MAX);\n            return format(this, sd, rm, 2);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber in base b, or base 10 if b is\r\n     * omitted. If a base is specified, including base 10, round according to DECIMAL_PLACES and\r\n     * ROUNDING_MODE. If a base is not specified, and this BigNumber has a positive exponent\r\n     * that is equal to or greater than TO_EXP_POS, or a negative exponent equal to or less than\r\n     * TO_EXP_NEG, return exponential notation.\r\n     *\r\n     * [b] {number} Integer, 2 to ALPHABET.length inclusive.\r\n     *\r\n     * '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n     */ P.toString = function(b) {\n            var str, n = this, s = n.s, e = n.e;\n            // Infinity or NaN?\n            if (e === null) {\n                if (s) {\n                    str = \"Infinity\";\n                    if (s < 0) str = \"-\" + str;\n                } else {\n                    str = \"NaN\";\n                }\n            } else {\n                if (b == null) {\n                    str = e <= TO_EXP_NEG || e >= TO_EXP_POS ? toExponential(coeffToString(n.c), e) : toFixedPoint(coeffToString(n.c), e, \"0\");\n                } else if (b === 10 && alphabetHasNormalDecimalDigits) {\n                    n = round(new BigNumber(n), DECIMAL_PLACES + e + 1, ROUNDING_MODE);\n                    str = toFixedPoint(coeffToString(n.c), n.e, \"0\");\n                } else {\n                    intCheck(b, 2, ALPHABET.length, \"Base\");\n                    str = convertBase(toFixedPoint(coeffToString(n.c), e, \"0\"), 10, b, s, true);\n                }\n                if (s < 0 && n.c[0]) str = \"-\" + str;\n            }\n            return str;\n        };\n        /*\r\n     * Return as toString, but do not accept a base argument, and include the minus sign for\r\n     * negative zero.\r\n     */ P.valueOf = P.toJSON = function() {\n            return valueOf(this);\n        };\n        P._isBigNumber = true;\n        if (configObject != null) BigNumber.set(configObject);\n        return BigNumber;\n    }\n    // PRIVATE HELPER FUNCTIONS\n    // These functions don't need access to variables,\n    // e.g. DECIMAL_PLACES, in the scope of the `clone` function above.\n    function bitFloor(n) {\n        var i = n | 0;\n        return n > 0 || n === i ? i : i - 1;\n    }\n    // Return a coefficient array as a string of base 10 digits.\n    function coeffToString(a) {\n        var s, z, i = 1, j = a.length, r = a[0] + \"\";\n        for(; i < j;){\n            s = a[i++] + \"\";\n            z = LOG_BASE - s.length;\n            for(; z--; s = \"0\" + s);\n            r += s;\n        }\n        // Determine trailing zeros.\n        for(j = r.length; r.charCodeAt(--j) === 48;);\n        return r.slice(0, j + 1 || 1);\n    }\n    // Compare the value of BigNumbers x and y.\n    function compare(x, y) {\n        var a, b, xc = x.c, yc = y.c, i = x.s, j = y.s, k = x.e, l = y.e;\n        // Either NaN?\n        if (!i || !j) return null;\n        a = xc && !xc[0];\n        b = yc && !yc[0];\n        // Either zero?\n        if (a || b) return a ? b ? 0 : -j : i;\n        // Signs differ?\n        if (i != j) return i;\n        a = i < 0;\n        b = k == l;\n        // Either Infinity?\n        if (!xc || !yc) return b ? 0 : !xc ^ a ? 1 : -1;\n        // Compare exponents.\n        if (!b) return k > l ^ a ? 1 : -1;\n        j = (k = xc.length) < (l = yc.length) ? k : l;\n        // Compare digit by digit.\n        for(i = 0; i < j; i++)if (xc[i] != yc[i]) return xc[i] > yc[i] ^ a ? 1 : -1;\n        // Compare lengths.\n        return k == l ? 0 : k > l ^ a ? 1 : -1;\n    }\n    /*\r\n   * Check that n is a primitive number, an integer, and in range, otherwise throw.\r\n   */ function intCheck(n, min, max, name) {\n        if (n < min || n > max || n !== mathfloor(n)) {\n            throw Error(bignumberError + (name || \"Argument\") + (typeof n == \"number\" ? n < min || n > max ? \" out of range: \" : \" not an integer: \" : \" not a primitive number: \") + String(n));\n        }\n    }\n    // Assumes finite n.\n    function isOdd(n) {\n        var k = n.c.length - 1;\n        return bitFloor(n.e / LOG_BASE) == k && n.c[k] % 2 != 0;\n    }\n    function toExponential(str, e) {\n        return (str.length > 1 ? str.charAt(0) + \".\" + str.slice(1) : str) + (e < 0 ? \"e\" : \"e+\") + e;\n    }\n    function toFixedPoint(str, e, z) {\n        var len, zs;\n        // Negative exponent?\n        if (e < 0) {\n            // Prepend zeros.\n            for(zs = z + \".\"; ++e; zs += z);\n            str = zs + str;\n        // Positive exponent\n        } else {\n            len = str.length;\n            // Append zeros.\n            if (++e > len) {\n                for(zs = z, e -= len; --e; zs += z);\n                str += zs;\n            } else if (e < len) {\n                str = str.slice(0, e) + \".\" + str.slice(e);\n            }\n        }\n        return str;\n    }\n    // EXPORT\n    BigNumber = clone();\n    BigNumber[\"default\"] = BigNumber.BigNumber = BigNumber;\n    // AMD.\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_RESULT__ = (function() {\n            return BigNumber;\n        }).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    // Node.js and other environments that support module.exports.\n    } else {}\n})(void 0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bignumber.js/bignumber.js\n");

/***/ })

};
;