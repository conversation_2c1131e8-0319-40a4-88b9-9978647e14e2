"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/webcrypto-core";
exports.ids = ["vendor-chunks/webcrypto-core"];
exports.modules = {

/***/ "(rsc)/./node_modules/webcrypto-core/build/webcrypto-core.es.js":
/*!****************************************************************!*\
  !*** ./node_modules/webcrypto-core/build/webcrypto-core.es.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AesCbcProvider: () => (/* binding */ AesCbcProvider),\n/* harmony export */   AesCmacProvider: () => (/* binding */ AesCmacProvider),\n/* harmony export */   AesCtrProvider: () => (/* binding */ AesCtrProvider),\n/* harmony export */   AesEcbProvider: () => (/* binding */ AesEcbProvider),\n/* harmony export */   AesGcmProvider: () => (/* binding */ AesGcmProvider),\n/* harmony export */   AesKwProvider: () => (/* binding */ AesKwProvider),\n/* harmony export */   AesProvider: () => (/* binding */ AesProvider),\n/* harmony export */   AlgorithmError: () => (/* binding */ AlgorithmError),\n/* harmony export */   BufferSourceConverter: () => (/* reexport safe */ pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter),\n/* harmony export */   Crypto: () => (/* binding */ Crypto),\n/* harmony export */   CryptoError: () => (/* binding */ CryptoError),\n/* harmony export */   CryptoKey: () => (/* binding */ CryptoKey),\n/* harmony export */   DesProvider: () => (/* binding */ DesProvider),\n/* harmony export */   EcCurves: () => (/* binding */ EcCurves),\n/* harmony export */   EcUtils: () => (/* binding */ EcUtils),\n/* harmony export */   EcdhEsProvider: () => (/* binding */ EcdhEsProvider),\n/* harmony export */   EcdhProvider: () => (/* binding */ EcdhProvider),\n/* harmony export */   EcdsaProvider: () => (/* binding */ EcdsaProvider),\n/* harmony export */   Ed25519Provider: () => (/* binding */ Ed25519Provider),\n/* harmony export */   EdDsaProvider: () => (/* binding */ EdDsaProvider),\n/* harmony export */   EllipticProvider: () => (/* binding */ EllipticProvider),\n/* harmony export */   HkdfProvider: () => (/* binding */ HkdfProvider),\n/* harmony export */   HmacProvider: () => (/* binding */ HmacProvider),\n/* harmony export */   JwkUtils: () => (/* binding */ JwkUtils),\n/* harmony export */   OperationError: () => (/* binding */ OperationError),\n/* harmony export */   Pbkdf2Provider: () => (/* binding */ Pbkdf2Provider),\n/* harmony export */   PemConverter: () => (/* binding */ PemConverter),\n/* harmony export */   ProviderCrypto: () => (/* binding */ ProviderCrypto),\n/* harmony export */   ProviderStorage: () => (/* binding */ ProviderStorage),\n/* harmony export */   RequiredPropertyError: () => (/* binding */ RequiredPropertyError),\n/* harmony export */   RsaOaepProvider: () => (/* binding */ RsaOaepProvider),\n/* harmony export */   RsaProvider: () => (/* binding */ RsaProvider),\n/* harmony export */   RsaPssProvider: () => (/* binding */ RsaPssProvider),\n/* harmony export */   RsaSsaProvider: () => (/* binding */ RsaSsaProvider),\n/* harmony export */   Shake128Provider: () => (/* binding */ Shake128Provider),\n/* harmony export */   Shake256Provider: () => (/* binding */ Shake256Provider),\n/* harmony export */   ShakeProvider: () => (/* binding */ ShakeProvider),\n/* harmony export */   SubtleCrypto: () => (/* binding */ SubtleCrypto),\n/* harmony export */   UnsupportedOperationError: () => (/* binding */ UnsupportedOperationError),\n/* harmony export */   X25519Provider: () => (/* binding */ X25519Provider),\n/* harmony export */   asn1: () => (/* binding */ index$1),\n/* harmony export */   isJWK: () => (/* binding */ isJWK),\n/* harmony export */   json: () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/./node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @peculiar/asn1-schema */ \"(rsc)/./node_modules/@peculiar/asn1-schema/build/es2015/index.js\");\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/webcrypto-core/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @peculiar/json-schema */ \"(rsc)/./node_modules/@peculiar/json-schema/build/index.es.js\");\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! asn1js */ \"(rsc)/./node_modules/asn1js/build/index.es.js\");\n/*!\n Copyright (c) Peculiar Ventures, LLC\n*/ \n\n\n\n\n\nclass CryptoError extends Error {\n}\nclass AlgorithmError extends CryptoError {\n}\nclass UnsupportedOperationError extends CryptoError {\n    constructor(methodName){\n        super(`Unsupported operation: ${methodName ? `${methodName}` : \"\"}`);\n    }\n}\nclass OperationError extends CryptoError {\n}\nclass RequiredPropertyError extends CryptoError {\n    constructor(propName){\n        super(`${propName}: Missing required property`);\n    }\n}\nclass PemConverter {\n    static toArrayBuffer(pem) {\n        const base64 = pem.replace(/-{5}(BEGIN|END) .*-{5}/g, \"\").replace(\"\\r\", \"\").replace(\"\\n\", \"\");\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64(base64);\n    }\n    static toUint8Array(pem) {\n        const bytes = this.toArrayBuffer(pem);\n        return new Uint8Array(bytes);\n    }\n    static fromBufferSource(buffer, tag) {\n        const base64 = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64(buffer);\n        let sliced;\n        let offset = 0;\n        const rows = [];\n        while(offset < base64.length){\n            sliced = base64.slice(offset, offset + 64);\n            if (sliced.length) {\n                rows.push(sliced);\n            } else {\n                break;\n            }\n            offset += 64;\n        }\n        const upperCaseTag = tag.toUpperCase();\n        return `-----BEGIN ${upperCaseTag}-----\\n${rows.join(\"\\n\")}\\n-----END ${upperCaseTag}-----`;\n    }\n    static isPEM(data) {\n        return /-----BEGIN .+-----[A-Za-z0-9+/+=\\s\\n]+-----END .+-----/i.test(data);\n    }\n    static getTagName(pem) {\n        if (!this.isPEM(pem)) {\n            throw new Error(\"Bad parameter. Incoming data is not right PEM\");\n        }\n        const res = /-----BEGIN (.+)-----/.exec(pem);\n        if (!res) {\n            throw new Error(\"Cannot get tag from PEM\");\n        }\n        return res[1];\n    }\n    static hasTagName(pem, tagName) {\n        const tag = this.getTagName(pem);\n        return tagName.toLowerCase() === tag.toLowerCase();\n    }\n    static isCertificate(pem) {\n        return this.hasTagName(pem, \"certificate\");\n    }\n    static isCertificateRequest(pem) {\n        return this.hasTagName(pem, \"certificate request\");\n    }\n    static isCRL(pem) {\n        return this.hasTagName(pem, \"x509 crl\");\n    }\n    static isPublicKey(pem) {\n        return this.hasTagName(pem, \"public key\");\n    }\n}\nfunction isJWK(data) {\n    return typeof data === \"object\" && \"kty\" in data;\n}\nclass ProviderCrypto {\n    async digest(...args) {\n        this.checkDigest.apply(this, args);\n        return this.onDigest.apply(this, args);\n    }\n    checkDigest(algorithm, _data) {\n        this.checkAlgorithmName(algorithm);\n    }\n    async onDigest(_algorithm, _data) {\n        throw new UnsupportedOperationError(\"digest\");\n    }\n    async generateKey(...args) {\n        this.checkGenerateKey.apply(this, args);\n        return this.onGenerateKey.apply(this, args);\n    }\n    checkGenerateKey(algorithm, _extractable, keyUsages, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkGenerateKeyParams(algorithm);\n        if (!(keyUsages && keyUsages.length)) {\n            throw new TypeError(`Usages cannot be empty when creating a key.`);\n        }\n        let allowedUsages;\n        if (Array.isArray(this.usages)) {\n            allowedUsages = this.usages;\n        } else {\n            allowedUsages = this.usages.privateKey.concat(this.usages.publicKey);\n        }\n        this.checkKeyUsages(keyUsages, allowedUsages);\n    }\n    checkGenerateKeyParams(_algorithm) {}\n    async onGenerateKey(_algorithm, _extractable, _keyUsages, ..._args) {\n        throw new UnsupportedOperationError(\"generateKey\");\n    }\n    async sign(...args) {\n        this.checkSign.apply(this, args);\n        return this.onSign.apply(this, args);\n    }\n    checkSign(algorithm, key, _data, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, \"sign\");\n    }\n    async onSign(_algorithm, _key, _data, ..._args) {\n        throw new UnsupportedOperationError(\"sign\");\n    }\n    async verify(...args) {\n        this.checkVerify.apply(this, args);\n        return this.onVerify.apply(this, args);\n    }\n    checkVerify(algorithm, key, _signature, _data, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, \"verify\");\n    }\n    async onVerify(_algorithm, _key, _signature, _data, ..._args) {\n        throw new UnsupportedOperationError(\"verify\");\n    }\n    async encrypt(...args) {\n        this.checkEncrypt.apply(this, args);\n        return this.onEncrypt.apply(this, args);\n    }\n    checkEncrypt(algorithm, key, _data, options = {}, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, options.keyUsage ? \"encrypt\" : void 0);\n    }\n    async onEncrypt(_algorithm, _key, _data, ..._args) {\n        throw new UnsupportedOperationError(\"encrypt\");\n    }\n    async decrypt(...args) {\n        this.checkDecrypt.apply(this, args);\n        return this.onDecrypt.apply(this, args);\n    }\n    checkDecrypt(algorithm, key, _data, options = {}, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, options.keyUsage ? \"decrypt\" : void 0);\n    }\n    async onDecrypt(_algorithm, _key, _data, ..._args) {\n        throw new UnsupportedOperationError(\"decrypt\");\n    }\n    async deriveBits(...args) {\n        this.checkDeriveBits.apply(this, args);\n        return this.onDeriveBits.apply(this, args);\n    }\n    checkDeriveBits(algorithm, baseKey, length, options = {}, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(baseKey, options.keyUsage ? \"deriveBits\" : void 0);\n        if (length % 8 !== 0) {\n            throw new OperationError(\"length: Is not multiple of 8\");\n        }\n    }\n    async onDeriveBits(_algorithm, _baseKey, _length, ..._args) {\n        throw new UnsupportedOperationError(\"deriveBits\");\n    }\n    async exportKey(...args) {\n        this.checkExportKey.apply(this, args);\n        return this.onExportKey.apply(this, args);\n    }\n    checkExportKey(format, key, ..._args) {\n        this.checkKeyFormat(format);\n        this.checkCryptoKey(key);\n        if (!key.extractable) {\n            throw new CryptoError(\"key: Is not extractable\");\n        }\n    }\n    async onExportKey(_format, _key, ..._args) {\n        throw new UnsupportedOperationError(\"exportKey\");\n    }\n    async importKey(...args) {\n        this.checkImportKey.apply(this, args);\n        return this.onImportKey.apply(this, args);\n    }\n    checkImportKey(format, keyData, algorithm, _extractable, keyUsages, ..._args) {\n        this.checkKeyFormat(format);\n        this.checkKeyData(format, keyData);\n        this.checkAlgorithmName(algorithm);\n        this.checkImportParams(algorithm);\n        if (Array.isArray(this.usages)) {\n            this.checkKeyUsages(keyUsages, this.usages);\n        }\n    }\n    async onImportKey(_format, _keyData, _algorithm, _extractable, _keyUsages, ..._args) {\n        throw new UnsupportedOperationError(\"importKey\");\n    }\n    checkAlgorithmName(algorithm) {\n        if (algorithm.name.toLowerCase() !== this.name.toLowerCase()) {\n            throw new AlgorithmError(\"Unrecognized name\");\n        }\n    }\n    checkAlgorithmParams(_algorithm) {}\n    checkDerivedKeyParams(_algorithm) {}\n    checkKeyUsages(usages, allowed) {\n        for (const usage of usages){\n            if (allowed.indexOf(usage) === -1) {\n                throw new TypeError(\"Cannot create a key using the specified key usages\");\n            }\n        }\n    }\n    checkCryptoKey(key, keyUsage) {\n        this.checkAlgorithmName(key.algorithm);\n        if (keyUsage && key.usages.indexOf(keyUsage) === -1) {\n            throw new CryptoError(`key does not match that of operation`);\n        }\n    }\n    checkRequiredProperty(data, propName) {\n        if (!(propName in data)) {\n            throw new RequiredPropertyError(propName);\n        }\n    }\n    checkHashAlgorithm(algorithm, hashAlgorithms) {\n        for (const item of hashAlgorithms){\n            if (item.toLowerCase() === algorithm.name.toLowerCase()) {\n                return;\n            }\n        }\n        throw new OperationError(`hash: Must be one of ${hashAlgorithms.join(\", \")}`);\n    }\n    checkImportParams(_algorithm) {}\n    checkKeyFormat(format) {\n        switch(format){\n            case \"raw\":\n            case \"pkcs8\":\n            case \"spki\":\n            case \"jwk\":\n                break;\n            default:\n                throw new TypeError(\"format: Is invalid value. Must be 'jwk', 'raw', 'spki', or 'pkcs8'\");\n        }\n    }\n    checkKeyData(format, keyData) {\n        if (!keyData) {\n            throw new TypeError(\"keyData: Cannot be empty on empty on key importing\");\n        }\n        if (format === \"jwk\") {\n            if (!isJWK(keyData)) {\n                throw new TypeError(\"keyData: Is not JsonWebToken\");\n            }\n        } else if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(keyData)) {\n            throw new TypeError(\"keyData: Is not ArrayBufferView or ArrayBuffer\");\n        }\n    }\n    prepareData(data) {\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n    }\n}\nclass AesProvider extends ProviderCrypto {\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not of type Number\");\n        }\n        switch(algorithm.length){\n            case 128:\n            case 192:\n            case 256:\n                break;\n            default:\n                throw new TypeError(\"length: Must be 128, 192, or 256\");\n        }\n    }\n    checkDerivedKeyParams(algorithm) {\n        this.checkGenerateKeyParams(algorithm);\n    }\n}\nclass AesCbcProvider extends AesProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"AES-CBC\";\n        this.usages = [\n            \"encrypt\",\n            \"decrypt\",\n            \"wrapKey\",\n            \"unwrapKey\"\n        ];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"iv\");\n        if (!(algorithm.iv instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.iv))) {\n            throw new TypeError(\"iv: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        if (algorithm.iv.byteLength !== 16) {\n            throw new TypeError(\"iv: Must have length 16 bytes\");\n        }\n    }\n}\nclass AesCmacProvider extends AesProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"AES-CMAC\";\n        this.usages = [\n            \"sign\",\n            \"verify\"\n        ];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not a Number\");\n        }\n        if (algorithm.length < 1) {\n            throw new OperationError(\"length: Must be more than 0\");\n        }\n    }\n}\nclass AesCtrProvider extends AesProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"AES-CTR\";\n        this.usages = [\n            \"encrypt\",\n            \"decrypt\",\n            \"wrapKey\",\n            \"unwrapKey\"\n        ];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"counter\");\n        if (!(algorithm.counter instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.counter))) {\n            throw new TypeError(\"counter: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        if (algorithm.counter.byteLength !== 16) {\n            throw new TypeError(\"iv: Must have length 16 bytes\");\n        }\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not a Number\");\n        }\n        if (algorithm.length < 1) {\n            throw new OperationError(\"length: Must be more than 0\");\n        }\n    }\n}\nclass AesEcbProvider extends AesProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"AES-ECB\";\n        this.usages = [\n            \"encrypt\",\n            \"decrypt\",\n            \"wrapKey\",\n            \"unwrapKey\"\n        ];\n    }\n}\nclass AesGcmProvider extends AesProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"AES-GCM\";\n        this.usages = [\n            \"encrypt\",\n            \"decrypt\",\n            \"wrapKey\",\n            \"unwrapKey\"\n        ];\n    }\n    checkAlgorithmParams(algorithm) {\n        var _a;\n        this.checkRequiredProperty(algorithm, \"iv\");\n        if (!(algorithm.iv instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.iv))) {\n            throw new TypeError(\"iv: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        if (algorithm.iv.byteLength < 1) {\n            throw new OperationError(\"iv: Must have length more than 0 and less than 2^64 - 1\");\n        }\n        (_a = algorithm.tagLength) !== null && _a !== void 0 ? _a : algorithm.tagLength = 128;\n        switch(algorithm.tagLength){\n            case 32:\n            case 64:\n            case 96:\n            case 104:\n            case 112:\n            case 120:\n            case 128:\n                break;\n            default:\n                throw new OperationError(\"tagLength: Must be one of 32, 64, 96, 104, 112, 120 or 128\");\n        }\n    }\n}\nclass AesKwProvider extends AesProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"AES-KW\";\n        this.usages = [\n            \"wrapKey\",\n            \"unwrapKey\"\n        ];\n    }\n}\nclass DesProvider extends ProviderCrypto {\n    constructor(){\n        super(...arguments);\n        this.usages = [\n            \"encrypt\",\n            \"decrypt\",\n            \"wrapKey\",\n            \"unwrapKey\"\n        ];\n    }\n    checkAlgorithmParams(algorithm) {\n        if (this.ivSize) {\n            this.checkRequiredProperty(algorithm, \"iv\");\n            if (!(algorithm.iv instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.iv))) {\n                throw new TypeError(\"iv: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n            }\n            if (algorithm.iv.byteLength !== this.ivSize) {\n                throw new TypeError(`iv: Must have length ${this.ivSize} bytes`);\n            }\n        }\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not of type Number\");\n        }\n        if (algorithm.length !== this.keySizeBits) {\n            throw new OperationError(`algorithm.length: Must be ${this.keySizeBits}`);\n        }\n    }\n    checkDerivedKeyParams(algorithm) {\n        this.checkGenerateKeyParams(algorithm);\n    }\n}\nclass RsaProvider extends ProviderCrypto {\n    constructor(){\n        super(...arguments);\n        this.hashAlgorithms = [\n            \"SHA-1\",\n            \"SHA-256\",\n            \"SHA-384\",\n            \"SHA-512\"\n        ];\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        this.checkRequiredProperty(algorithm, \"publicExponent\");\n        if (!(algorithm.publicExponent && algorithm.publicExponent instanceof Uint8Array)) {\n            throw new TypeError(\"publicExponent: Missing or not a Uint8Array\");\n        }\n        const publicExponent = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64(algorithm.publicExponent);\n        if (!(publicExponent === \"Aw==\" || publicExponent === \"AQAB\")) {\n            throw new TypeError(\"publicExponent: Must be [3] or [1,0,1]\");\n        }\n        this.checkRequiredProperty(algorithm, \"modulusLength\");\n        if (algorithm.modulusLength % 8 || algorithm.modulusLength < 256 || algorithm.modulusLength > 16384) {\n            throw new TypeError(\"The modulus length must be a multiple of 8 bits and >= 256 and <= 16384\");\n        }\n    }\n    checkImportParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n    }\n}\nclass RsaSsaProvider extends RsaProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"RSASSA-PKCS1-v1_5\";\n        this.usages = {\n            privateKey: [\n                \"sign\"\n            ],\n            publicKey: [\n                \"verify\"\n            ]\n        };\n    }\n}\nclass RsaPssProvider extends RsaProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"RSA-PSS\";\n        this.usages = {\n            privateKey: [\n                \"sign\"\n            ],\n            publicKey: [\n                \"verify\"\n            ]\n        };\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"saltLength\");\n        if (typeof algorithm.saltLength !== \"number\") {\n            throw new TypeError(\"saltLength: Is not a Number\");\n        }\n        if (algorithm.saltLength < 0) {\n            throw new RangeError(\"saltLength: Must be positive number\");\n        }\n    }\n}\nclass RsaOaepProvider extends RsaProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"RSA-OAEP\";\n        this.usages = {\n            privateKey: [\n                \"decrypt\",\n                \"unwrapKey\"\n            ],\n            publicKey: [\n                \"encrypt\",\n                \"wrapKey\"\n            ]\n        };\n    }\n    checkAlgorithmParams(algorithm) {\n        if (algorithm.label && !(algorithm.label instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.label))) {\n            throw new TypeError(\"label: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n    }\n}\nclass EllipticProvider extends ProviderCrypto {\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"namedCurve\");\n        this.checkNamedCurve(algorithm.namedCurve);\n    }\n    checkNamedCurve(namedCurve) {\n        for (const item of this.namedCurves){\n            if (item.toLowerCase() === namedCurve.toLowerCase()) {\n                return;\n            }\n        }\n        throw new OperationError(`namedCurve: Must be one of ${this.namedCurves.join(\", \")}`);\n    }\n}\nclass EcdsaProvider extends EllipticProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"ECDSA\";\n        this.hashAlgorithms = [\n            \"SHA-1\",\n            \"SHA-256\",\n            \"SHA-384\",\n            \"SHA-512\"\n        ];\n        this.usages = {\n            privateKey: [\n                \"sign\"\n            ],\n            publicKey: [\n                \"verify\"\n            ]\n        };\n        this.namedCurves = [\n            \"P-256\",\n            \"P-384\",\n            \"P-521\",\n            \"K-256\"\n        ];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n    }\n}\nconst KEY_TYPES = [\n    \"secret\",\n    \"private\",\n    \"public\"\n];\nclass CryptoKey {\n    static create(algorithm, type, extractable, usages) {\n        const key = new this();\n        key.algorithm = algorithm;\n        key.type = type;\n        key.extractable = extractable;\n        key.usages = usages;\n        return key;\n    }\n    static isKeyType(data) {\n        return KEY_TYPES.indexOf(data) !== -1;\n    }\n    get [Symbol.toStringTag]() {\n        return \"CryptoKey\";\n    }\n}\nclass EcdhProvider extends EllipticProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"ECDH\";\n        this.usages = {\n            privateKey: [\n                \"deriveBits\",\n                \"deriveKey\"\n            ],\n            publicKey: []\n        };\n        this.namedCurves = [\n            \"P-256\",\n            \"P-384\",\n            \"P-521\",\n            \"K-256\"\n        ];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"public\");\n        if (!(algorithm.public instanceof CryptoKey)) {\n            throw new TypeError(\"public: Is not a CryptoKey\");\n        }\n        if (algorithm.public.type !== \"public\") {\n            throw new OperationError(\"public: Is not a public key\");\n        }\n        if (algorithm.public.algorithm.name !== this.name) {\n            throw new OperationError(`public: Is not ${this.name} key`);\n        }\n    }\n}\nclass EcdhEsProvider extends EcdhProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"ECDH-ES\";\n        this.namedCurves = [\n            \"X25519\",\n            \"X448\"\n        ];\n    }\n}\nclass EdDsaProvider extends EllipticProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"EdDSA\";\n        this.usages = {\n            privateKey: [\n                \"sign\"\n            ],\n            publicKey: [\n                \"verify\"\n            ]\n        };\n        this.namedCurves = [\n            \"Ed25519\",\n            \"Ed448\"\n        ];\n    }\n}\nlet ObjectIdentifier = class ObjectIdentifier {\n    constructor(value){\n        if (value) {\n            this.value = value;\n        }\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.ObjectIdentifier\n    })\n], ObjectIdentifier.prototype, \"value\", void 0);\nObjectIdentifier = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice\n    })\n], ObjectIdentifier);\nclass AlgorithmIdentifier {\n    constructor(params){\n        Object.assign(this, params);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.ObjectIdentifier\n    })\n], AlgorithmIdentifier.prototype, \"algorithm\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any,\n        optional: true\n    })\n], AlgorithmIdentifier.prototype, \"parameters\", void 0);\nclass PrivateKeyInfo {\n    constructor(){\n        this.version = 0;\n        this.privateKeyAlgorithm = new AlgorithmIdentifier();\n        this.privateKey = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer\n    })\n], PrivateKeyInfo.prototype, \"version\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: AlgorithmIdentifier\n    })\n], PrivateKeyInfo.prototype, \"privateKeyAlgorithm\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString\n    })\n], PrivateKeyInfo.prototype, \"privateKey\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any,\n        optional: true\n    })\n], PrivateKeyInfo.prototype, \"attributes\", void 0);\nclass PublicKeyInfo {\n    constructor(){\n        this.publicKeyAlgorithm = new AlgorithmIdentifier();\n        this.publicKey = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: AlgorithmIdentifier\n    })\n], PublicKeyInfo.prototype, \"publicKeyAlgorithm\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString\n    })\n], PublicKeyInfo.prototype, \"publicKey\", void 0);\nconst JsonBase64UrlArrayBufferConverter = {\n    fromJSON: (value)=>pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(value),\n    toJSON: (value)=>pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(new Uint8Array(value))\n};\nconst AsnIntegerArrayBufferConverter = {\n    fromASN: (value)=>{\n        const valueHex = value.valueBlock.valueHex;\n        return !new Uint8Array(valueHex)[0] ? value.valueBlock.valueHex.slice(1) : value.valueBlock.valueHex;\n    },\n    toASN: (value)=>{\n        const valueHex = new Uint8Array(value)[0] > 127 ? (0,pvtsutils__WEBPACK_IMPORTED_MODULE_0__.combine)(new Uint8Array([\n            0\n        ]).buffer, value) : value;\n        return new asn1js__WEBPACK_IMPORTED_MODULE_3__.Integer({\n            valueHex\n        });\n    }\n};\nvar index$3 = /*#__PURE__*/ Object.freeze({\n    __proto__: null,\n    AsnIntegerArrayBufferConverter: AsnIntegerArrayBufferConverter,\n    JsonBase64UrlArrayBufferConverter: JsonBase64UrlArrayBufferConverter\n});\nclass RsaPrivateKey {\n    constructor(){\n        this.version = 0;\n        this.modulus = new ArrayBuffer(0);\n        this.publicExponent = new ArrayBuffer(0);\n        this.privateExponent = new ArrayBuffer(0);\n        this.prime1 = new ArrayBuffer(0);\n        this.prime2 = new ArrayBuffer(0);\n        this.exponent1 = new ArrayBuffer(0);\n        this.exponent2 = new ArrayBuffer(0);\n        this.coefficient = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnIntegerConverter\n    })\n], RsaPrivateKey.prototype, \"version\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"n\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPrivateKey.prototype, \"modulus\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"e\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPrivateKey.prototype, \"publicExponent\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"d\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPrivateKey.prototype, \"privateExponent\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"p\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPrivateKey.prototype, \"prime1\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"q\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPrivateKey.prototype, \"prime2\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"dp\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPrivateKey.prototype, \"exponent1\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"dq\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPrivateKey.prototype, \"exponent2\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"qi\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPrivateKey.prototype, \"coefficient\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any,\n        optional: true\n    })\n], RsaPrivateKey.prototype, \"otherPrimeInfos\", void 0);\nclass RsaPublicKey {\n    constructor(){\n        this.modulus = new ArrayBuffer(0);\n        this.publicExponent = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"n\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPublicKey.prototype, \"modulus\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerArrayBufferConverter\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        name: \"e\",\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], RsaPublicKey.prototype, \"publicExponent\", void 0);\nlet EcPublicKey = class EcPublicKey {\n    constructor(value){\n        this.value = new ArrayBuffer(0);\n        if (value) {\n            this.value = value;\n        }\n    }\n    toJSON() {\n        let bytes = new Uint8Array(this.value);\n        if (bytes[0] !== 0x04) {\n            throw new CryptoError(\"Wrong ECPoint. Current version supports only Uncompressed (0x04) point\");\n        }\n        bytes = new Uint8Array(this.value.slice(1));\n        const size = bytes.length / 2;\n        const offset = 0;\n        const json = {\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(bytes.buffer.slice(offset, offset + size)),\n            y: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(bytes.buffer.slice(offset + size, offset + size + size))\n        };\n        return json;\n    }\n    fromJSON(json) {\n        if (!(\"x\" in json)) {\n            throw new Error(\"x: Missing required property\");\n        }\n        if (!(\"y\" in json)) {\n            throw new Error(\"y: Missing required property\");\n        }\n        const x = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.x);\n        const y = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.y);\n        const value = (0,pvtsutils__WEBPACK_IMPORTED_MODULE_0__.combine)(new Uint8Array([\n            0x04\n        ]).buffer, x, y);\n        this.value = new Uint8Array(value).buffer;\n        return this;\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString\n    })\n], EcPublicKey.prototype, \"value\", void 0);\nEcPublicKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice\n    })\n], EcPublicKey);\nclass EcPrivateKey {\n    constructor(){\n        this.version = 1;\n        this.privateKey = new ArrayBuffer(0);\n    }\n    fromJSON(json) {\n        if (!(\"d\" in json)) {\n            throw new Error(\"d: Missing required property\");\n        }\n        this.privateKey = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.d);\n        if (\"x\" in json) {\n            const publicKey = new EcPublicKey();\n            publicKey.fromJSON(json);\n            const asn = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnSerializer.toASN(publicKey);\n            if (\"valueHex\" in asn.valueBlock) {\n                this.publicKey = asn.valueBlock.valueHex;\n            }\n        }\n        return this;\n    }\n    toJSON() {\n        const jwk = {};\n        jwk.d = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(this.privateKey);\n        if (this.publicKey) {\n            Object.assign(jwk, new EcPublicKey(this.publicKey).toJSON());\n        }\n        return jwk;\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnIntegerConverter\n    })\n], EcPrivateKey.prototype, \"version\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString\n    })\n], EcPrivateKey.prototype, \"privateKey\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        context: 0,\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any,\n        optional: true\n    })\n], EcPrivateKey.prototype, \"parameters\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        context: 1,\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString,\n        optional: true\n    })\n], EcPrivateKey.prototype, \"publicKey\", void 0);\nconst AsnIntegerWithoutPaddingConverter = {\n    fromASN: (value)=>{\n        const bytes = new Uint8Array(value.valueBlock.valueHex);\n        return bytes[0] === 0 ? bytes.buffer.slice(1) : bytes.buffer;\n    },\n    toASN: (value)=>{\n        const bytes = new Uint8Array(value);\n        if (bytes[0] > 127) {\n            const newValue = new Uint8Array(bytes.length + 1);\n            newValue.set(bytes, 1);\n            return new asn1js__WEBPACK_IMPORTED_MODULE_3__.Integer({\n                valueHex: newValue.buffer\n            });\n        }\n        return new asn1js__WEBPACK_IMPORTED_MODULE_3__.Integer({\n            valueHex: value\n        });\n    }\n};\nvar index$2 = /*#__PURE__*/ Object.freeze({\n    __proto__: null,\n    AsnIntegerWithoutPaddingConverter: AsnIntegerWithoutPaddingConverter\n});\nclass EcUtils {\n    static decodePoint(data, pointSize) {\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(data);\n        if (view.length === 0 || view[0] !== 4) {\n            throw new Error(\"Only uncompressed point format supported\");\n        }\n        const n = (view.length - 1) / 2;\n        if (n !== Math.ceil(pointSize / 8)) {\n            throw new Error(\"Point does not match field size\");\n        }\n        const xb = view.slice(1, n + 1);\n        const yb = view.slice(n + 1, n + 1 + n);\n        return {\n            x: xb,\n            y: yb\n        };\n    }\n    static encodePoint(point, pointSize) {\n        const size = Math.ceil(pointSize / 8);\n        if (point.x.byteLength !== size || point.y.byteLength !== size) {\n            throw new Error(\"X,Y coordinates don't match point size criteria\");\n        }\n        const x = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(point.x);\n        const y = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(point.y);\n        const res = new Uint8Array(size * 2 + 1);\n        res[0] = 4;\n        res.set(x, 1);\n        res.set(y, size + 1);\n        return res;\n    }\n    static getSize(pointSize) {\n        return Math.ceil(pointSize / 8);\n    }\n    static encodeSignature(signature, pointSize) {\n        const size = this.getSize(pointSize);\n        const r = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(signature.r);\n        const s = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(signature.s);\n        const res = new Uint8Array(size * 2);\n        res.set(this.padStart(r, size));\n        res.set(this.padStart(s, size), size);\n        return res;\n    }\n    static decodeSignature(data, pointSize) {\n        const size = this.getSize(pointSize);\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(data);\n        if (view.length !== size * 2) {\n            throw new Error(\"Incorrect size of the signature\");\n        }\n        const r = view.slice(0, size);\n        const s = view.slice(size);\n        return {\n            r: this.trimStart(r),\n            s: this.trimStart(s)\n        };\n    }\n    static trimStart(data) {\n        let i = 0;\n        while(i < data.length - 1 && data[i] === 0){\n            i++;\n        }\n        if (i === 0) {\n            return data;\n        }\n        return data.slice(i, data.length);\n    }\n    static padStart(data, size) {\n        if (size === data.length) {\n            return data;\n        }\n        const res = new Uint8Array(size);\n        res.set(data, size - data.length);\n        return res;\n    }\n}\nclass EcDsaSignature {\n    constructor(){\n        this.r = new ArrayBuffer(0);\n        this.s = new ArrayBuffer(0);\n    }\n    static fromWebCryptoSignature(value) {\n        const pointSize = value.byteLength / 2;\n        const point = EcUtils.decodeSignature(value, pointSize * 8);\n        const ecSignature = new EcDsaSignature();\n        ecSignature.r = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(point.r);\n        ecSignature.s = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(point.s);\n        return ecSignature;\n    }\n    toWebCryptoSignature(pointSize) {\n        if (!pointSize) {\n            const maxPointLength = Math.max(this.r.byteLength, this.s.byteLength);\n            if (maxPointLength <= 32) {\n                pointSize = 256;\n            } else if (maxPointLength <= 48) {\n                pointSize = 384;\n            } else {\n                pointSize = 521;\n            }\n        }\n        const signature = EcUtils.encodeSignature(this, pointSize);\n        return signature.buffer;\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerWithoutPaddingConverter\n    })\n], EcDsaSignature.prototype, \"r\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer,\n        converter: AsnIntegerWithoutPaddingConverter\n    })\n], EcDsaSignature.prototype, \"s\", void 0);\nclass OneAsymmetricKey extends PrivateKeyInfo {\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        context: 1,\n        implicit: true,\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString,\n        optional: true\n    })\n], OneAsymmetricKey.prototype, \"publicKey\", void 0);\nlet EdPrivateKey = class EdPrivateKey {\n    constructor(){\n        this.value = new ArrayBuffer(0);\n    }\n    fromJSON(json) {\n        if (!json.d) {\n            throw new Error(\"d: Missing required property\");\n        }\n        this.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.d);\n        return this;\n    }\n    toJSON() {\n        const jwk = {\n            d: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(this.value)\n        };\n        return jwk;\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString\n    })\n], EdPrivateKey.prototype, \"value\", void 0);\nEdPrivateKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice\n    })\n], EdPrivateKey);\nlet EdPublicKey = class EdPublicKey {\n    constructor(value){\n        this.value = new ArrayBuffer(0);\n        if (value) {\n            this.value = value;\n        }\n    }\n    toJSON() {\n        const json = {\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(this.value)\n        };\n        return json;\n    }\n    fromJSON(json) {\n        if (!(\"x\" in json)) {\n            throw new Error(\"x: Missing required property\");\n        }\n        this.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.x);\n        return this;\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString\n    })\n], EdPublicKey.prototype, \"value\", void 0);\nEdPublicKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice\n    })\n], EdPublicKey);\nlet CurvePrivateKey = class CurvePrivateKey {\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString\n    }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({\n        type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonPropTypes.String,\n        converter: JsonBase64UrlArrayBufferConverter\n    })\n], CurvePrivateKey.prototype, \"d\", void 0);\nCurvePrivateKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice\n    })\n], CurvePrivateKey);\nconst idSecp256r1 = \"1.2.840.10045.3.1.7\";\nconst idEllipticCurve = \"*********\";\nconst idSecp384r1 = `${idEllipticCurve}.34`;\nconst idSecp521r1 = `${idEllipticCurve}.35`;\nconst idSecp256k1 = `${idEllipticCurve}.10`;\nconst idVersionOne = \"********.*******.1\";\nconst idBrainpoolP160r1 = `${idVersionOne}.1`;\nconst idBrainpoolP160t1 = `${idVersionOne}.2`;\nconst idBrainpoolP192r1 = `${idVersionOne}.3`;\nconst idBrainpoolP192t1 = `${idVersionOne}.4`;\nconst idBrainpoolP224r1 = `${idVersionOne}.5`;\nconst idBrainpoolP224t1 = `${idVersionOne}.6`;\nconst idBrainpoolP256r1 = `${idVersionOne}.7`;\nconst idBrainpoolP256t1 = `${idVersionOne}.8`;\nconst idBrainpoolP320r1 = `${idVersionOne}.9`;\nconst idBrainpoolP320t1 = `${idVersionOne}.10`;\nconst idBrainpoolP384r1 = `${idVersionOne}.11`;\nconst idBrainpoolP384t1 = `${idVersionOne}.12`;\nconst idBrainpoolP512r1 = `${idVersionOne}.13`;\nconst idBrainpoolP512t1 = `${idVersionOne}.14`;\nconst idX25519 = \"***********\";\nconst idX448 = \"***********\";\nconst idEd25519 = \"***********\";\nconst idEd448 = \"***********\";\nvar index$1 = /*#__PURE__*/ Object.freeze({\n    __proto__: null,\n    AlgorithmIdentifier: AlgorithmIdentifier,\n    get CurvePrivateKey () {\n        return CurvePrivateKey;\n    },\n    EcDsaSignature: EcDsaSignature,\n    EcPrivateKey: EcPrivateKey,\n    get EcPublicKey () {\n        return EcPublicKey;\n    },\n    get EdPrivateKey () {\n        return EdPrivateKey;\n    },\n    get EdPublicKey () {\n        return EdPublicKey;\n    },\n    get ObjectIdentifier () {\n        return ObjectIdentifier;\n    },\n    OneAsymmetricKey: OneAsymmetricKey,\n    PrivateKeyInfo: PrivateKeyInfo,\n    PublicKeyInfo: PublicKeyInfo,\n    RsaPrivateKey: RsaPrivateKey,\n    RsaPublicKey: RsaPublicKey,\n    converters: index$2,\n    idBrainpoolP160r1: idBrainpoolP160r1,\n    idBrainpoolP160t1: idBrainpoolP160t1,\n    idBrainpoolP192r1: idBrainpoolP192r1,\n    idBrainpoolP192t1: idBrainpoolP192t1,\n    idBrainpoolP224r1: idBrainpoolP224r1,\n    idBrainpoolP224t1: idBrainpoolP224t1,\n    idBrainpoolP256r1: idBrainpoolP256r1,\n    idBrainpoolP256t1: idBrainpoolP256t1,\n    idBrainpoolP320r1: idBrainpoolP320r1,\n    idBrainpoolP320t1: idBrainpoolP320t1,\n    idBrainpoolP384r1: idBrainpoolP384r1,\n    idBrainpoolP384t1: idBrainpoolP384t1,\n    idBrainpoolP512r1: idBrainpoolP512r1,\n    idBrainpoolP512t1: idBrainpoolP512t1,\n    idEd25519: idEd25519,\n    idEd448: idEd448,\n    idEllipticCurve: idEllipticCurve,\n    idSecp256k1: idSecp256k1,\n    idSecp256r1: idSecp256r1,\n    idSecp384r1: idSecp384r1,\n    idSecp521r1: idSecp521r1,\n    idVersionOne: idVersionOne,\n    idX25519: idX25519,\n    idX448: idX448\n});\nclass EcCurves {\n    constructor(){}\n    static register(item) {\n        const oid = new ObjectIdentifier();\n        oid.value = item.id;\n        const raw = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnConvert.serialize(oid);\n        this.items.push({\n            ...item,\n            raw\n        });\n        this.names.push(item.name);\n    }\n    static find(nameOrId) {\n        nameOrId = nameOrId.toUpperCase();\n        for (const item of this.items){\n            if (item.name.toUpperCase() === nameOrId || item.id.toUpperCase() === nameOrId) {\n                return item;\n            }\n        }\n        return null;\n    }\n    static get(nameOrId) {\n        const res = this.find(nameOrId);\n        if (!res) {\n            throw new Error(`Unsupported EC named curve '${nameOrId}'`);\n        }\n        return res;\n    }\n}\nEcCurves.items = [];\nEcCurves.names = [];\nEcCurves.register({\n    name: \"P-256\",\n    id: idSecp256r1,\n    size: 256\n});\nEcCurves.register({\n    name: \"P-384\",\n    id: idSecp384r1,\n    size: 384\n});\nEcCurves.register({\n    name: \"P-521\",\n    id: idSecp521r1,\n    size: 521\n});\nEcCurves.register({\n    name: \"K-256\",\n    id: idSecp256k1,\n    size: 256\n});\nEcCurves.register({\n    name: \"brainpoolP160r1\",\n    id: idBrainpoolP160r1,\n    size: 160\n});\nEcCurves.register({\n    name: \"brainpoolP160t1\",\n    id: idBrainpoolP160t1,\n    size: 160\n});\nEcCurves.register({\n    name: \"brainpoolP192r1\",\n    id: idBrainpoolP192r1,\n    size: 192\n});\nEcCurves.register({\n    name: \"brainpoolP192t1\",\n    id: idBrainpoolP192t1,\n    size: 192\n});\nEcCurves.register({\n    name: \"brainpoolP224r1\",\n    id: idBrainpoolP224r1,\n    size: 224\n});\nEcCurves.register({\n    name: \"brainpoolP224t1\",\n    id: idBrainpoolP224t1,\n    size: 224\n});\nEcCurves.register({\n    name: \"brainpoolP256r1\",\n    id: idBrainpoolP256r1,\n    size: 256\n});\nEcCurves.register({\n    name: \"brainpoolP256t1\",\n    id: idBrainpoolP256t1,\n    size: 256\n});\nEcCurves.register({\n    name: \"brainpoolP320r1\",\n    id: idBrainpoolP320r1,\n    size: 320\n});\nEcCurves.register({\n    name: \"brainpoolP320t1\",\n    id: idBrainpoolP320t1,\n    size: 320\n});\nEcCurves.register({\n    name: \"brainpoolP384r1\",\n    id: idBrainpoolP384r1,\n    size: 384\n});\nEcCurves.register({\n    name: \"brainpoolP384t1\",\n    id: idBrainpoolP384t1,\n    size: 384\n});\nEcCurves.register({\n    name: \"brainpoolP512r1\",\n    id: idBrainpoolP512r1,\n    size: 512\n});\nEcCurves.register({\n    name: \"brainpoolP512t1\",\n    id: idBrainpoolP512t1,\n    size: 512\n});\nclass X25519Provider extends ProviderCrypto {\n    constructor(){\n        super(...arguments);\n        this.name = \"X25519\";\n        this.usages = {\n            privateKey: [\n                \"deriveKey\",\n                \"deriveBits\"\n            ],\n            publicKey: []\n        };\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"public\");\n    }\n}\nclass Ed25519Provider extends ProviderCrypto {\n    constructor(){\n        super(...arguments);\n        this.name = \"Ed25519\";\n        this.usages = {\n            privateKey: [\n                \"sign\"\n            ],\n            publicKey: [\n                \"verify\"\n            ]\n        };\n    }\n}\nclass HmacProvider extends ProviderCrypto {\n    constructor(){\n        super(...arguments);\n        this.name = \"HMAC\";\n        this.hashAlgorithms = [\n            \"SHA-1\",\n            \"SHA-256\",\n            \"SHA-384\",\n            \"SHA-512\"\n        ];\n        this.usages = [\n            \"sign\",\n            \"verify\"\n        ];\n    }\n    getDefaultLength(algName) {\n        switch(algName.toUpperCase()){\n            case \"SHA-1\":\n            case \"SHA-256\":\n            case \"SHA-384\":\n            case \"SHA-512\":\n                return 512;\n            default:\n                throw new Error(`Unknown algorithm name '${algName}'`);\n        }\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        if (\"length\" in algorithm) {\n            if (typeof algorithm.length !== \"number\") {\n                throw new TypeError(\"length: Is not a Number\");\n            }\n            if (algorithm.length < 1) {\n                throw new RangeError(\"length: Number is out of range\");\n            }\n        }\n    }\n    checkImportParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n    }\n}\nclass Pbkdf2Provider extends ProviderCrypto {\n    constructor(){\n        super(...arguments);\n        this.name = \"PBKDF2\";\n        this.hashAlgorithms = [\n            \"SHA-1\",\n            \"SHA-256\",\n            \"SHA-384\",\n            \"SHA-512\"\n        ];\n        this.usages = [\n            \"deriveBits\",\n            \"deriveKey\"\n        ];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        this.checkRequiredProperty(algorithm, \"salt\");\n        if (!(algorithm.salt instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.salt))) {\n            throw new TypeError(\"salt: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        this.checkRequiredProperty(algorithm, \"iterations\");\n        if (typeof algorithm.iterations !== \"number\") {\n            throw new TypeError(\"iterations: Is not a Number\");\n        }\n        if (algorithm.iterations < 1) {\n            throw new TypeError(\"iterations: Is less than 1\");\n        }\n    }\n    checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args) {\n        super.checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args);\n        if (extractable) {\n            throw new SyntaxError(\"extractable: Must be 'false'\");\n        }\n    }\n}\nclass HkdfProvider extends ProviderCrypto {\n    constructor(){\n        super(...arguments);\n        this.name = \"HKDF\";\n        this.hashAlgorithms = [\n            \"SHA-1\",\n            \"SHA-256\",\n            \"SHA-384\",\n            \"SHA-512\"\n        ];\n        this.usages = [\n            \"deriveKey\",\n            \"deriveBits\"\n        ];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        this.checkRequiredProperty(algorithm, \"salt\");\n        if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(algorithm.salt)) {\n            throw new TypeError(\"salt: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        this.checkRequiredProperty(algorithm, \"info\");\n        if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(algorithm.info)) {\n            throw new TypeError(\"salt: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n    }\n    checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args) {\n        super.checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args);\n        if (extractable) {\n            throw new SyntaxError(\"extractable: Must be 'false'\");\n        }\n    }\n}\nclass ShakeProvider extends ProviderCrypto {\n    constructor(){\n        super(...arguments);\n        this.usages = [];\n        this.defaultLength = 0;\n    }\n    digest(...args) {\n        args[0] = {\n            length: this.defaultLength,\n            ...args[0]\n        };\n        return super.digest.apply(this, args);\n    }\n    checkDigest(algorithm, data) {\n        super.checkDigest(algorithm, data);\n        const length = algorithm.length || 0;\n        if (typeof length !== \"number\") {\n            throw new TypeError(\"length: Is not a Number\");\n        }\n        if (length < 0) {\n            throw new TypeError(\"length: Is negative\");\n        }\n    }\n}\nclass Shake128Provider extends ShakeProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"shake128\";\n        this.defaultLength = 16;\n    }\n}\nclass Shake256Provider extends ShakeProvider {\n    constructor(){\n        super(...arguments);\n        this.name = \"shake256\";\n        this.defaultLength = 32;\n    }\n}\nclass Crypto {\n    get [Symbol.toStringTag]() {\n        return \"Crypto\";\n    }\n    randomUUID() {\n        const b = this.getRandomValues(new Uint8Array(16));\n        b[6] = b[6] & 0x0f | 0x40;\n        b[8] = b[8] & 0x3f | 0x80;\n        const uuid = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(b).toLowerCase();\n        return `${uuid.substring(0, 8)}-${uuid.substring(8, 12)}-${uuid.substring(12, 16)}-${uuid.substring(16, 20)}-${uuid.substring(20)}`;\n    }\n}\nclass ProviderStorage {\n    constructor(){\n        this.items = {};\n    }\n    get(algorithmName) {\n        return this.items[algorithmName.toLowerCase()] || null;\n    }\n    set(provider) {\n        this.items[provider.name.toLowerCase()] = provider;\n    }\n    removeAt(algorithmName) {\n        const provider = this.get(algorithmName.toLowerCase());\n        if (provider) {\n            delete this.items[algorithmName];\n        }\n        return provider;\n    }\n    has(name) {\n        return !!this.get(name);\n    }\n    get length() {\n        return Object.keys(this.items).length;\n    }\n    get algorithms() {\n        const algorithms = [];\n        for(const key in this.items){\n            const provider = this.items[key];\n            algorithms.push(provider.name);\n        }\n        return algorithms.sort();\n    }\n}\nconst keyFormatMap = {\n    \"jwk\": [\n        \"private\",\n        \"public\",\n        \"secret\"\n    ],\n    \"pkcs8\": [\n        \"private\"\n    ],\n    \"spki\": [\n        \"public\"\n    ],\n    \"raw\": [\n        \"secret\",\n        \"public\"\n    ]\n};\nconst sourceBufferKeyFormats = [\n    \"pkcs8\",\n    \"spki\",\n    \"raw\"\n];\nclass SubtleCrypto {\n    constructor(){\n        this.providers = new ProviderStorage();\n    }\n    static isHashedAlgorithm(data) {\n        return data && typeof data === \"object\" && \"name\" in data && \"hash\" in data ? true : false;\n    }\n    get [Symbol.toStringTag]() {\n        return \"SubtleCrypto\";\n    }\n    async digest(...args) {\n        this.checkRequiredArguments(args, 2, \"digest\");\n        const [algorithm, data, ...params] = args;\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.digest(preparedAlgorithm, preparedData, ...params);\n        return result;\n    }\n    async generateKey(...args) {\n        this.checkRequiredArguments(args, 3, \"generateKey\");\n        const [algorithm, extractable, keyUsages, ...params] = args;\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.generateKey({\n            ...preparedAlgorithm,\n            name: provider.name\n        }, extractable, keyUsages, ...params);\n        return result;\n    }\n    async sign(...args) {\n        this.checkRequiredArguments(args, 3, \"sign\");\n        const [algorithm, key, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.sign({\n            ...preparedAlgorithm,\n            name: provider.name\n        }, key, preparedData, ...params);\n        return result;\n    }\n    async verify(...args) {\n        this.checkRequiredArguments(args, 4, \"verify\");\n        const [algorithm, key, signature, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const preparedSignature = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(signature);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.verify({\n            ...preparedAlgorithm,\n            name: provider.name\n        }, key, preparedSignature, preparedData, ...params);\n        return result;\n    }\n    async encrypt(...args) {\n        this.checkRequiredArguments(args, 3, \"encrypt\");\n        const [algorithm, key, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.encrypt({\n            ...preparedAlgorithm,\n            name: provider.name\n        }, key, preparedData, {\n            keyUsage: true\n        }, ...params);\n        return result;\n    }\n    async decrypt(...args) {\n        this.checkRequiredArguments(args, 3, \"decrypt\");\n        const [algorithm, key, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.decrypt({\n            ...preparedAlgorithm,\n            name: provider.name\n        }, key, preparedData, {\n            keyUsage: true\n        }, ...params);\n        return result;\n    }\n    async deriveBits(...args) {\n        this.checkRequiredArguments(args, 3, \"deriveBits\");\n        const [algorithm, baseKey, length, ...params] = args;\n        this.checkCryptoKey(baseKey);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.deriveBits({\n            ...preparedAlgorithm,\n            name: provider.name\n        }, baseKey, length, {\n            keyUsage: true\n        }, ...params);\n        return result;\n    }\n    async deriveKey(...args) {\n        this.checkRequiredArguments(args, 5, \"deriveKey\");\n        const [algorithm, baseKey, derivedKeyType, extractable, keyUsages, ...params] = args;\n        const preparedDerivedKeyType = this.prepareAlgorithm(derivedKeyType);\n        const importProvider = this.getProvider(preparedDerivedKeyType.name);\n        importProvider.checkDerivedKeyParams(preparedDerivedKeyType);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        provider.checkCryptoKey(baseKey, \"deriveKey\");\n        const derivedBits = await provider.deriveBits({\n            ...preparedAlgorithm,\n            name: provider.name\n        }, baseKey, derivedKeyType.length || 512, {\n            keyUsage: false\n        }, ...params);\n        return this.importKey(\"raw\", derivedBits, derivedKeyType, extractable, keyUsages, ...params);\n    }\n    async exportKey(...args) {\n        this.checkRequiredArguments(args, 2, \"exportKey\");\n        const [format, key, ...params] = args;\n        this.checkCryptoKey(key);\n        if (!keyFormatMap[format]) {\n            throw new TypeError(\"Invalid keyFormat argument\");\n        }\n        if (!keyFormatMap[format].includes(key.type)) {\n            throw new DOMException(\"The key is not of the expected type\");\n        }\n        const provider = this.getProvider(key.algorithm.name);\n        const result = await provider.exportKey(format, key, ...params);\n        return result;\n    }\n    async importKey(...args) {\n        this.checkRequiredArguments(args, 5, \"importKey\");\n        const [format, keyData, algorithm, extractable, keyUsages, ...params] = args;\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        if (format === \"jwk\") {\n            if (typeof keyData !== \"object\" || !keyData.kty) {\n                throw new TypeError(\"Key data must be an object for JWK import\");\n            }\n        } else if (sourceBufferKeyFormats.includes(format)) {\n            if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(keyData)) {\n                throw new TypeError(\"Key data must be a BufferSource for non-JWK formats\");\n            }\n        } else {\n            throw new TypeError(\"The provided value is not of type '(ArrayBuffer or ArrayBufferView or JsonWebKey)'\");\n        }\n        return provider.importKey(format, keyData, {\n            ...preparedAlgorithm,\n            name: provider.name\n        }, extractable, keyUsages, ...params);\n    }\n    async wrapKey(format, key, wrappingKey, wrapAlgorithm, ...args) {\n        let keyData = await this.exportKey(format, key, ...args);\n        if (format === \"jwk\") {\n            const json = JSON.stringify(keyData);\n            keyData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromUtf8String(json);\n        }\n        const preparedAlgorithm = this.prepareAlgorithm(wrapAlgorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(keyData);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        return provider.encrypt({\n            ...preparedAlgorithm,\n            name: provider.name\n        }, wrappingKey, preparedData, {\n            keyUsage: false\n        }, ...args);\n    }\n    async unwrapKey(format, wrappedKey, unwrappingKey, unwrapAlgorithm, unwrappedKeyAlgorithm, extractable, keyUsages, ...args) {\n        const preparedAlgorithm = this.prepareAlgorithm(unwrapAlgorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(wrappedKey);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        let keyData = await provider.decrypt({\n            ...preparedAlgorithm,\n            name: provider.name\n        }, unwrappingKey, preparedData, {\n            keyUsage: false\n        }, ...args);\n        if (format === \"jwk\") {\n            try {\n                keyData = JSON.parse(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToUtf8String(keyData));\n            } catch (e) {\n                const error = new TypeError(\"wrappedKey: Is not a JSON\");\n                error.internal = e;\n                throw error;\n            }\n        }\n        return this.importKey(format, keyData, unwrappedKeyAlgorithm, extractable, keyUsages, ...args);\n    }\n    checkRequiredArguments(args, size, methodName) {\n        if (args.length < size) {\n            throw new TypeError(`Failed to execute '${methodName}' on 'SubtleCrypto': ${size} arguments required, but only ${args.length} present`);\n        }\n    }\n    prepareAlgorithm(algorithm) {\n        if (typeof algorithm === \"string\") {\n            return {\n                name: algorithm\n            };\n        }\n        if (SubtleCrypto.isHashedAlgorithm(algorithm)) {\n            const preparedAlgorithm = {\n                ...algorithm\n            };\n            preparedAlgorithm.hash = this.prepareAlgorithm(algorithm.hash);\n            return preparedAlgorithm;\n        }\n        return {\n            ...algorithm\n        };\n    }\n    getProvider(name) {\n        const provider = this.providers.get(name);\n        if (!provider) {\n            throw new AlgorithmError(\"Unrecognized name\");\n        }\n        return provider;\n    }\n    checkCryptoKey(key) {\n        if (!(key instanceof CryptoKey)) {\n            throw new TypeError(`Key is not of type 'CryptoKey'`);\n        }\n    }\n}\nvar index = /*#__PURE__*/ Object.freeze({\n    __proto__: null,\n    converters: index$3\n});\nconst REQUIRED_FIELDS = [\n    \"crv\",\n    \"e\",\n    \"k\",\n    \"kty\",\n    \"n\",\n    \"x\",\n    \"y\"\n];\nclass JwkUtils {\n    static async thumbprint(hash, jwk, crypto) {\n        const data = this.format(jwk, true);\n        return crypto.subtle.digest(hash, pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBinary(JSON.stringify(data)));\n    }\n    static format(jwk, remove = false) {\n        let res = Object.entries(jwk);\n        if (remove) {\n            res = res.filter((o)=>REQUIRED_FIELDS.includes(o[0]));\n        }\n        res = res.sort(([keyA], [keyB])=>keyA > keyB ? 1 : keyA < keyB ? -1 : 0);\n        return Object.fromEntries(res);\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/webcrypto-core/build/webcrypto-core.es.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/webcrypto-core/node_modules/tslib/tslib.es6.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/webcrypto-core/node_modules/tslib/tslib.es6.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __addDisposableResource: () => (/* binding */ __addDisposableResource),\n/* harmony export */   __assign: () => (/* binding */ __assign),\n/* harmony export */   __asyncDelegator: () => (/* binding */ __asyncDelegator),\n/* harmony export */   __asyncGenerator: () => (/* binding */ __asyncGenerator),\n/* harmony export */   __asyncValues: () => (/* binding */ __asyncValues),\n/* harmony export */   __await: () => (/* binding */ __await),\n/* harmony export */   __awaiter: () => (/* binding */ __awaiter),\n/* harmony export */   __classPrivateFieldGet: () => (/* binding */ __classPrivateFieldGet),\n/* harmony export */   __classPrivateFieldIn: () => (/* binding */ __classPrivateFieldIn),\n/* harmony export */   __classPrivateFieldSet: () => (/* binding */ __classPrivateFieldSet),\n/* harmony export */   __createBinding: () => (/* binding */ __createBinding),\n/* harmony export */   __decorate: () => (/* binding */ __decorate),\n/* harmony export */   __disposeResources: () => (/* binding */ __disposeResources),\n/* harmony export */   __esDecorate: () => (/* binding */ __esDecorate),\n/* harmony export */   __exportStar: () => (/* binding */ __exportStar),\n/* harmony export */   __extends: () => (/* binding */ __extends),\n/* harmony export */   __generator: () => (/* binding */ __generator),\n/* harmony export */   __importDefault: () => (/* binding */ __importDefault),\n/* harmony export */   __importStar: () => (/* binding */ __importStar),\n/* harmony export */   __makeTemplateObject: () => (/* binding */ __makeTemplateObject),\n/* harmony export */   __metadata: () => (/* binding */ __metadata),\n/* harmony export */   __param: () => (/* binding */ __param),\n/* harmony export */   __propKey: () => (/* binding */ __propKey),\n/* harmony export */   __read: () => (/* binding */ __read),\n/* harmony export */   __rest: () => (/* binding */ __rest),\n/* harmony export */   __rewriteRelativeImportExtension: () => (/* binding */ __rewriteRelativeImportExtension),\n/* harmony export */   __runInitializers: () => (/* binding */ __runInitializers),\n/* harmony export */   __setFunctionName: () => (/* binding */ __setFunctionName),\n/* harmony export */   __spread: () => (/* binding */ __spread),\n/* harmony export */   __spreadArray: () => (/* binding */ __spreadArray),\n/* harmony export */   __spreadArrays: () => (/* binding */ __spreadArrays),\n/* harmony export */   __values: () => (/* binding */ __values),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */ /* global Reflect, Promise, SuppressedError, Symbol, Iterator */ var extendStatics = function(d, b) {\n    extendStatics = Object.setPrototypeOf || ({\n        __proto__: []\n    }) instanceof Array && function(d, b) {\n        d.__proto__ = b;\n    } || function(d, b) {\n        for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n};\nfunction __extends(d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n        this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\nvar __assign = function() {\n    __assign = Object.assign || function __assign(t) {\n        for(var s, i = 1, n = arguments.length; i < n; i++){\n            s = arguments[i];\n            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nfunction __rest(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n}\nfunction __decorate(decorators, target, key, desc) {\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\nfunction __param(paramIndex, decorator) {\n    return function(target, key) {\n        decorator(target, key, paramIndex);\n    };\n}\nfunction __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) {\n        if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\");\n        return f;\n    }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for(var i = decorators.length - 1; i >= 0; i--){\n        var context = {};\n        for(var p in contextIn)context[p] = p === \"access\" ? {} : contextIn[p];\n        for(var p in contextIn.access)context.access[p] = contextIn.access[p];\n        context.addInitializer = function(f) {\n            if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\");\n            extraInitializers.push(accept(f || null));\n        };\n        var result = (0, decorators[i])(kind === \"accessor\" ? {\n            get: descriptor.get,\n            set: descriptor.set\n        } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        } else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n}\n;\nfunction __runInitializers(thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for(var i = 0; i < initializers.length; i++){\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n}\n;\nfunction __propKey(x) {\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\n}\n;\nfunction __setFunctionName(f, name, prefix) {\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n    return Object.defineProperty(f, \"name\", {\n        configurable: true,\n        value: prefix ? \"\".concat(prefix, \" \", name) : name\n    });\n}\n;\nfunction __metadata(metadataKey, metadataValue) {\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\nfunction __awaiter(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n}\nfunction __generator(thisArg, body) {\n    var _ = {\n        label: 0,\n        sent: function() {\n            if (t[0] & 1) throw t[1];\n            return t[1];\n        },\n        trys: [],\n        ops: []\n    }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() {\n        return this;\n    }), g;\n    function verb(n) {\n        return function(v) {\n            return step([\n                n,\n                v\n            ]);\n        };\n    }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while(g && (g = 0, op[0] && (_ = 0)), _)try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [\n                op[0] & 2,\n                t.value\n            ];\n            switch(op[0]){\n                case 0:\n                case 1:\n                    t = op;\n                    break;\n                case 4:\n                    _.label++;\n                    return {\n                        value: op[1],\n                        done: false\n                    };\n                case 5:\n                    _.label++;\n                    y = op[1];\n                    op = [\n                        0\n                    ];\n                    continue;\n                case 7:\n                    op = _.ops.pop();\n                    _.trys.pop();\n                    continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n                        _ = 0;\n                        continue;\n                    }\n                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n                        _.label = op[1];\n                        break;\n                    }\n                    if (op[0] === 6 && _.label < t[1]) {\n                        _.label = t[1];\n                        t = op;\n                        break;\n                    }\n                    if (t && _.label < t[2]) {\n                        _.label = t[2];\n                        _.ops.push(op);\n                        break;\n                    }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop();\n                    continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) {\n            op = [\n                6,\n                e\n            ];\n            y = 0;\n        } finally{\n            f = t = 0;\n        }\n        if (op[0] & 5) throw op[1];\n        return {\n            value: op[0] ? op[1] : void 0,\n            done: true\n        };\n    }\n}\nvar __createBinding = Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n};\nfunction __exportStar(m, o) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\nfunction __values(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function() {\n            if (o && i >= o.length) o = void 0;\n            return {\n                value: o && o[i++],\n                done: !o\n            };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\nfunction __read(o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);\n    } catch (error) {\n        e = {\n            error: error\n        };\n    } finally{\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        } finally{\n            if (e) throw e.error;\n        }\n    }\n    return ar;\n}\n/** @deprecated */ function __spread() {\n    for(var ar = [], i = 0; i < arguments.length; i++)ar = ar.concat(__read(arguments[i]));\n    return ar;\n}\n/** @deprecated */ function __spreadArrays() {\n    for(var s = 0, i = 0, il = arguments.length; i < il; i++)s += arguments[i].length;\n    for(var r = Array(s), k = 0, i = 0; i < il; i++)for(var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)r[k] = a[j];\n    return r;\n}\nfunction __spreadArray(to, from, pack) {\n    if (pack || arguments.length === 2) for(var i = 0, l = from.length, ar; i < l; i++){\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n}\nfunction __await(v) {\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function() {\n        return this;\n    }, i;\n    function awaitReturn(f) {\n        return function(v) {\n            return Promise.resolve(v).then(f, reject);\n        };\n    }\n    function verb(n, f) {\n        if (g[n]) {\n            i[n] = function(v) {\n                return new Promise(function(a, b) {\n                    q.push([\n                        n,\n                        v,\n                        a,\n                        b\n                    ]) > 1 || resume(n, v);\n                });\n            };\n            if (f) i[n] = f(i[n]);\n        }\n    }\n    function resume(n, v) {\n        try {\n            step(g[n](v));\n        } catch (e) {\n            settle(q[0][3], e);\n        }\n    }\n    function step(r) {\n        r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);\n    }\n    function fulfill(value) {\n        resume(\"next\", value);\n    }\n    function reject(value) {\n        resume(\"throw\", value);\n    }\n    function settle(f, v) {\n        if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);\n    }\n}\nfunction __asyncDelegator(o) {\n    var i, p;\n    return i = {}, verb(\"next\"), verb(\"throw\", function(e) {\n        throw e;\n    }), verb(\"return\"), i[Symbol.iterator] = function() {\n        return this;\n    }, i;\n    function verb(n, f) {\n        i[n] = o[n] ? function(v) {\n            return (p = !p) ? {\n                value: __await(o[n](v)),\n                done: false\n            } : f ? f(v) : v;\n        } : f;\n    }\n}\nfunction __asyncValues(o) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var m = o[Symbol.asyncIterator], i;\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function() {\n        return this;\n    }, i);\n    function verb(n) {\n        i[n] = o[n] && function(v) {\n            return new Promise(function(resolve, reject) {\n                v = o[n](v), settle(resolve, reject, v.done, v.value);\n            });\n        };\n    }\n    function settle(resolve, reject, d, v) {\n        Promise.resolve(v).then(function(v) {\n            resolve({\n                value: v,\n                done: d\n            });\n        }, reject);\n    }\n}\nfunction __makeTemplateObject(cooked, raw) {\n    if (Object.defineProperty) {\n        Object.defineProperty(cooked, \"raw\", {\n            value: raw\n        });\n    } else {\n        cooked.raw = raw;\n    }\n    return cooked;\n}\n;\nvar __setModuleDefault = Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n};\nvar ownKeys = function(o) {\n    ownKeys = Object.getOwnPropertyNames || function(o) {\n        var ar = [];\n        for(var k in o)if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n        return ar;\n    };\n    return ownKeys(o);\n};\nfunction __importStar(mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) {\n        for(var k = ownKeys(mod), i = 0; i < k.length; i++)if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n    }\n    __setModuleDefault(result, mod);\n    return result;\n}\nfunction __importDefault(mod) {\n    return mod && mod.__esModule ? mod : {\n        default: mod\n    };\n}\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n}\nfunction __classPrivateFieldIn(state, receiver) {\n    if (receiver === null || typeof receiver !== \"object\" && typeof receiver !== \"function\") throw new TypeError(\"Cannot use 'in' operator on non-object\");\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\nfunction __addDisposableResource(env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose, inner;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n            if (async) inner = dispose;\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        if (inner) dispose = function() {\n            try {\n                inner.call(this);\n            } catch (e) {\n                return Promise.reject(e);\n            }\n        };\n        env.stack.push({\n            value: value,\n            dispose: dispose,\n            async: async\n        });\n    } else if (async) {\n        env.stack.push({\n            async: true\n        });\n    }\n    return value;\n}\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function(error, suppressed, message) {\n    var e = new Error(message);\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nfunction __disposeResources(env) {\n    function fail(e) {\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n        env.hasError = true;\n    }\n    var r, s = 0;\n    function next() {\n        while(r = env.stack.pop()){\n            try {\n                if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n                if (r.dispose) {\n                    var result = r.dispose.call(r.value);\n                    if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) {\n                        fail(e);\n                        return next();\n                    });\n                } else s |= 1;\n            } catch (e) {\n                fail(e);\n            }\n        }\n        if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n        if (env.hasError) throw env.error;\n    }\n    return next();\n}\nfunction __rewriteRelativeImportExtension(path, preserveJsx) {\n    if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n        return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function(m, tsx, d, ext, cm) {\n            return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : d + ext + \".\" + cm.toLowerCase() + \"js\";\n        });\n    }\n    return path;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    __extends,\n    __assign,\n    __rest,\n    __decorate,\n    __param,\n    __esDecorate,\n    __runInitializers,\n    __propKey,\n    __setFunctionName,\n    __metadata,\n    __awaiter,\n    __generator,\n    __createBinding,\n    __exportStar,\n    __values,\n    __read,\n    __spread,\n    __spreadArrays,\n    __spreadArray,\n    __await,\n    __asyncGenerator,\n    __asyncDelegator,\n    __asyncValues,\n    __makeTemplateObject,\n    __importStar,\n    __importDefault,\n    __classPrivateFieldGet,\n    __classPrivateFieldSet,\n    __classPrivateFieldIn,\n    __addDisposableResource,\n    __disposeResources,\n    __rewriteRelativeImportExtension\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvd2ViY3J5cHRvLWNvcmUvbm9kZV9tb2R1bGVzL3RzbGliL3RzbGliLmVzNi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7Ozs7Ozs7Ozs7Ozs7OEVBYThFLEdBQzlFLDhEQUE4RCxHQUU5RCxJQUFJQSxnQkFBZ0IsU0FBU0MsQ0FBQyxFQUFFQyxDQUFDO0lBQy9CRixnQkFBZ0JHLE9BQU9DLGNBQWMsSUFDaEM7UUFBRUMsV0FBVyxFQUFFO0lBQUMsY0FBYUMsU0FBUyxTQUFVTCxDQUFDLEVBQUVDLENBQUM7UUFBSUQsRUFBRUksU0FBUyxHQUFHSDtJQUFHLEtBQzFFLFNBQVVELENBQUMsRUFBRUMsQ0FBQztRQUFJLElBQUssSUFBSUssS0FBS0wsRUFBRyxJQUFJQyxPQUFPSyxTQUFTLENBQUNDLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDUixHQUFHSyxJQUFJTixDQUFDLENBQUNNLEVBQUUsR0FBR0wsQ0FBQyxDQUFDSyxFQUFFO0lBQUU7SUFDcEcsT0FBT1AsY0FBY0MsR0FBR0M7QUFDMUI7QUFFTyxTQUFTUyxVQUFVVixDQUFDLEVBQUVDLENBQUM7SUFDNUIsSUFBSSxPQUFPQSxNQUFNLGNBQWNBLE1BQU0sTUFDakMsTUFBTSxJQUFJVSxVQUFVLHlCQUF5QkMsT0FBT1gsS0FBSztJQUM3REYsY0FBY0MsR0FBR0M7SUFDakIsU0FBU1k7UUFBTyxJQUFJLENBQUNDLFdBQVcsR0FBR2Q7SUFBRztJQUN0Q0EsRUFBRU8sU0FBUyxHQUFHTixNQUFNLE9BQU9DLE9BQU9hLE1BQU0sQ0FBQ2QsS0FBTVksQ0FBQUEsR0FBR04sU0FBUyxHQUFHTixFQUFFTSxTQUFTLEVBQUUsSUFBSU0sSUFBRztBQUNwRjtBQUVPLElBQUlHLFdBQVc7SUFDcEJBLFdBQVdkLE9BQU9lLE1BQU0sSUFBSSxTQUFTRCxTQUFTRSxDQUFDO1FBQzNDLElBQUssSUFBSUMsR0FBR0MsSUFBSSxHQUFHQyxJQUFJQyxVQUFVQyxNQUFNLEVBQUVILElBQUlDLEdBQUdELElBQUs7WUFDakRELElBQUlHLFNBQVMsQ0FBQ0YsRUFBRTtZQUNoQixJQUFLLElBQUlkLEtBQUthLEVBQUcsSUFBSWpCLE9BQU9LLFNBQVMsQ0FBQ0MsY0FBYyxDQUFDQyxJQUFJLENBQUNVLEdBQUdiLElBQUlZLENBQUMsQ0FBQ1osRUFBRSxHQUFHYSxDQUFDLENBQUNiLEVBQUU7UUFDaEY7UUFDQSxPQUFPWTtJQUNYO0lBQ0EsT0FBT0YsU0FBU1EsS0FBSyxDQUFDLElBQUksRUFBRUY7QUFDOUIsRUFBQztBQUVNLFNBQVNHLE9BQU9OLENBQUMsRUFBRU8sQ0FBQztJQUN6QixJQUFJUixJQUFJLENBQUM7SUFDVCxJQUFLLElBQUlaLEtBQUthLEVBQUcsSUFBSWpCLE9BQU9LLFNBQVMsQ0FBQ0MsY0FBYyxDQUFDQyxJQUFJLENBQUNVLEdBQUdiLE1BQU1vQixFQUFFQyxPQUFPLENBQUNyQixLQUFLLEdBQzlFWSxDQUFDLENBQUNaLEVBQUUsR0FBR2EsQ0FBQyxDQUFDYixFQUFFO0lBQ2YsSUFBSWEsS0FBSyxRQUFRLE9BQU9qQixPQUFPMEIscUJBQXFCLEtBQUssWUFDckQsSUFBSyxJQUFJUixJQUFJLEdBQUdkLElBQUlKLE9BQU8wQixxQkFBcUIsQ0FBQ1QsSUFBSUMsSUFBSWQsRUFBRWlCLE1BQU0sRUFBRUgsSUFBSztRQUNwRSxJQUFJTSxFQUFFQyxPQUFPLENBQUNyQixDQUFDLENBQUNjLEVBQUUsSUFBSSxLQUFLbEIsT0FBT0ssU0FBUyxDQUFDc0Isb0JBQW9CLENBQUNwQixJQUFJLENBQUNVLEdBQUdiLENBQUMsQ0FBQ2MsRUFBRSxHQUN6RUYsQ0FBQyxDQUFDWixDQUFDLENBQUNjLEVBQUUsQ0FBQyxHQUFHRCxDQUFDLENBQUNiLENBQUMsQ0FBQ2MsRUFBRSxDQUFDO0lBQ3pCO0lBQ0osT0FBT0Y7QUFDVDtBQUVPLFNBQVNZLFdBQVdDLFVBQVUsRUFBRUMsTUFBTSxFQUFFQyxHQUFHLEVBQUVDLElBQUk7SUFDdEQsSUFBSUMsSUFBSWIsVUFBVUMsTUFBTSxFQUFFYSxJQUFJRCxJQUFJLElBQUlILFNBQVNFLFNBQVMsT0FBT0EsT0FBT2hDLE9BQU9tQyx3QkFBd0IsQ0FBQ0wsUUFBUUMsT0FBT0MsTUFBTWxDO0lBQzNILElBQUksT0FBT3NDLFlBQVksWUFBWSxPQUFPQSxRQUFRQyxRQUFRLEtBQUssWUFBWUgsSUFBSUUsUUFBUUMsUUFBUSxDQUFDUixZQUFZQyxRQUFRQyxLQUFLQztTQUNwSCxJQUFLLElBQUlkLElBQUlXLFdBQVdSLE1BQU0sR0FBRyxHQUFHSCxLQUFLLEdBQUdBLElBQUssSUFBSXBCLElBQUkrQixVQUFVLENBQUNYLEVBQUUsRUFBRWdCLElBQUksQ0FBQ0QsSUFBSSxJQUFJbkMsRUFBRW9DLEtBQUtELElBQUksSUFBSW5DLEVBQUVnQyxRQUFRQyxLQUFLRyxLQUFLcEMsRUFBRWdDLFFBQVFDLElBQUcsS0FBTUc7SUFDaEosT0FBT0QsSUFBSSxLQUFLQyxLQUFLbEMsT0FBT3NDLGNBQWMsQ0FBQ1IsUUFBUUMsS0FBS0csSUFBSUE7QUFDOUQ7QUFFTyxTQUFTSyxRQUFRQyxVQUFVLEVBQUVDLFNBQVM7SUFDM0MsT0FBTyxTQUFVWCxNQUFNLEVBQUVDLEdBQUc7UUFBSVUsVUFBVVgsUUFBUUMsS0FBS1M7SUFBYTtBQUN0RTtBQUVPLFNBQVNFLGFBQWFDLElBQUksRUFBRUMsWUFBWSxFQUFFZixVQUFVLEVBQUVnQixTQUFTLEVBQUVDLFlBQVksRUFBRUMsaUJBQWlCO0lBQ3JHLFNBQVNDLE9BQU9DLENBQUM7UUFBSSxJQUFJQSxNQUFNLEtBQUssS0FBSyxPQUFPQSxNQUFNLFlBQVksTUFBTSxJQUFJeEMsVUFBVTtRQUFzQixPQUFPd0M7SUFBRztJQUN0SCxJQUFJQyxPQUFPTCxVQUFVSyxJQUFJLEVBQUVuQixNQUFNbUIsU0FBUyxXQUFXLFFBQVFBLFNBQVMsV0FBVyxRQUFRO0lBQ3pGLElBQUlwQixTQUFTLENBQUNjLGdCQUFnQkQsT0FBT0UsU0FBUyxDQUFDLFNBQVMsR0FBR0YsT0FBT0EsS0FBS3RDLFNBQVMsR0FBRztJQUNuRixJQUFJOEMsYUFBYVAsZ0JBQWlCZCxDQUFBQSxTQUFTOUIsT0FBT21DLHdCQUF3QixDQUFDTCxRQUFRZSxVQUFVTyxJQUFJLElBQUksQ0FBQztJQUN0RyxJQUFJQyxHQUFHQyxPQUFPO0lBQ2QsSUFBSyxJQUFJcEMsSUFBSVcsV0FBV1IsTUFBTSxHQUFHLEdBQUdILEtBQUssR0FBR0EsSUFBSztRQUM3QyxJQUFJcUMsVUFBVSxDQUFDO1FBQ2YsSUFBSyxJQUFJbkQsS0FBS3lDLFVBQVdVLE9BQU8sQ0FBQ25ELEVBQUUsR0FBR0EsTUFBTSxXQUFXLENBQUMsSUFBSXlDLFNBQVMsQ0FBQ3pDLEVBQUU7UUFDeEUsSUFBSyxJQUFJQSxLQUFLeUMsVUFBVVcsTUFBTSxDQUFFRCxRQUFRQyxNQUFNLENBQUNwRCxFQUFFLEdBQUd5QyxVQUFVVyxNQUFNLENBQUNwRCxFQUFFO1FBQ3ZFbUQsUUFBUUUsY0FBYyxHQUFHLFNBQVVSLENBQUM7WUFBSSxJQUFJSyxNQUFNLE1BQU0sSUFBSTdDLFVBQVU7WUFBMkRzQyxrQkFBa0JXLElBQUksQ0FBQ1YsT0FBT0MsS0FBSztRQUFRO1FBQzVLLElBQUlVLFNBQVMsQ0FBQyxHQUFHOUIsVUFBVSxDQUFDWCxFQUFFLEVBQUVnQyxTQUFTLGFBQWE7WUFBRVUsS0FBS1QsV0FBV1MsR0FBRztZQUFFQyxLQUFLVixXQUFXVSxHQUFHO1FBQUMsSUFBSVYsVUFBVSxDQUFDcEIsSUFBSSxFQUFFd0I7UUFDdEgsSUFBSUwsU0FBUyxZQUFZO1lBQ3JCLElBQUlTLFdBQVcsS0FBSyxHQUFHO1lBQ3ZCLElBQUlBLFdBQVcsUUFBUSxPQUFPQSxXQUFXLFVBQVUsTUFBTSxJQUFJbEQsVUFBVTtZQUN2RSxJQUFJNEMsSUFBSUwsT0FBT1csT0FBT0MsR0FBRyxHQUFHVCxXQUFXUyxHQUFHLEdBQUdQO1lBQzdDLElBQUlBLElBQUlMLE9BQU9XLE9BQU9FLEdBQUcsR0FBR1YsV0FBV1UsR0FBRyxHQUFHUjtZQUM3QyxJQUFJQSxJQUFJTCxPQUFPVyxPQUFPRyxJQUFJLEdBQUdoQixhQUFhaUIsT0FBTyxDQUFDVjtRQUN0RCxPQUNLLElBQUlBLElBQUlMLE9BQU9XLFNBQVM7WUFDekIsSUFBSVQsU0FBUyxTQUFTSixhQUFhaUIsT0FBTyxDQUFDVjtpQkFDdENGLFVBQVUsQ0FBQ3BCLElBQUksR0FBR3NCO1FBQzNCO0lBQ0o7SUFDQSxJQUFJdkIsUUFBUTlCLE9BQU9zQyxjQUFjLENBQUNSLFFBQVFlLFVBQVVPLElBQUksRUFBRUQ7SUFDMURHLE9BQU87QUFDVDs7QUFFTyxTQUFTVSxrQkFBa0JDLE9BQU8sRUFBRW5CLFlBQVksRUFBRW9CLEtBQUs7SUFDNUQsSUFBSUMsV0FBVy9DLFVBQVVDLE1BQU0sR0FBRztJQUNsQyxJQUFLLElBQUlILElBQUksR0FBR0EsSUFBSTRCLGFBQWF6QixNQUFNLEVBQUVILElBQUs7UUFDMUNnRCxRQUFRQyxXQUFXckIsWUFBWSxDQUFDNUIsRUFBRSxDQUFDWCxJQUFJLENBQUMwRCxTQUFTQyxTQUFTcEIsWUFBWSxDQUFDNUIsRUFBRSxDQUFDWCxJQUFJLENBQUMwRDtJQUNuRjtJQUNBLE9BQU9FLFdBQVdELFFBQVEsS0FBSztBQUNqQzs7QUFFTyxTQUFTRSxVQUFVQyxDQUFDO0lBQ3pCLE9BQU8sT0FBT0EsTUFBTSxXQUFXQSxJQUFJLEdBQUdDLE1BQU0sQ0FBQ0Q7QUFDL0M7O0FBRU8sU0FBU0Usa0JBQWtCdEIsQ0FBQyxFQUFFRyxJQUFJLEVBQUVvQixNQUFNO0lBQy9DLElBQUksT0FBT3BCLFNBQVMsVUFBVUEsT0FBT0EsS0FBS3FCLFdBQVcsR0FBRyxJQUFJSCxNQUFNLENBQUNsQixLQUFLcUIsV0FBVyxFQUFFLE9BQU87SUFDNUYsT0FBT3pFLE9BQU9zQyxjQUFjLENBQUNXLEdBQUcsUUFBUTtRQUFFeUIsY0FBYztRQUFNUixPQUFPTSxTQUFTLEdBQUdGLE1BQU0sQ0FBQ0UsUUFBUSxLQUFLcEIsUUFBUUE7SUFBSztBQUNwSDs7QUFFTyxTQUFTdUIsV0FBV0MsV0FBVyxFQUFFQyxhQUFhO0lBQ25ELElBQUksT0FBT3pDLFlBQVksWUFBWSxPQUFPQSxRQUFRMEMsUUFBUSxLQUFLLFlBQVksT0FBTzFDLFFBQVEwQyxRQUFRLENBQUNGLGFBQWFDO0FBQ2xIO0FBRU8sU0FBU0UsVUFBVWQsT0FBTyxFQUFFZSxVQUFVLEVBQUVDLENBQUMsRUFBRUMsU0FBUztJQUN6RCxTQUFTQyxNQUFNakIsS0FBSztRQUFJLE9BQU9BLGlCQUFpQmUsSUFBSWYsUUFBUSxJQUFJZSxFQUFFLFNBQVVHLE9BQU87WUFBSUEsUUFBUWxCO1FBQVE7SUFBSTtJQUMzRyxPQUFPLElBQUtlLENBQUFBLEtBQU1BLENBQUFBLElBQUlJLE9BQU0sQ0FBQyxFQUFHLFNBQVVELE9BQU8sRUFBRUUsTUFBTTtRQUNyRCxTQUFTQyxVQUFVckIsS0FBSztZQUFJLElBQUk7Z0JBQUVzQixLQUFLTixVQUFVTyxJQUFJLENBQUN2QjtZQUFTLEVBQUUsT0FBTzFDLEdBQUc7Z0JBQUU4RCxPQUFPOUQ7WUFBSTtRQUFFO1FBQzFGLFNBQVNrRSxTQUFTeEIsS0FBSztZQUFJLElBQUk7Z0JBQUVzQixLQUFLTixTQUFTLENBQUMsUUFBUSxDQUFDaEI7WUFBUyxFQUFFLE9BQU8xQyxHQUFHO2dCQUFFOEQsT0FBTzlEO1lBQUk7UUFBRTtRQUM3RixTQUFTZ0UsS0FBSzdCLE1BQU07WUFBSUEsT0FBT0wsSUFBSSxHQUFHOEIsUUFBUXpCLE9BQU9PLEtBQUssSUFBSWlCLE1BQU14QixPQUFPTyxLQUFLLEVBQUV5QixJQUFJLENBQUNKLFdBQVdHO1FBQVc7UUFDN0dGLEtBQUssQ0FBQ04sWUFBWUEsVUFBVTVELEtBQUssQ0FBQzJDLFNBQVNlLGNBQWMsRUFBRSxHQUFHUyxJQUFJO0lBQ3RFO0FBQ0Y7QUFFTyxTQUFTRyxZQUFZM0IsT0FBTyxFQUFFNEIsSUFBSTtJQUN2QyxJQUFJeEMsSUFBSTtRQUFFeUMsT0FBTztRQUFHQyxNQUFNO1lBQWEsSUFBSS9FLENBQUMsQ0FBQyxFQUFFLEdBQUcsR0FBRyxNQUFNQSxDQUFDLENBQUMsRUFBRTtZQUFFLE9BQU9BLENBQUMsQ0FBQyxFQUFFO1FBQUU7UUFBR2dGLE1BQU0sRUFBRTtRQUFFQyxLQUFLLEVBQUU7SUFBQyxHQUFHaEQsR0FBR2lELEdBQUdsRixHQUFHbUYsSUFBSW5HLE9BQU9hLE1BQU0sQ0FBQyxDQUFDLE9BQU91RixhQUFhLGFBQWFBLFdBQVdwRyxNQUFLLEVBQUdLLFNBQVM7SUFDL0wsT0FBTzhGLEVBQUVWLElBQUksR0FBR1ksS0FBSyxJQUFJRixDQUFDLENBQUMsUUFBUSxHQUFHRSxLQUFLLElBQUlGLENBQUMsQ0FBQyxTQUFTLEdBQUdFLEtBQUssSUFBSSxPQUFPQyxXQUFXLGNBQWVILENBQUFBLENBQUMsQ0FBQ0csT0FBT0MsUUFBUSxDQUFDLEdBQUc7UUFBYSxPQUFPLElBQUk7SUFBRSxJQUFJSjtJQUMxSixTQUFTRSxLQUFLbEYsQ0FBQztRQUFJLE9BQU8sU0FBVXFGLENBQUM7WUFBSSxPQUFPaEIsS0FBSztnQkFBQ3JFO2dCQUFHcUY7YUFBRTtRQUFHO0lBQUc7SUFDakUsU0FBU2hCLEtBQUtpQixFQUFFO1FBQ1osSUFBSXhELEdBQUcsTUFBTSxJQUFJeEMsVUFBVTtRQUMzQixNQUFPMEYsS0FBTUEsQ0FBQUEsSUFBSSxHQUFHTSxFQUFFLENBQUMsRUFBRSxJQUFLcEQsQ0FBQUEsSUFBSSxFQUFDLEdBQUlBLEVBQUcsSUFBSTtZQUMxQyxJQUFJSixJQUFJLEdBQUdpRCxLQUFNbEYsQ0FBQUEsSUFBSXlGLEVBQUUsQ0FBQyxFQUFFLEdBQUcsSUFBSVAsQ0FBQyxDQUFDLFNBQVMsR0FBR08sRUFBRSxDQUFDLEVBQUUsR0FBR1AsQ0FBQyxDQUFDLFFBQVEsSUFBSyxFQUFDbEYsSUFBSWtGLENBQUMsQ0FBQyxTQUFTLEtBQUtsRixFQUFFVCxJQUFJLENBQUMyRixJQUFJLEtBQUtBLEVBQUVULElBQUksS0FBSyxDQUFDLENBQUN6RSxJQUFJQSxFQUFFVCxJQUFJLENBQUMyRixHQUFHTyxFQUFFLENBQUMsRUFBRSxHQUFHbkQsSUFBSSxFQUFFLE9BQU90QztZQUMzSixJQUFJa0YsSUFBSSxHQUFHbEYsR0FBR3lGLEtBQUs7Z0JBQUNBLEVBQUUsQ0FBQyxFQUFFLEdBQUc7Z0JBQUd6RixFQUFFa0QsS0FBSzthQUFDO1lBQ3ZDLE9BQVF1QyxFQUFFLENBQUMsRUFBRTtnQkFDVCxLQUFLO2dCQUFHLEtBQUs7b0JBQUd6RixJQUFJeUY7b0JBQUk7Z0JBQ3hCLEtBQUs7b0JBQUdwRCxFQUFFeUMsS0FBSztvQkFBSSxPQUFPO3dCQUFFNUIsT0FBT3VDLEVBQUUsQ0FBQyxFQUFFO3dCQUFFbkQsTUFBTTtvQkFBTTtnQkFDdEQsS0FBSztvQkFBR0QsRUFBRXlDLEtBQUs7b0JBQUlJLElBQUlPLEVBQUUsQ0FBQyxFQUFFO29CQUFFQSxLQUFLO3dCQUFDO3FCQUFFO29CQUFFO2dCQUN4QyxLQUFLO29CQUFHQSxLQUFLcEQsRUFBRTRDLEdBQUcsQ0FBQ1MsR0FBRztvQkFBSXJELEVBQUUyQyxJQUFJLENBQUNVLEdBQUc7b0JBQUk7Z0JBQ3hDO29CQUNJLElBQUksQ0FBRTFGLENBQUFBLElBQUlxQyxFQUFFMkMsSUFBSSxFQUFFaEYsSUFBSUEsRUFBRUssTUFBTSxHQUFHLEtBQUtMLENBQUMsQ0FBQ0EsRUFBRUssTUFBTSxHQUFHLEVBQUUsS0FBTW9GLENBQUFBLEVBQUUsQ0FBQyxFQUFFLEtBQUssS0FBS0EsRUFBRSxDQUFDLEVBQUUsS0FBSyxJQUFJO3dCQUFFcEQsSUFBSTt3QkFBRztvQkFBVTtvQkFDM0csSUFBSW9ELEVBQUUsQ0FBQyxFQUFFLEtBQUssS0FBTSxFQUFDekYsS0FBTXlGLEVBQUUsQ0FBQyxFQUFFLEdBQUd6RixDQUFDLENBQUMsRUFBRSxJQUFJeUYsRUFBRSxDQUFDLEVBQUUsR0FBR3pGLENBQUMsQ0FBQyxFQUFFLEdBQUk7d0JBQUVxQyxFQUFFeUMsS0FBSyxHQUFHVyxFQUFFLENBQUMsRUFBRTt3QkFBRTtvQkFBTztvQkFDckYsSUFBSUEsRUFBRSxDQUFDLEVBQUUsS0FBSyxLQUFLcEQsRUFBRXlDLEtBQUssR0FBRzlFLENBQUMsQ0FBQyxFQUFFLEVBQUU7d0JBQUVxQyxFQUFFeUMsS0FBSyxHQUFHOUUsQ0FBQyxDQUFDLEVBQUU7d0JBQUVBLElBQUl5Rjt3QkFBSTtvQkFBTztvQkFDcEUsSUFBSXpGLEtBQUtxQyxFQUFFeUMsS0FBSyxHQUFHOUUsQ0FBQyxDQUFDLEVBQUUsRUFBRTt3QkFBRXFDLEVBQUV5QyxLQUFLLEdBQUc5RSxDQUFDLENBQUMsRUFBRTt3QkFBRXFDLEVBQUU0QyxHQUFHLENBQUN2QyxJQUFJLENBQUMrQzt3QkFBSztvQkFBTztvQkFDbEUsSUFBSXpGLENBQUMsQ0FBQyxFQUFFLEVBQUVxQyxFQUFFNEMsR0FBRyxDQUFDUyxHQUFHO29CQUNuQnJELEVBQUUyQyxJQUFJLENBQUNVLEdBQUc7b0JBQUk7WUFDdEI7WUFDQUQsS0FBS1osS0FBS3RGLElBQUksQ0FBQzBELFNBQVNaO1FBQzVCLEVBQUUsT0FBTzdCLEdBQUc7WUFBRWlGLEtBQUs7Z0JBQUM7Z0JBQUdqRjthQUFFO1lBQUUwRSxJQUFJO1FBQUcsU0FBVTtZQUFFakQsSUFBSWpDLElBQUk7UUFBRztRQUN6RCxJQUFJeUYsRUFBRSxDQUFDLEVBQUUsR0FBRyxHQUFHLE1BQU1BLEVBQUUsQ0FBQyxFQUFFO1FBQUUsT0FBTztZQUFFdkMsT0FBT3VDLEVBQUUsQ0FBQyxFQUFFLEdBQUdBLEVBQUUsQ0FBQyxFQUFFLEdBQUcsS0FBSztZQUFHbkQsTUFBTTtRQUFLO0lBQ25GO0FBQ0Y7QUFFTyxJQUFJcUQsa0JBQWtCM0csT0FBT2EsTUFBTSxHQUFJLFNBQVMrRixDQUFDLEVBQUVDLENBQUMsRUFBRUMsQ0FBQyxFQUFFQyxFQUFFO0lBQ2hFLElBQUlBLE9BQU9DLFdBQVdELEtBQUtEO0lBQzNCLElBQUk5RSxPQUFPaEMsT0FBT21DLHdCQUF3QixDQUFDMEUsR0FBR0M7SUFDOUMsSUFBSSxDQUFDOUUsUUFBUyxVQUFTQSxPQUFPLENBQUM2RSxFQUFFSSxVQUFVLEdBQUdqRixLQUFLa0YsUUFBUSxJQUFJbEYsS0FBSzBDLFlBQVksR0FBRztRQUMvRTFDLE9BQU87WUFBRW1GLFlBQVk7WUFBTXZELEtBQUs7Z0JBQWEsT0FBT2lELENBQUMsQ0FBQ0MsRUFBRTtZQUFFO1FBQUU7SUFDaEU7SUFDQTlHLE9BQU9zQyxjQUFjLENBQUNzRSxHQUFHRyxJQUFJL0U7QUFDL0IsSUFBTSxTQUFTNEUsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUMsRUFBRUMsRUFBRTtJQUN4QixJQUFJQSxPQUFPQyxXQUFXRCxLQUFLRDtJQUMzQkYsQ0FBQyxDQUFDRyxHQUFHLEdBQUdGLENBQUMsQ0FBQ0MsRUFBRTtBQUNkLEVBQUc7QUFFSSxTQUFTTSxhQUFhUCxDQUFDLEVBQUVELENBQUM7SUFDL0IsSUFBSyxJQUFJeEcsS0FBS3lHLEVBQUcsSUFBSXpHLE1BQU0sYUFBYSxDQUFDSixPQUFPSyxTQUFTLENBQUNDLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDcUcsR0FBR3hHLElBQUl1RyxnQkFBZ0JDLEdBQUdDLEdBQUd6RztBQUM3RztBQUVPLFNBQVNpSCxTQUFTVCxDQUFDO0lBQ3hCLElBQUkzRixJQUFJLE9BQU9xRixXQUFXLGNBQWNBLE9BQU9DLFFBQVEsRUFBRU0sSUFBSTVGLEtBQUsyRixDQUFDLENBQUMzRixFQUFFLEVBQUVDLElBQUk7SUFDNUUsSUFBSTJGLEdBQUcsT0FBT0EsRUFBRXRHLElBQUksQ0FBQ3FHO0lBQ3JCLElBQUlBLEtBQUssT0FBT0EsRUFBRXZGLE1BQU0sS0FBSyxVQUFVLE9BQU87UUFDMUNvRSxNQUFNO1lBQ0YsSUFBSW1CLEtBQUsxRixLQUFLMEYsRUFBRXZGLE1BQU0sRUFBRXVGLElBQUksS0FBSztZQUNqQyxPQUFPO2dCQUFFMUMsT0FBTzBDLEtBQUtBLENBQUMsQ0FBQzFGLElBQUk7Z0JBQUVvQyxNQUFNLENBQUNzRDtZQUFFO1FBQzFDO0lBQ0o7SUFDQSxNQUFNLElBQUluRyxVQUFVUSxJQUFJLDRCQUE0QjtBQUN0RDtBQUVPLFNBQVNxRyxPQUFPVixDQUFDLEVBQUV6RixDQUFDO0lBQ3pCLElBQUkwRixJQUFJLE9BQU9QLFdBQVcsY0FBY00sQ0FBQyxDQUFDTixPQUFPQyxRQUFRLENBQUM7SUFDMUQsSUFBSSxDQUFDTSxHQUFHLE9BQU9EO0lBQ2YsSUFBSTFGLElBQUkyRixFQUFFdEcsSUFBSSxDQUFDcUcsSUFBSTFFLEdBQUdxRixLQUFLLEVBQUUsRUFBRS9GO0lBQy9CLElBQUk7UUFDQSxNQUFPLENBQUNMLE1BQU0sS0FBSyxLQUFLQSxNQUFNLE1BQU0sQ0FBQyxDQUFDZSxJQUFJaEIsRUFBRXVFLElBQUksRUFBQyxFQUFHbkMsSUFBSSxDQUFFaUUsR0FBRzdELElBQUksQ0FBQ3hCLEVBQUVnQyxLQUFLO0lBQzdFLEVBQ0EsT0FBT3NELE9BQU87UUFBRWhHLElBQUk7WUFBRWdHLE9BQU9BO1FBQU07SUFBRyxTQUM5QjtRQUNKLElBQUk7WUFDQSxJQUFJdEYsS0FBSyxDQUFDQSxFQUFFb0IsSUFBSSxJQUFLdUQsQ0FBQUEsSUFBSTNGLENBQUMsQ0FBQyxTQUFTLEdBQUcyRixFQUFFdEcsSUFBSSxDQUFDVztRQUNsRCxTQUNRO1lBQUUsSUFBSU0sR0FBRyxNQUFNQSxFQUFFZ0csS0FBSztRQUFFO0lBQ3BDO0lBQ0EsT0FBT0Q7QUFDVDtBQUVBLGdCQUFnQixHQUNULFNBQVNFO0lBQ2QsSUFBSyxJQUFJRixLQUFLLEVBQUUsRUFBRXJHLElBQUksR0FBR0EsSUFBSUUsVUFBVUMsTUFBTSxFQUFFSCxJQUMzQ3FHLEtBQUtBLEdBQUdqRCxNQUFNLENBQUNnRCxPQUFPbEcsU0FBUyxDQUFDRixFQUFFO0lBQ3RDLE9BQU9xRztBQUNUO0FBRUEsZ0JBQWdCLEdBQ1QsU0FBU0c7SUFDZCxJQUFLLElBQUl6RyxJQUFJLEdBQUdDLElBQUksR0FBR3lHLEtBQUt2RyxVQUFVQyxNQUFNLEVBQUVILElBQUl5RyxJQUFJekcsSUFBS0QsS0FBS0csU0FBUyxDQUFDRixFQUFFLENBQUNHLE1BQU07SUFDbkYsSUFBSyxJQUFJYSxJQUFJL0IsTUFBTWMsSUFBSTZGLElBQUksR0FBRzVGLElBQUksR0FBR0EsSUFBSXlHLElBQUl6RyxJQUN6QyxJQUFLLElBQUkwRyxJQUFJeEcsU0FBUyxDQUFDRixFQUFFLEVBQUUyRyxJQUFJLEdBQUdDLEtBQUtGLEVBQUV2RyxNQUFNLEVBQUV3RyxJQUFJQyxJQUFJRCxLQUFLZixJQUMxRDVFLENBQUMsQ0FBQzRFLEVBQUUsR0FBR2MsQ0FBQyxDQUFDQyxFQUFFO0lBQ25CLE9BQU8zRjtBQUNUO0FBRU8sU0FBUzZGLGNBQWNDLEVBQUUsRUFBRUMsSUFBSSxFQUFFQyxJQUFJO0lBQzFDLElBQUlBLFFBQVE5RyxVQUFVQyxNQUFNLEtBQUssR0FBRyxJQUFLLElBQUlILElBQUksR0FBR2lILElBQUlGLEtBQUs1RyxNQUFNLEVBQUVrRyxJQUFJckcsSUFBSWlILEdBQUdqSCxJQUFLO1FBQ2pGLElBQUlxRyxNQUFNLENBQUVyRyxDQUFBQSxLQUFLK0csSUFBRyxHQUFJO1lBQ3BCLElBQUksQ0FBQ1YsSUFBSUEsS0FBS3BILE1BQU1FLFNBQVMsQ0FBQytILEtBQUssQ0FBQzdILElBQUksQ0FBQzBILE1BQU0sR0FBRy9HO1lBQ2xEcUcsRUFBRSxDQUFDckcsRUFBRSxHQUFHK0csSUFBSSxDQUFDL0csRUFBRTtRQUNuQjtJQUNKO0lBQ0EsT0FBTzhHLEdBQUcxRCxNQUFNLENBQUNpRCxNQUFNcEgsTUFBTUUsU0FBUyxDQUFDK0gsS0FBSyxDQUFDN0gsSUFBSSxDQUFDMEg7QUFDcEQ7QUFFTyxTQUFTSSxRQUFRN0IsQ0FBQztJQUN2QixPQUFPLElBQUksWUFBWTZCLFVBQVcsS0FBSSxDQUFDN0IsQ0FBQyxHQUFHQSxHQUFHLElBQUksSUFBSSxJQUFJNkIsUUFBUTdCO0FBQ3BFO0FBRU8sU0FBUzhCLGlCQUFpQnJFLE9BQU8sRUFBRWUsVUFBVSxFQUFFRSxTQUFTO0lBQzdELElBQUksQ0FBQ29CLE9BQU9pQyxhQUFhLEVBQUUsTUFBTSxJQUFJOUgsVUFBVTtJQUMvQyxJQUFJMEYsSUFBSWpCLFVBQVU1RCxLQUFLLENBQUMyQyxTQUFTZSxjQUFjLEVBQUUsR0FBRzlELEdBQUdzSCxJQUFJLEVBQUU7SUFDN0QsT0FBT3RILElBQUlsQixPQUFPYSxNQUFNLENBQUMsQ0FBQyxPQUFPNEgsa0JBQWtCLGFBQWFBLGdCQUFnQnpJLE1BQUssRUFBR0ssU0FBUyxHQUFHZ0csS0FBSyxTQUFTQSxLQUFLLFVBQVVBLEtBQUssVUFBVXFDLGNBQWN4SCxDQUFDLENBQUNvRixPQUFPaUMsYUFBYSxDQUFDLEdBQUc7UUFBYyxPQUFPLElBQUk7SUFBRSxHQUFHckg7SUFDdE4sU0FBU3dILFlBQVl6RixDQUFDO1FBQUksT0FBTyxTQUFVdUQsQ0FBQztZQUFJLE9BQU9uQixRQUFRRCxPQUFPLENBQUNvQixHQUFHYixJQUFJLENBQUMxQyxHQUFHcUM7UUFBUztJQUFHO0lBQzlGLFNBQVNlLEtBQUtsRixDQUFDLEVBQUU4QixDQUFDO1FBQUksSUFBSWtELENBQUMsQ0FBQ2hGLEVBQUUsRUFBRTtZQUFFRCxDQUFDLENBQUNDLEVBQUUsR0FBRyxTQUFVcUYsQ0FBQztnQkFBSSxPQUFPLElBQUluQixRQUFRLFNBQVV1QyxDQUFDLEVBQUU3SCxDQUFDO29CQUFJeUksRUFBRTlFLElBQUksQ0FBQzt3QkFBQ3ZDO3dCQUFHcUY7d0JBQUdvQjt3QkFBRzdIO3FCQUFFLElBQUksS0FBSzRJLE9BQU94SCxHQUFHcUY7Z0JBQUk7WUFBSTtZQUFHLElBQUl2RCxHQUFHL0IsQ0FBQyxDQUFDQyxFQUFFLEdBQUc4QixFQUFFL0IsQ0FBQyxDQUFDQyxFQUFFO1FBQUc7SUFBRTtJQUN2SyxTQUFTd0gsT0FBT3hILENBQUMsRUFBRXFGLENBQUM7UUFBSSxJQUFJO1lBQUVoQixLQUFLVyxDQUFDLENBQUNoRixFQUFFLENBQUNxRjtRQUFLLEVBQUUsT0FBT2hGLEdBQUc7WUFBRW9ILE9BQU9KLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxFQUFFaEg7UUFBSTtJQUFFO0lBQ2pGLFNBQVNnRSxLQUFLdEQsQ0FBQztRQUFJQSxFQUFFZ0MsS0FBSyxZQUFZbUUsVUFBVWhELFFBQVFELE9BQU8sQ0FBQ2xELEVBQUVnQyxLQUFLLENBQUNzQyxDQUFDLEVBQUViLElBQUksQ0FBQ2tELFNBQVN2RCxVQUFVc0QsT0FBT0osQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUV0RztJQUFJO0lBQ3ZILFNBQVMyRyxRQUFRM0UsS0FBSztRQUFJeUUsT0FBTyxRQUFRekU7SUFBUTtJQUNqRCxTQUFTb0IsT0FBT3BCLEtBQUs7UUFBSXlFLE9BQU8sU0FBU3pFO0lBQVE7SUFDakQsU0FBUzBFLE9BQU8zRixDQUFDLEVBQUV1RCxDQUFDO1FBQUksSUFBSXZELEVBQUV1RCxJQUFJZ0MsRUFBRU0sS0FBSyxJQUFJTixFQUFFbkgsTUFBTSxFQUFFc0gsT0FBT0gsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEVBQUVBLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRTtJQUFHO0FBQ25GO0FBRU8sU0FBU08saUJBQWlCbkMsQ0FBQztJQUNoQyxJQUFJMUYsR0FBR2Q7SUFDUCxPQUFPYyxJQUFJLENBQUMsR0FBR21GLEtBQUssU0FBU0EsS0FBSyxTQUFTLFNBQVU3RSxDQUFDO1FBQUksTUFBTUE7SUFBRyxJQUFJNkUsS0FBSyxXQUFXbkYsQ0FBQyxDQUFDb0YsT0FBT0MsUUFBUSxDQUFDLEdBQUc7UUFBYyxPQUFPLElBQUk7SUFBRSxHQUFHckY7SUFDMUksU0FBU21GLEtBQUtsRixDQUFDLEVBQUU4QixDQUFDO1FBQUkvQixDQUFDLENBQUNDLEVBQUUsR0FBR3lGLENBQUMsQ0FBQ3pGLEVBQUUsR0FBRyxTQUFVcUYsQ0FBQztZQUFJLE9BQU8sQ0FBQ3BHLElBQUksQ0FBQ0EsQ0FBQUEsSUFBSztnQkFBRThELE9BQU9tRSxRQUFRekIsQ0FBQyxDQUFDekYsRUFBRSxDQUFDcUY7Z0JBQUtsRCxNQUFNO1lBQU0sSUFBSUwsSUFBSUEsRUFBRXVELEtBQUtBO1FBQUcsSUFBSXZEO0lBQUc7QUFDdkk7QUFFTyxTQUFTK0YsY0FBY3BDLENBQUM7SUFDN0IsSUFBSSxDQUFDTixPQUFPaUMsYUFBYSxFQUFFLE1BQU0sSUFBSTlILFVBQVU7SUFDL0MsSUFBSW9HLElBQUlELENBQUMsQ0FBQ04sT0FBT2lDLGFBQWEsQ0FBQyxFQUFFckg7SUFDakMsT0FBTzJGLElBQUlBLEVBQUV0RyxJQUFJLENBQUNxRyxLQUFNQSxDQUFBQSxJQUFJLE9BQU9TLGFBQWEsYUFBYUEsU0FBU1QsS0FBS0EsQ0FBQyxDQUFDTixPQUFPQyxRQUFRLENBQUMsSUFBSXJGLElBQUksQ0FBQyxHQUFHbUYsS0FBSyxTQUFTQSxLQUFLLFVBQVVBLEtBQUssV0FBV25GLENBQUMsQ0FBQ29GLE9BQU9pQyxhQUFhLENBQUMsR0FBRztRQUFjLE9BQU8sSUFBSTtJQUFFLEdBQUdySCxDQUFBQTtJQUM5TSxTQUFTbUYsS0FBS2xGLENBQUM7UUFBSUQsQ0FBQyxDQUFDQyxFQUFFLEdBQUd5RixDQUFDLENBQUN6RixFQUFFLElBQUksU0FBVXFGLENBQUM7WUFBSSxPQUFPLElBQUluQixRQUFRLFNBQVVELE9BQU8sRUFBRUUsTUFBTTtnQkFBSWtCLElBQUlJLENBQUMsQ0FBQ3pGLEVBQUUsQ0FBQ3FGLElBQUlvQyxPQUFPeEQsU0FBU0UsUUFBUWtCLEVBQUVsRCxJQUFJLEVBQUVrRCxFQUFFdEMsS0FBSztZQUFHO1FBQUk7SUFBRztJQUMvSixTQUFTMEUsT0FBT3hELE9BQU8sRUFBRUUsTUFBTSxFQUFFeEYsQ0FBQyxFQUFFMEcsQ0FBQztRQUFJbkIsUUFBUUQsT0FBTyxDQUFDb0IsR0FBR2IsSUFBSSxDQUFDLFNBQVNhLENBQUM7WUFBSXBCLFFBQVE7Z0JBQUVsQixPQUFPc0M7Z0JBQUdsRCxNQUFNeEQ7WUFBRTtRQUFJLEdBQUd3RjtJQUFTO0FBQzdIO0FBRU8sU0FBUzJELHFCQUFxQkMsTUFBTSxFQUFFQyxHQUFHO0lBQzlDLElBQUluSixPQUFPc0MsY0FBYyxFQUFFO1FBQUV0QyxPQUFPc0MsY0FBYyxDQUFDNEcsUUFBUSxPQUFPO1lBQUVoRixPQUFPaUY7UUFBSTtJQUFJLE9BQU87UUFBRUQsT0FBT0MsR0FBRyxHQUFHQTtJQUFLO0lBQzlHLE9BQU9EO0FBQ1Q7O0FBRUEsSUFBSUUscUJBQXFCcEosT0FBT2EsTUFBTSxHQUFJLFNBQVMrRixDQUFDLEVBQUVKLENBQUM7SUFDckR4RyxPQUFPc0MsY0FBYyxDQUFDc0UsR0FBRyxXQUFXO1FBQUVPLFlBQVk7UUFBTWpELE9BQU9zQztJQUFFO0FBQ25FLElBQUssU0FBU0ksQ0FBQyxFQUFFSixDQUFDO0lBQ2hCSSxDQUFDLENBQUMsVUFBVSxHQUFHSjtBQUNqQjtBQUVBLElBQUk2QyxVQUFVLFNBQVN6QyxDQUFDO0lBQ3RCeUMsVUFBVXJKLE9BQU9zSixtQkFBbUIsSUFBSSxTQUFVMUMsQ0FBQztRQUNqRCxJQUFJVyxLQUFLLEVBQUU7UUFDWCxJQUFLLElBQUlULEtBQUtGLEVBQUcsSUFBSTVHLE9BQU9LLFNBQVMsQ0FBQ0MsY0FBYyxDQUFDQyxJQUFJLENBQUNxRyxHQUFHRSxJQUFJUyxFQUFFLENBQUNBLEdBQUdsRyxNQUFNLENBQUMsR0FBR3lGO1FBQ2pGLE9BQU9TO0lBQ1Q7SUFDQSxPQUFPOEIsUUFBUXpDO0FBQ2pCO0FBRU8sU0FBUzJDLGFBQWFDLEdBQUc7SUFDOUIsSUFBSUEsT0FBT0EsSUFBSXZDLFVBQVUsRUFBRSxPQUFPdUM7SUFDbEMsSUFBSTdGLFNBQVMsQ0FBQztJQUNkLElBQUk2RixPQUFPLE1BQU07UUFBQSxJQUFLLElBQUkxQyxJQUFJdUMsUUFBUUcsTUFBTXRJLElBQUksR0FBR0EsSUFBSTRGLEVBQUV6RixNQUFNLEVBQUVILElBQUssSUFBSTRGLENBQUMsQ0FBQzVGLEVBQUUsS0FBSyxXQUFXeUYsZ0JBQWdCaEQsUUFBUTZGLEtBQUsxQyxDQUFDLENBQUM1RixFQUFFO0lBQUM7SUFDaElrSSxtQkFBbUJ6RixRQUFRNkY7SUFDM0IsT0FBTzdGO0FBQ1Q7QUFFTyxTQUFTOEYsZ0JBQWdCRCxHQUFHO0lBQ2pDLE9BQU8sT0FBUUEsSUFBSXZDLFVBQVUsR0FBSXVDLE1BQU07UUFBRUUsU0FBU0Y7SUFBSTtBQUN4RDtBQUVPLFNBQVNHLHVCQUF1QkMsUUFBUSxFQUFFQyxLQUFLLEVBQUUzRyxJQUFJLEVBQUVELENBQUM7SUFDN0QsSUFBSUMsU0FBUyxPQUFPLENBQUNELEdBQUcsTUFBTSxJQUFJeEMsVUFBVTtJQUM1QyxJQUFJLE9BQU9vSixVQUFVLGFBQWFELGFBQWFDLFNBQVMsQ0FBQzVHLElBQUksQ0FBQzRHLE1BQU1DLEdBQUcsQ0FBQ0YsV0FBVyxNQUFNLElBQUluSixVQUFVO0lBQ3ZHLE9BQU95QyxTQUFTLE1BQU1ELElBQUlDLFNBQVMsTUFBTUQsRUFBRTFDLElBQUksQ0FBQ3FKLFlBQVkzRyxJQUFJQSxFQUFFaUIsS0FBSyxHQUFHMkYsTUFBTWpHLEdBQUcsQ0FBQ2dHO0FBQ3RGO0FBRU8sU0FBU0csdUJBQXVCSCxRQUFRLEVBQUVDLEtBQUssRUFBRTNGLEtBQUssRUFBRWhCLElBQUksRUFBRUQsQ0FBQztJQUNwRSxJQUFJQyxTQUFTLEtBQUssTUFBTSxJQUFJekMsVUFBVTtJQUN0QyxJQUFJeUMsU0FBUyxPQUFPLENBQUNELEdBQUcsTUFBTSxJQUFJeEMsVUFBVTtJQUM1QyxJQUFJLE9BQU9vSixVQUFVLGFBQWFELGFBQWFDLFNBQVMsQ0FBQzVHLElBQUksQ0FBQzRHLE1BQU1DLEdBQUcsQ0FBQ0YsV0FBVyxNQUFNLElBQUluSixVQUFVO0lBQ3ZHLE9BQU8sU0FBVSxNQUFNd0MsRUFBRTFDLElBQUksQ0FBQ3FKLFVBQVUxRixTQUFTakIsSUFBSUEsRUFBRWlCLEtBQUssR0FBR0EsUUFBUTJGLE1BQU1oRyxHQUFHLENBQUMrRixVQUFVMUYsUUFBU0E7QUFDdEc7QUFFTyxTQUFTOEYsc0JBQXNCSCxLQUFLLEVBQUVELFFBQVE7SUFDbkQsSUFBSUEsYUFBYSxRQUFTLE9BQU9BLGFBQWEsWUFBWSxPQUFPQSxhQUFhLFlBQWEsTUFBTSxJQUFJbkosVUFBVTtJQUMvRyxPQUFPLE9BQU9vSixVQUFVLGFBQWFELGFBQWFDLFFBQVFBLE1BQU1DLEdBQUcsQ0FBQ0Y7QUFDdEU7QUFFTyxTQUFTSyx3QkFBd0JDLEdBQUcsRUFBRWhHLEtBQUssRUFBRWlHLEtBQUs7SUFDdkQsSUFBSWpHLFVBQVUsUUFBUUEsVUFBVSxLQUFLLEdBQUc7UUFDdEMsSUFBSSxPQUFPQSxVQUFVLFlBQVksT0FBT0EsVUFBVSxZQUFZLE1BQU0sSUFBSXpELFVBQVU7UUFDbEYsSUFBSTJKLFNBQVNDO1FBQ2IsSUFBSUYsT0FBTztZQUNULElBQUksQ0FBQzdELE9BQU9nRSxZQUFZLEVBQUUsTUFBTSxJQUFJN0osVUFBVTtZQUM5QzJKLFVBQVVsRyxLQUFLLENBQUNvQyxPQUFPZ0UsWUFBWSxDQUFDO1FBQ3RDO1FBQ0EsSUFBSUYsWUFBWSxLQUFLLEdBQUc7WUFDdEIsSUFBSSxDQUFDOUQsT0FBTzhELE9BQU8sRUFBRSxNQUFNLElBQUkzSixVQUFVO1lBQ3pDMkosVUFBVWxHLEtBQUssQ0FBQ29DLE9BQU84RCxPQUFPLENBQUM7WUFDL0IsSUFBSUQsT0FBT0UsUUFBUUQ7UUFDckI7UUFDQSxJQUFJLE9BQU9BLFlBQVksWUFBWSxNQUFNLElBQUkzSixVQUFVO1FBQ3ZELElBQUk0SixPQUFPRCxVQUFVO1lBQWEsSUFBSTtnQkFBRUMsTUFBTTlKLElBQUksQ0FBQyxJQUFJO1lBQUcsRUFBRSxPQUFPaUIsR0FBRztnQkFBRSxPQUFPNkQsUUFBUUMsTUFBTSxDQUFDOUQ7WUFBSTtRQUFFO1FBQ3BHMEksSUFBSUssS0FBSyxDQUFDN0csSUFBSSxDQUFDO1lBQUVRLE9BQU9BO1lBQU9rRyxTQUFTQTtZQUFTRCxPQUFPQTtRQUFNO0lBQ2hFLE9BQ0ssSUFBSUEsT0FBTztRQUNkRCxJQUFJSyxLQUFLLENBQUM3RyxJQUFJLENBQUM7WUFBRXlHLE9BQU87UUFBSztJQUMvQjtJQUNBLE9BQU9qRztBQUNUO0FBRUEsSUFBSXNHLG1CQUFtQixPQUFPQyxvQkFBb0IsYUFBYUEsa0JBQWtCLFNBQVVqRCxLQUFLLEVBQUVrRCxVQUFVLEVBQUVDLE9BQU87SUFDbkgsSUFBSW5KLElBQUksSUFBSW9KLE1BQU1EO0lBQ2xCLE9BQU9uSixFQUFFNEIsSUFBSSxHQUFHLG1CQUFtQjVCLEVBQUVnRyxLQUFLLEdBQUdBLE9BQU9oRyxFQUFFa0osVUFBVSxHQUFHQSxZQUFZbEo7QUFDakY7QUFFTyxTQUFTcUosbUJBQW1CWCxHQUFHO0lBQ3BDLFNBQVNZLEtBQUt0SixDQUFDO1FBQ2IwSSxJQUFJMUMsS0FBSyxHQUFHMEMsSUFBSWEsUUFBUSxHQUFHLElBQUlQLGlCQUFpQmhKLEdBQUcwSSxJQUFJMUMsS0FBSyxFQUFFLDhDQUE4Q2hHO1FBQzVHMEksSUFBSWEsUUFBUSxHQUFHO0lBQ2pCO0lBQ0EsSUFBSTdJLEdBQUdqQixJQUFJO0lBQ1gsU0FBU3dFO1FBQ1AsTUFBT3ZELElBQUlnSSxJQUFJSyxLQUFLLENBQUM3RCxHQUFHLEdBQUk7WUFDMUIsSUFBSTtnQkFDRixJQUFJLENBQUN4RSxFQUFFaUksS0FBSyxJQUFJbEosTUFBTSxHQUFHLE9BQU9BLElBQUksR0FBR2lKLElBQUlLLEtBQUssQ0FBQzdHLElBQUksQ0FBQ3hCLElBQUltRCxRQUFRRCxPQUFPLEdBQUdPLElBQUksQ0FBQ0Y7Z0JBQ2pGLElBQUl2RCxFQUFFa0ksT0FBTyxFQUFFO29CQUNiLElBQUl6RyxTQUFTekIsRUFBRWtJLE9BQU8sQ0FBQzdKLElBQUksQ0FBQzJCLEVBQUVnQyxLQUFLO29CQUNuQyxJQUFJaEMsRUFBRWlJLEtBQUssRUFBRSxPQUFPbEosS0FBSyxHQUFHb0UsUUFBUUQsT0FBTyxDQUFDekIsUUFBUWdDLElBQUksQ0FBQ0YsTUFBTSxTQUFTakUsQ0FBQzt3QkFBSXNKLEtBQUt0Sjt3QkFBSSxPQUFPaUU7b0JBQVE7Z0JBQ3ZHLE9BQ0t4RSxLQUFLO1lBQ1osRUFDQSxPQUFPTyxHQUFHO2dCQUNSc0osS0FBS3RKO1lBQ1A7UUFDRjtRQUNBLElBQUlQLE1BQU0sR0FBRyxPQUFPaUosSUFBSWEsUUFBUSxHQUFHMUYsUUFBUUMsTUFBTSxDQUFDNEUsSUFBSTFDLEtBQUssSUFBSW5DLFFBQVFELE9BQU87UUFDOUUsSUFBSThFLElBQUlhLFFBQVEsRUFBRSxNQUFNYixJQUFJMUMsS0FBSztJQUNuQztJQUNBLE9BQU8vQjtBQUNUO0FBRU8sU0FBU3VGLGlDQUFpQ0MsSUFBSSxFQUFFQyxXQUFXO0lBQ2hFLElBQUksT0FBT0QsU0FBUyxZQUFZLFdBQVdFLElBQUksQ0FBQ0YsT0FBTztRQUNuRCxPQUFPQSxLQUFLRyxPQUFPLENBQUMsb0RBQW9ELFNBQVV2RSxDQUFDLEVBQUV3RSxHQUFHLEVBQUV2TCxDQUFDLEVBQUV3TCxHQUFHLEVBQUVDLEVBQUU7WUFDaEcsT0FBT0YsTUFBTUgsY0FBYyxTQUFTLFFBQVFwTCxLQUFNLEVBQUN3TCxPQUFPLENBQUNDLEVBQUMsSUFBSzFFLElBQUsvRyxJQUFJd0wsTUFBTSxNQUFNQyxHQUFHQyxXQUFXLEtBQUs7UUFDN0c7SUFDSjtJQUNBLE9BQU9QO0FBQ1Q7QUFFQSxpRUFBZTtJQUNieks7SUFDQU07SUFDQVM7SUFDQUs7SUFDQVc7SUFDQUc7SUFDQXNCO0lBQ0FJO0lBQ0FHO0lBQ0FJO0lBQ0FJO0lBQ0FhO0lBQ0FlO0lBQ0FTO0lBQ0FDO0lBQ0FDO0lBQ0FHO0lBQ0FDO0lBQ0FLO0lBQ0FNO0lBQ0FDO0lBQ0FTO0lBQ0FDO0lBQ0FDO0lBQ0FNO0lBQ0FFO0lBQ0FFO0lBQ0FJO0lBQ0FDO0lBQ0FDO0lBQ0FZO0lBQ0FHO0FBQ0YsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3VzdG9tLWdyb3VwLWNyZWF0b3IvLi9ub2RlX21vZHVsZXMvd2ViY3J5cHRvLWNvcmUvbm9kZV9tb2R1bGVzL3RzbGliL3RzbGliLmVzNi5tanM/NGVmNiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqXG5Db3B5cmlnaHQgKGMpIE1pY3Jvc29mdCBDb3Jwb3JhdGlvbi5cblxuUGVybWlzc2lvbiB0byB1c2UsIGNvcHksIG1vZGlmeSwgYW5kL29yIGRpc3RyaWJ1dGUgdGhpcyBzb2Z0d2FyZSBmb3IgYW55XG5wdXJwb3NlIHdpdGggb3Igd2l0aG91dCBmZWUgaXMgaGVyZWJ5IGdyYW50ZWQuXG5cblRIRSBTT0ZUV0FSRSBJUyBQUk9WSURFRCBcIkFTIElTXCIgQU5EIFRIRSBBVVRIT1IgRElTQ0xBSU1TIEFMTCBXQVJSQU5USUVTIFdJVEhcblJFR0FSRCBUTyBUSElTIFNPRlRXQVJFIElOQ0xVRElORyBBTEwgSU1QTElFRCBXQVJSQU5USUVTIE9GIE1FUkNIQU5UQUJJTElUWVxuQU5EIEZJVE5FU1MuIElOIE5PIEVWRU5UIFNIQUxMIFRIRSBBVVRIT1IgQkUgTElBQkxFIEZPUiBBTlkgU1BFQ0lBTCwgRElSRUNULFxuSU5ESVJFQ1QsIE9SIENPTlNFUVVFTlRJQUwgREFNQUdFUyBPUiBBTlkgREFNQUdFUyBXSEFUU09FVkVSIFJFU1VMVElORyBGUk9NXG5MT1NTIE9GIFVTRSwgREFUQSBPUiBQUk9GSVRTLCBXSEVUSEVSIElOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgTkVHTElHRU5DRSBPUlxuT1RIRVIgVE9SVElPVVMgQUNUSU9OLCBBUklTSU5HIE9VVCBPRiBPUiBJTiBDT05ORUNUSU9OIFdJVEggVEhFIFVTRSBPUlxuUEVSRk9STUFOQ0UgT0YgVEhJUyBTT0ZUV0FSRS5cbioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqICovXG4vKiBnbG9iYWwgUmVmbGVjdCwgUHJvbWlzZSwgU3VwcHJlc3NlZEVycm9yLCBTeW1ib2wsIEl0ZXJhdG9yICovXG5cbnZhciBleHRlbmRTdGF0aWNzID0gZnVuY3Rpb24oZCwgYikge1xuICBleHRlbmRTdGF0aWNzID0gT2JqZWN0LnNldFByb3RvdHlwZU9mIHx8XG4gICAgICAoeyBfX3Byb3RvX186IFtdIH0gaW5zdGFuY2VvZiBBcnJheSAmJiBmdW5jdGlvbiAoZCwgYikgeyBkLl9fcHJvdG9fXyA9IGI7IH0pIHx8XG4gICAgICBmdW5jdGlvbiAoZCwgYikgeyBmb3IgKHZhciBwIGluIGIpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoYiwgcCkpIGRbcF0gPSBiW3BdOyB9O1xuICByZXR1cm4gZXh0ZW5kU3RhdGljcyhkLCBiKTtcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2V4dGVuZHMoZCwgYikge1xuICBpZiAodHlwZW9mIGIgIT09IFwiZnVuY3Rpb25cIiAmJiBiICE9PSBudWxsKVxuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNsYXNzIGV4dGVuZHMgdmFsdWUgXCIgKyBTdHJpbmcoYikgKyBcIiBpcyBub3QgYSBjb25zdHJ1Y3RvciBvciBudWxsXCIpO1xuICBleHRlbmRTdGF0aWNzKGQsIGIpO1xuICBmdW5jdGlvbiBfXygpIHsgdGhpcy5jb25zdHJ1Y3RvciA9IGQ7IH1cbiAgZC5wcm90b3R5cGUgPSBiID09PSBudWxsID8gT2JqZWN0LmNyZWF0ZShiKSA6IChfXy5wcm90b3R5cGUgPSBiLnByb3RvdHlwZSwgbmV3IF9fKCkpO1xufVxuXG5leHBvcnQgdmFyIF9fYXNzaWduID0gZnVuY3Rpb24oKSB7XG4gIF9fYXNzaWduID0gT2JqZWN0LmFzc2lnbiB8fCBmdW5jdGlvbiBfX2Fzc2lnbih0KSB7XG4gICAgICBmb3IgKHZhciBzLCBpID0gMSwgbiA9IGFyZ3VtZW50cy5sZW5ndGg7IGkgPCBuOyBpKyspIHtcbiAgICAgICAgICBzID0gYXJndW1lbnRzW2ldO1xuICAgICAgICAgIGZvciAodmFyIHAgaW4gcykgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzLCBwKSkgdFtwXSA9IHNbcF07XG4gICAgICB9XG4gICAgICByZXR1cm4gdDtcbiAgfVxuICByZXR1cm4gX19hc3NpZ24uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fcmVzdChzLCBlKSB7XG4gIHZhciB0ID0ge307XG4gIGZvciAodmFyIHAgaW4gcykgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzLCBwKSAmJiBlLmluZGV4T2YocCkgPCAwKVxuICAgICAgdFtwXSA9IHNbcF07XG4gIGlmIChzICE9IG51bGwgJiYgdHlwZW9mIE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMgPT09IFwiZnVuY3Rpb25cIilcbiAgICAgIGZvciAodmFyIGkgPSAwLCBwID0gT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyhzKTsgaSA8IHAubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICBpZiAoZS5pbmRleE9mKHBbaV0pIDwgMCAmJiBPYmplY3QucHJvdG90eXBlLnByb3BlcnR5SXNFbnVtZXJhYmxlLmNhbGwocywgcFtpXSkpXG4gICAgICAgICAgICAgIHRbcFtpXV0gPSBzW3BbaV1dO1xuICAgICAgfVxuICByZXR1cm4gdDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fZGVjb3JhdGUoZGVjb3JhdG9ycywgdGFyZ2V0LCBrZXksIGRlc2MpIHtcbiAgdmFyIGMgPSBhcmd1bWVudHMubGVuZ3RoLCByID0gYyA8IDMgPyB0YXJnZXQgOiBkZXNjID09PSBudWxsID8gZGVzYyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IodGFyZ2V0LCBrZXkpIDogZGVzYywgZDtcbiAgaWYgKHR5cGVvZiBSZWZsZWN0ID09PSBcIm9iamVjdFwiICYmIHR5cGVvZiBSZWZsZWN0LmRlY29yYXRlID09PSBcImZ1bmN0aW9uXCIpIHIgPSBSZWZsZWN0LmRlY29yYXRlKGRlY29yYXRvcnMsIHRhcmdldCwga2V5LCBkZXNjKTtcbiAgZWxzZSBmb3IgKHZhciBpID0gZGVjb3JhdG9ycy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkgaWYgKGQgPSBkZWNvcmF0b3JzW2ldKSByID0gKGMgPCAzID8gZChyKSA6IGMgPiAzID8gZCh0YXJnZXQsIGtleSwgcikgOiBkKHRhcmdldCwga2V5KSkgfHwgcjtcbiAgcmV0dXJuIGMgPiAzICYmIHIgJiYgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwga2V5LCByKSwgcjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fcGFyYW0ocGFyYW1JbmRleCwgZGVjb3JhdG9yKSB7XG4gIHJldHVybiBmdW5jdGlvbiAodGFyZ2V0LCBrZXkpIHsgZGVjb3JhdG9yKHRhcmdldCwga2V5LCBwYXJhbUluZGV4KTsgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gX19lc0RlY29yYXRlKGN0b3IsIGRlc2NyaXB0b3JJbiwgZGVjb3JhdG9ycywgY29udGV4dEluLCBpbml0aWFsaXplcnMsIGV4dHJhSW5pdGlhbGl6ZXJzKSB7XG4gIGZ1bmN0aW9uIGFjY2VwdChmKSB7IGlmIChmICE9PSB2b2lkIDAgJiYgdHlwZW9mIGYgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkZ1bmN0aW9uIGV4cGVjdGVkXCIpOyByZXR1cm4gZjsgfVxuICB2YXIga2luZCA9IGNvbnRleHRJbi5raW5kLCBrZXkgPSBraW5kID09PSBcImdldHRlclwiID8gXCJnZXRcIiA6IGtpbmQgPT09IFwic2V0dGVyXCIgPyBcInNldFwiIDogXCJ2YWx1ZVwiO1xuICB2YXIgdGFyZ2V0ID0gIWRlc2NyaXB0b3JJbiAmJiBjdG9yID8gY29udGV4dEluW1wic3RhdGljXCJdID8gY3RvciA6IGN0b3IucHJvdG90eXBlIDogbnVsbDtcbiAgdmFyIGRlc2NyaXB0b3IgPSBkZXNjcmlwdG9ySW4gfHwgKHRhcmdldCA/IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IodGFyZ2V0LCBjb250ZXh0SW4ubmFtZSkgOiB7fSk7XG4gIHZhciBfLCBkb25lID0gZmFsc2U7XG4gIGZvciAodmFyIGkgPSBkZWNvcmF0b3JzLmxlbmd0aCAtIDE7IGkgPj0gMDsgaS0tKSB7XG4gICAgICB2YXIgY29udGV4dCA9IHt9O1xuICAgICAgZm9yICh2YXIgcCBpbiBjb250ZXh0SW4pIGNvbnRleHRbcF0gPSBwID09PSBcImFjY2Vzc1wiID8ge30gOiBjb250ZXh0SW5bcF07XG4gICAgICBmb3IgKHZhciBwIGluIGNvbnRleHRJbi5hY2Nlc3MpIGNvbnRleHQuYWNjZXNzW3BdID0gY29udGV4dEluLmFjY2Vzc1twXTtcbiAgICAgIGNvbnRleHQuYWRkSW5pdGlhbGl6ZXIgPSBmdW5jdGlvbiAoZikgeyBpZiAoZG9uZSkgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNhbm5vdCBhZGQgaW5pdGlhbGl6ZXJzIGFmdGVyIGRlY29yYXRpb24gaGFzIGNvbXBsZXRlZFwiKTsgZXh0cmFJbml0aWFsaXplcnMucHVzaChhY2NlcHQoZiB8fCBudWxsKSk7IH07XG4gICAgICB2YXIgcmVzdWx0ID0gKDAsIGRlY29yYXRvcnNbaV0pKGtpbmQgPT09IFwiYWNjZXNzb3JcIiA/IHsgZ2V0OiBkZXNjcmlwdG9yLmdldCwgc2V0OiBkZXNjcmlwdG9yLnNldCB9IDogZGVzY3JpcHRvcltrZXldLCBjb250ZXh0KTtcbiAgICAgIGlmIChraW5kID09PSBcImFjY2Vzc29yXCIpIHtcbiAgICAgICAgICBpZiAocmVzdWx0ID09PSB2b2lkIDApIGNvbnRpbnVlO1xuICAgICAgICAgIGlmIChyZXN1bHQgPT09IG51bGwgfHwgdHlwZW9mIHJlc3VsdCAhPT0gXCJvYmplY3RcIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIk9iamVjdCBleHBlY3RlZFwiKTtcbiAgICAgICAgICBpZiAoXyA9IGFjY2VwdChyZXN1bHQuZ2V0KSkgZGVzY3JpcHRvci5nZXQgPSBfO1xuICAgICAgICAgIGlmIChfID0gYWNjZXB0KHJlc3VsdC5zZXQpKSBkZXNjcmlwdG9yLnNldCA9IF87XG4gICAgICAgICAgaWYgKF8gPSBhY2NlcHQocmVzdWx0LmluaXQpKSBpbml0aWFsaXplcnMudW5zaGlmdChfKTtcbiAgICAgIH1cbiAgICAgIGVsc2UgaWYgKF8gPSBhY2NlcHQocmVzdWx0KSkge1xuICAgICAgICAgIGlmIChraW5kID09PSBcImZpZWxkXCIpIGluaXRpYWxpemVycy51bnNoaWZ0KF8pO1xuICAgICAgICAgIGVsc2UgZGVzY3JpcHRvcltrZXldID0gXztcbiAgICAgIH1cbiAgfVxuICBpZiAodGFyZ2V0KSBPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBjb250ZXh0SW4ubmFtZSwgZGVzY3JpcHRvcik7XG4gIGRvbmUgPSB0cnVlO1xufTtcblxuZXhwb3J0IGZ1bmN0aW9uIF9fcnVuSW5pdGlhbGl6ZXJzKHRoaXNBcmcsIGluaXRpYWxpemVycywgdmFsdWUpIHtcbiAgdmFyIHVzZVZhbHVlID0gYXJndW1lbnRzLmxlbmd0aCA+IDI7XG4gIGZvciAodmFyIGkgPSAwOyBpIDwgaW5pdGlhbGl6ZXJzLmxlbmd0aDsgaSsrKSB7XG4gICAgICB2YWx1ZSA9IHVzZVZhbHVlID8gaW5pdGlhbGl6ZXJzW2ldLmNhbGwodGhpc0FyZywgdmFsdWUpIDogaW5pdGlhbGl6ZXJzW2ldLmNhbGwodGhpc0FyZyk7XG4gIH1cbiAgcmV0dXJuIHVzZVZhbHVlID8gdmFsdWUgOiB2b2lkIDA7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gX19wcm9wS2V5KHgpIHtcbiAgcmV0dXJuIHR5cGVvZiB4ID09PSBcInN5bWJvbFwiID8geCA6IFwiXCIuY29uY2F0KHgpO1xufTtcblxuZXhwb3J0IGZ1bmN0aW9uIF9fc2V0RnVuY3Rpb25OYW1lKGYsIG5hbWUsIHByZWZpeCkge1xuICBpZiAodHlwZW9mIG5hbWUgPT09IFwic3ltYm9sXCIpIG5hbWUgPSBuYW1lLmRlc2NyaXB0aW9uID8gXCJbXCIuY29uY2F0KG5hbWUuZGVzY3JpcHRpb24sIFwiXVwiKSA6IFwiXCI7XG4gIHJldHVybiBPYmplY3QuZGVmaW5lUHJvcGVydHkoZiwgXCJuYW1lXCIsIHsgY29uZmlndXJhYmxlOiB0cnVlLCB2YWx1ZTogcHJlZml4ID8gXCJcIi5jb25jYXQocHJlZml4LCBcIiBcIiwgbmFtZSkgOiBuYW1lIH0pO1xufTtcblxuZXhwb3J0IGZ1bmN0aW9uIF9fbWV0YWRhdGEobWV0YWRhdGFLZXksIG1ldGFkYXRhVmFsdWUpIHtcbiAgaWYgKHR5cGVvZiBSZWZsZWN0ID09PSBcIm9iamVjdFwiICYmIHR5cGVvZiBSZWZsZWN0Lm1ldGFkYXRhID09PSBcImZ1bmN0aW9uXCIpIHJldHVybiBSZWZsZWN0Lm1ldGFkYXRhKG1ldGFkYXRhS2V5LCBtZXRhZGF0YVZhbHVlKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fYXdhaXRlcih0aGlzQXJnLCBfYXJndW1lbnRzLCBQLCBnZW5lcmF0b3IpIHtcbiAgZnVuY3Rpb24gYWRvcHQodmFsdWUpIHsgcmV0dXJuIHZhbHVlIGluc3RhbmNlb2YgUCA/IHZhbHVlIDogbmV3IFAoZnVuY3Rpb24gKHJlc29sdmUpIHsgcmVzb2x2ZSh2YWx1ZSk7IH0pOyB9XG4gIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgZnVuY3Rpb24gZnVsZmlsbGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yLm5leHQodmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgZnVuY3Rpb24gcmVqZWN0ZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3JbXCJ0aHJvd1wiXSh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxuICAgICAgc3RlcCgoZ2VuZXJhdG9yID0gZ2VuZXJhdG9yLmFwcGx5KHRoaXNBcmcsIF9hcmd1bWVudHMgfHwgW10pKS5uZXh0KCkpO1xuICB9KTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fZ2VuZXJhdG9yKHRoaXNBcmcsIGJvZHkpIHtcbiAgdmFyIF8gPSB7IGxhYmVsOiAwLCBzZW50OiBmdW5jdGlvbigpIHsgaWYgKHRbMF0gJiAxKSB0aHJvdyB0WzFdOyByZXR1cm4gdFsxXTsgfSwgdHJ5czogW10sIG9wczogW10gfSwgZiwgeSwgdCwgZyA9IE9iamVjdC5jcmVhdGUoKHR5cGVvZiBJdGVyYXRvciA9PT0gXCJmdW5jdGlvblwiID8gSXRlcmF0b3IgOiBPYmplY3QpLnByb3RvdHlwZSk7XG4gIHJldHVybiBnLm5leHQgPSB2ZXJiKDApLCBnW1widGhyb3dcIl0gPSB2ZXJiKDEpLCBnW1wicmV0dXJuXCJdID0gdmVyYigyKSwgdHlwZW9mIFN5bWJvbCA9PT0gXCJmdW5jdGlvblwiICYmIChnW1N5bWJvbC5pdGVyYXRvcl0gPSBmdW5jdGlvbigpIHsgcmV0dXJuIHRoaXM7IH0pLCBnO1xuICBmdW5jdGlvbiB2ZXJiKG4pIHsgcmV0dXJuIGZ1bmN0aW9uICh2KSB7IHJldHVybiBzdGVwKFtuLCB2XSk7IH07IH1cbiAgZnVuY3Rpb24gc3RlcChvcCkge1xuICAgICAgaWYgKGYpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJHZW5lcmF0b3IgaXMgYWxyZWFkeSBleGVjdXRpbmcuXCIpO1xuICAgICAgd2hpbGUgKGcgJiYgKGcgPSAwLCBvcFswXSAmJiAoXyA9IDApKSwgXykgdHJ5IHtcbiAgICAgICAgICBpZiAoZiA9IDEsIHkgJiYgKHQgPSBvcFswXSAmIDIgPyB5W1wicmV0dXJuXCJdIDogb3BbMF0gPyB5W1widGhyb3dcIl0gfHwgKCh0ID0geVtcInJldHVyblwiXSkgJiYgdC5jYWxsKHkpLCAwKSA6IHkubmV4dCkgJiYgISh0ID0gdC5jYWxsKHksIG9wWzFdKSkuZG9uZSkgcmV0dXJuIHQ7XG4gICAgICAgICAgaWYgKHkgPSAwLCB0KSBvcCA9IFtvcFswXSAmIDIsIHQudmFsdWVdO1xuICAgICAgICAgIHN3aXRjaCAob3BbMF0pIHtcbiAgICAgICAgICAgICAgY2FzZSAwOiBjYXNlIDE6IHQgPSBvcDsgYnJlYWs7XG4gICAgICAgICAgICAgIGNhc2UgNDogXy5sYWJlbCsrOyByZXR1cm4geyB2YWx1ZTogb3BbMV0sIGRvbmU6IGZhbHNlIH07XG4gICAgICAgICAgICAgIGNhc2UgNTogXy5sYWJlbCsrOyB5ID0gb3BbMV07IG9wID0gWzBdOyBjb250aW51ZTtcbiAgICAgICAgICAgICAgY2FzZSA3OiBvcCA9IF8ub3BzLnBvcCgpOyBfLnRyeXMucG9wKCk7IGNvbnRpbnVlO1xuICAgICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgICAgaWYgKCEodCA9IF8udHJ5cywgdCA9IHQubGVuZ3RoID4gMCAmJiB0W3QubGVuZ3RoIC0gMV0pICYmIChvcFswXSA9PT0gNiB8fCBvcFswXSA9PT0gMikpIHsgXyA9IDA7IGNvbnRpbnVlOyB9XG4gICAgICAgICAgICAgICAgICBpZiAob3BbMF0gPT09IDMgJiYgKCF0IHx8IChvcFsxXSA+IHRbMF0gJiYgb3BbMV0gPCB0WzNdKSkpIHsgXy5sYWJlbCA9IG9wWzFdOyBicmVhazsgfVxuICAgICAgICAgICAgICAgICAgaWYgKG9wWzBdID09PSA2ICYmIF8ubGFiZWwgPCB0WzFdKSB7IF8ubGFiZWwgPSB0WzFdOyB0ID0gb3A7IGJyZWFrOyB9XG4gICAgICAgICAgICAgICAgICBpZiAodCAmJiBfLmxhYmVsIDwgdFsyXSkgeyBfLmxhYmVsID0gdFsyXTsgXy5vcHMucHVzaChvcCk7IGJyZWFrOyB9XG4gICAgICAgICAgICAgICAgICBpZiAodFsyXSkgXy5vcHMucG9wKCk7XG4gICAgICAgICAgICAgICAgICBfLnRyeXMucG9wKCk7IGNvbnRpbnVlO1xuICAgICAgICAgIH1cbiAgICAgICAgICBvcCA9IGJvZHkuY2FsbCh0aGlzQXJnLCBfKTtcbiAgICAgIH0gY2F0Y2ggKGUpIHsgb3AgPSBbNiwgZV07IHkgPSAwOyB9IGZpbmFsbHkgeyBmID0gdCA9IDA7IH1cbiAgICAgIGlmIChvcFswXSAmIDUpIHRocm93IG9wWzFdOyByZXR1cm4geyB2YWx1ZTogb3BbMF0gPyBvcFsxXSA6IHZvaWQgMCwgZG9uZTogdHJ1ZSB9O1xuICB9XG59XG5cbmV4cG9ydCB2YXIgX19jcmVhdGVCaW5kaW5nID0gT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICB2YXIgZGVzYyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IobSwgayk7XG4gIGlmICghZGVzYyB8fCAoXCJnZXRcIiBpbiBkZXNjID8gIW0uX19lc01vZHVsZSA6IGRlc2Mud3JpdGFibGUgfHwgZGVzYy5jb25maWd1cmFibGUpKSB7XG4gICAgICBkZXNjID0geyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9O1xuICB9XG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgZGVzYyk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICBvW2syXSA9IG1ba107XG59KTtcblxuZXhwb3J0IGZ1bmN0aW9uIF9fZXhwb3J0U3RhcihtLCBvKSB7XG4gIGZvciAodmFyIHAgaW4gbSkgaWYgKHAgIT09IFwiZGVmYXVsdFwiICYmICFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwobywgcCkpIF9fY3JlYXRlQmluZGluZyhvLCBtLCBwKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fdmFsdWVzKG8pIHtcbiAgdmFyIHMgPSB0eXBlb2YgU3ltYm9sID09PSBcImZ1bmN0aW9uXCIgJiYgU3ltYm9sLml0ZXJhdG9yLCBtID0gcyAmJiBvW3NdLCBpID0gMDtcbiAgaWYgKG0pIHJldHVybiBtLmNhbGwobyk7XG4gIGlmIChvICYmIHR5cGVvZiBvLmxlbmd0aCA9PT0gXCJudW1iZXJcIikgcmV0dXJuIHtcbiAgICAgIG5leHQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICBpZiAobyAmJiBpID49IG8ubGVuZ3RoKSBvID0gdm9pZCAwO1xuICAgICAgICAgIHJldHVybiB7IHZhbHVlOiBvICYmIG9baSsrXSwgZG9uZTogIW8gfTtcbiAgICAgIH1cbiAgfTtcbiAgdGhyb3cgbmV3IFR5cGVFcnJvcihzID8gXCJPYmplY3QgaXMgbm90IGl0ZXJhYmxlLlwiIDogXCJTeW1ib2wuaXRlcmF0b3IgaXMgbm90IGRlZmluZWQuXCIpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19yZWFkKG8sIG4pIHtcbiAgdmFyIG0gPSB0eXBlb2YgU3ltYm9sID09PSBcImZ1bmN0aW9uXCIgJiYgb1tTeW1ib2wuaXRlcmF0b3JdO1xuICBpZiAoIW0pIHJldHVybiBvO1xuICB2YXIgaSA9IG0uY2FsbChvKSwgciwgYXIgPSBbXSwgZTtcbiAgdHJ5IHtcbiAgICAgIHdoaWxlICgobiA9PT0gdm9pZCAwIHx8IG4tLSA+IDApICYmICEociA9IGkubmV4dCgpKS5kb25lKSBhci5wdXNoKHIudmFsdWUpO1xuICB9XG4gIGNhdGNoIChlcnJvcikgeyBlID0geyBlcnJvcjogZXJyb3IgfTsgfVxuICBmaW5hbGx5IHtcbiAgICAgIHRyeSB7XG4gICAgICAgICAgaWYgKHIgJiYgIXIuZG9uZSAmJiAobSA9IGlbXCJyZXR1cm5cIl0pKSBtLmNhbGwoaSk7XG4gICAgICB9XG4gICAgICBmaW5hbGx5IHsgaWYgKGUpIHRocm93IGUuZXJyb3I7IH1cbiAgfVxuICByZXR1cm4gYXI7XG59XG5cbi8qKiBAZGVwcmVjYXRlZCAqL1xuZXhwb3J0IGZ1bmN0aW9uIF9fc3ByZWFkKCkge1xuICBmb3IgKHZhciBhciA9IFtdLCBpID0gMDsgaSA8IGFyZ3VtZW50cy5sZW5ndGg7IGkrKylcbiAgICAgIGFyID0gYXIuY29uY2F0KF9fcmVhZChhcmd1bWVudHNbaV0pKTtcbiAgcmV0dXJuIGFyO1xufVxuXG4vKiogQGRlcHJlY2F0ZWQgKi9cbmV4cG9ydCBmdW5jdGlvbiBfX3NwcmVhZEFycmF5cygpIHtcbiAgZm9yICh2YXIgcyA9IDAsIGkgPSAwLCBpbCA9IGFyZ3VtZW50cy5sZW5ndGg7IGkgPCBpbDsgaSsrKSBzICs9IGFyZ3VtZW50c1tpXS5sZW5ndGg7XG4gIGZvciAodmFyIHIgPSBBcnJheShzKSwgayA9IDAsIGkgPSAwOyBpIDwgaWw7IGkrKylcbiAgICAgIGZvciAodmFyIGEgPSBhcmd1bWVudHNbaV0sIGogPSAwLCBqbCA9IGEubGVuZ3RoOyBqIDwgamw7IGorKywgaysrKVxuICAgICAgICAgIHJba10gPSBhW2pdO1xuICByZXR1cm4gcjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fc3ByZWFkQXJyYXkodG8sIGZyb20sIHBhY2spIHtcbiAgaWYgKHBhY2sgfHwgYXJndW1lbnRzLmxlbmd0aCA9PT0gMikgZm9yICh2YXIgaSA9IDAsIGwgPSBmcm9tLmxlbmd0aCwgYXI7IGkgPCBsOyBpKyspIHtcbiAgICAgIGlmIChhciB8fCAhKGkgaW4gZnJvbSkpIHtcbiAgICAgICAgICBpZiAoIWFyKSBhciA9IEFycmF5LnByb3RvdHlwZS5zbGljZS5jYWxsKGZyb20sIDAsIGkpO1xuICAgICAgICAgIGFyW2ldID0gZnJvbVtpXTtcbiAgICAgIH1cbiAgfVxuICByZXR1cm4gdG8uY29uY2F0KGFyIHx8IEFycmF5LnByb3RvdHlwZS5zbGljZS5jYWxsKGZyb20pKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fYXdhaXQodikge1xuICByZXR1cm4gdGhpcyBpbnN0YW5jZW9mIF9fYXdhaXQgPyAodGhpcy52ID0gdiwgdGhpcykgOiBuZXcgX19hd2FpdCh2KTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fYXN5bmNHZW5lcmF0b3IodGhpc0FyZywgX2FyZ3VtZW50cywgZ2VuZXJhdG9yKSB7XG4gIGlmICghU3ltYm9sLmFzeW5jSXRlcmF0b3IpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJTeW1ib2wuYXN5bmNJdGVyYXRvciBpcyBub3QgZGVmaW5lZC5cIik7XG4gIHZhciBnID0gZ2VuZXJhdG9yLmFwcGx5KHRoaXNBcmcsIF9hcmd1bWVudHMgfHwgW10pLCBpLCBxID0gW107XG4gIHJldHVybiBpID0gT2JqZWN0LmNyZWF0ZSgodHlwZW9mIEFzeW5jSXRlcmF0b3IgPT09IFwiZnVuY3Rpb25cIiA/IEFzeW5jSXRlcmF0b3IgOiBPYmplY3QpLnByb3RvdHlwZSksIHZlcmIoXCJuZXh0XCIpLCB2ZXJiKFwidGhyb3dcIiksIHZlcmIoXCJyZXR1cm5cIiwgYXdhaXRSZXR1cm4pLCBpW1N5bWJvbC5hc3luY0l0ZXJhdG9yXSA9IGZ1bmN0aW9uICgpIHsgcmV0dXJuIHRoaXM7IH0sIGk7XG4gIGZ1bmN0aW9uIGF3YWl0UmV0dXJuKGYpIHsgcmV0dXJuIGZ1bmN0aW9uICh2KSB7IHJldHVybiBQcm9taXNlLnJlc29sdmUodikudGhlbihmLCByZWplY3QpOyB9OyB9XG4gIGZ1bmN0aW9uIHZlcmIobiwgZikgeyBpZiAoZ1tuXSkgeyBpW25dID0gZnVuY3Rpb24gKHYpIHsgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChhLCBiKSB7IHEucHVzaChbbiwgdiwgYSwgYl0pID4gMSB8fCByZXN1bWUobiwgdik7IH0pOyB9OyBpZiAoZikgaVtuXSA9IGYoaVtuXSk7IH0gfVxuICBmdW5jdGlvbiByZXN1bWUobiwgdikgeyB0cnkgeyBzdGVwKGdbbl0odikpOyB9IGNhdGNoIChlKSB7IHNldHRsZShxWzBdWzNdLCBlKTsgfSB9XG4gIGZ1bmN0aW9uIHN0ZXAocikgeyByLnZhbHVlIGluc3RhbmNlb2YgX19hd2FpdCA/IFByb21pc2UucmVzb2x2ZShyLnZhbHVlLnYpLnRoZW4oZnVsZmlsbCwgcmVqZWN0KSA6IHNldHRsZShxWzBdWzJdLCByKTsgfVxuICBmdW5jdGlvbiBmdWxmaWxsKHZhbHVlKSB7IHJlc3VtZShcIm5leHRcIiwgdmFsdWUpOyB9XG4gIGZ1bmN0aW9uIHJlamVjdCh2YWx1ZSkgeyByZXN1bWUoXCJ0aHJvd1wiLCB2YWx1ZSk7IH1cbiAgZnVuY3Rpb24gc2V0dGxlKGYsIHYpIHsgaWYgKGYodiksIHEuc2hpZnQoKSwgcS5sZW5ndGgpIHJlc3VtZShxWzBdWzBdLCBxWzBdWzFdKTsgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gX19hc3luY0RlbGVnYXRvcihvKSB7XG4gIHZhciBpLCBwO1xuICByZXR1cm4gaSA9IHt9LCB2ZXJiKFwibmV4dFwiKSwgdmVyYihcInRocm93XCIsIGZ1bmN0aW9uIChlKSB7IHRocm93IGU7IH0pLCB2ZXJiKFwicmV0dXJuXCIpLCBpW1N5bWJvbC5pdGVyYXRvcl0gPSBmdW5jdGlvbiAoKSB7IHJldHVybiB0aGlzOyB9LCBpO1xuICBmdW5jdGlvbiB2ZXJiKG4sIGYpIHsgaVtuXSA9IG9bbl0gPyBmdW5jdGlvbiAodikgeyByZXR1cm4gKHAgPSAhcCkgPyB7IHZhbHVlOiBfX2F3YWl0KG9bbl0odikpLCBkb25lOiBmYWxzZSB9IDogZiA/IGYodikgOiB2OyB9IDogZjsgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gX19hc3luY1ZhbHVlcyhvKSB7XG4gIGlmICghU3ltYm9sLmFzeW5jSXRlcmF0b3IpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJTeW1ib2wuYXN5bmNJdGVyYXRvciBpcyBub3QgZGVmaW5lZC5cIik7XG4gIHZhciBtID0gb1tTeW1ib2wuYXN5bmNJdGVyYXRvcl0sIGk7XG4gIHJldHVybiBtID8gbS5jYWxsKG8pIDogKG8gPSB0eXBlb2YgX192YWx1ZXMgPT09IFwiZnVuY3Rpb25cIiA/IF9fdmFsdWVzKG8pIDogb1tTeW1ib2wuaXRlcmF0b3JdKCksIGkgPSB7fSwgdmVyYihcIm5leHRcIiksIHZlcmIoXCJ0aHJvd1wiKSwgdmVyYihcInJldHVyblwiKSwgaVtTeW1ib2wuYXN5bmNJdGVyYXRvcl0gPSBmdW5jdGlvbiAoKSB7IHJldHVybiB0aGlzOyB9LCBpKTtcbiAgZnVuY3Rpb24gdmVyYihuKSB7IGlbbl0gPSBvW25dICYmIGZ1bmN0aW9uICh2KSB7IHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7IHYgPSBvW25dKHYpLCBzZXR0bGUocmVzb2x2ZSwgcmVqZWN0LCB2LmRvbmUsIHYudmFsdWUpOyB9KTsgfTsgfVxuICBmdW5jdGlvbiBzZXR0bGUocmVzb2x2ZSwgcmVqZWN0LCBkLCB2KSB7IFByb21pc2UucmVzb2x2ZSh2KS50aGVuKGZ1bmN0aW9uKHYpIHsgcmVzb2x2ZSh7IHZhbHVlOiB2LCBkb25lOiBkIH0pOyB9LCByZWplY3QpOyB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX21ha2VUZW1wbGF0ZU9iamVjdChjb29rZWQsIHJhdykge1xuICBpZiAoT2JqZWN0LmRlZmluZVByb3BlcnR5KSB7IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShjb29rZWQsIFwicmF3XCIsIHsgdmFsdWU6IHJhdyB9KTsgfSBlbHNlIHsgY29va2VkLnJhdyA9IHJhdzsgfVxuICByZXR1cm4gY29va2VkO1xufTtcblxudmFyIF9fc2V0TW9kdWxlRGVmYXVsdCA9IE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgdikge1xuICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgXCJkZWZhdWx0XCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgdmFsdWU6IHYgfSk7XG59KSA6IGZ1bmN0aW9uKG8sIHYpIHtcbiAgb1tcImRlZmF1bHRcIl0gPSB2O1xufTtcblxudmFyIG93bktleXMgPSBmdW5jdGlvbihvKSB7XG4gIG93bktleXMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcyB8fCBmdW5jdGlvbiAobykge1xuICAgIHZhciBhciA9IFtdO1xuICAgIGZvciAodmFyIGsgaW4gbykgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChvLCBrKSkgYXJbYXIubGVuZ3RoXSA9IGs7XG4gICAgcmV0dXJuIGFyO1xuICB9O1xuICByZXR1cm4gb3duS2V5cyhvKTtcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2ltcG9ydFN0YXIobW9kKSB7XG4gIGlmIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpIHJldHVybiBtb2Q7XG4gIHZhciByZXN1bHQgPSB7fTtcbiAgaWYgKG1vZCAhPSBudWxsKSBmb3IgKHZhciBrID0gb3duS2V5cyhtb2QpLCBpID0gMDsgaSA8IGsubGVuZ3RoOyBpKyspIGlmIChrW2ldICE9PSBcImRlZmF1bHRcIikgX19jcmVhdGVCaW5kaW5nKHJlc3VsdCwgbW9kLCBrW2ldKTtcbiAgX19zZXRNb2R1bGVEZWZhdWx0KHJlc3VsdCwgbW9kKTtcbiAgcmV0dXJuIHJlc3VsdDtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9faW1wb3J0RGVmYXVsdChtb2QpIHtcbiAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBkZWZhdWx0OiBtb2QgfTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fY2xhc3NQcml2YXRlRmllbGRHZXQocmVjZWl2ZXIsIHN0YXRlLCBraW5kLCBmKSB7XG4gIGlmIChraW5kID09PSBcImFcIiAmJiAhZikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlByaXZhdGUgYWNjZXNzb3Igd2FzIGRlZmluZWQgd2l0aG91dCBhIGdldHRlclwiKTtcbiAgaWYgKHR5cGVvZiBzdGF0ZSA9PT0gXCJmdW5jdGlvblwiID8gcmVjZWl2ZXIgIT09IHN0YXRlIHx8ICFmIDogIXN0YXRlLmhhcyhyZWNlaXZlcikpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgcmVhZCBwcml2YXRlIG1lbWJlciBmcm9tIGFuIG9iamVjdCB3aG9zZSBjbGFzcyBkaWQgbm90IGRlY2xhcmUgaXRcIik7XG4gIHJldHVybiBraW5kID09PSBcIm1cIiA/IGYgOiBraW5kID09PSBcImFcIiA/IGYuY2FsbChyZWNlaXZlcikgOiBmID8gZi52YWx1ZSA6IHN0YXRlLmdldChyZWNlaXZlcik7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2NsYXNzUHJpdmF0ZUZpZWxkU2V0KHJlY2VpdmVyLCBzdGF0ZSwgdmFsdWUsIGtpbmQsIGYpIHtcbiAgaWYgKGtpbmQgPT09IFwibVwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiUHJpdmF0ZSBtZXRob2QgaXMgbm90IHdyaXRhYmxlXCIpO1xuICBpZiAoa2luZCA9PT0gXCJhXCIgJiYgIWYpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJQcml2YXRlIGFjY2Vzc29yIHdhcyBkZWZpbmVkIHdpdGhvdXQgYSBzZXR0ZXJcIik7XG4gIGlmICh0eXBlb2Ygc3RhdGUgPT09IFwiZnVuY3Rpb25cIiA/IHJlY2VpdmVyICE9PSBzdGF0ZSB8fCAhZiA6ICFzdGF0ZS5oYXMocmVjZWl2ZXIpKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiQ2Fubm90IHdyaXRlIHByaXZhdGUgbWVtYmVyIHRvIGFuIG9iamVjdCB3aG9zZSBjbGFzcyBkaWQgbm90IGRlY2xhcmUgaXRcIik7XG4gIHJldHVybiAoa2luZCA9PT0gXCJhXCIgPyBmLmNhbGwocmVjZWl2ZXIsIHZhbHVlKSA6IGYgPyBmLnZhbHVlID0gdmFsdWUgOiBzdGF0ZS5zZXQocmVjZWl2ZXIsIHZhbHVlKSksIHZhbHVlO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19jbGFzc1ByaXZhdGVGaWVsZEluKHN0YXRlLCByZWNlaXZlcikge1xuICBpZiAocmVjZWl2ZXIgPT09IG51bGwgfHwgKHR5cGVvZiByZWNlaXZlciAhPT0gXCJvYmplY3RcIiAmJiB0eXBlb2YgcmVjZWl2ZXIgIT09IFwiZnVuY3Rpb25cIikpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgdXNlICdpbicgb3BlcmF0b3Igb24gbm9uLW9iamVjdFwiKTtcbiAgcmV0dXJuIHR5cGVvZiBzdGF0ZSA9PT0gXCJmdW5jdGlvblwiID8gcmVjZWl2ZXIgPT09IHN0YXRlIDogc3RhdGUuaGFzKHJlY2VpdmVyKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fYWRkRGlzcG9zYWJsZVJlc291cmNlKGVudiwgdmFsdWUsIGFzeW5jKSB7XG4gIGlmICh2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gdm9pZCAwKSB7XG4gICAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gXCJvYmplY3RcIiAmJiB0eXBlb2YgdmFsdWUgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIk9iamVjdCBleHBlY3RlZC5cIik7XG4gICAgdmFyIGRpc3Bvc2UsIGlubmVyO1xuICAgIGlmIChhc3luYykge1xuICAgICAgaWYgKCFTeW1ib2wuYXN5bmNEaXNwb3NlKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiU3ltYm9sLmFzeW5jRGlzcG9zZSBpcyBub3QgZGVmaW5lZC5cIik7XG4gICAgICBkaXNwb3NlID0gdmFsdWVbU3ltYm9sLmFzeW5jRGlzcG9zZV07XG4gICAgfVxuICAgIGlmIChkaXNwb3NlID09PSB2b2lkIDApIHtcbiAgICAgIGlmICghU3ltYm9sLmRpc3Bvc2UpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJTeW1ib2wuZGlzcG9zZSBpcyBub3QgZGVmaW5lZC5cIik7XG4gICAgICBkaXNwb3NlID0gdmFsdWVbU3ltYm9sLmRpc3Bvc2VdO1xuICAgICAgaWYgKGFzeW5jKSBpbm5lciA9IGRpc3Bvc2U7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgZGlzcG9zZSAhPT0gXCJmdW5jdGlvblwiKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiT2JqZWN0IG5vdCBkaXNwb3NhYmxlLlwiKTtcbiAgICBpZiAoaW5uZXIpIGRpc3Bvc2UgPSBmdW5jdGlvbigpIHsgdHJ5IHsgaW5uZXIuY2FsbCh0aGlzKTsgfSBjYXRjaCAoZSkgeyByZXR1cm4gUHJvbWlzZS5yZWplY3QoZSk7IH0gfTtcbiAgICBlbnYuc3RhY2sucHVzaCh7IHZhbHVlOiB2YWx1ZSwgZGlzcG9zZTogZGlzcG9zZSwgYXN5bmM6IGFzeW5jIH0pO1xuICB9XG4gIGVsc2UgaWYgKGFzeW5jKSB7XG4gICAgZW52LnN0YWNrLnB1c2goeyBhc3luYzogdHJ1ZSB9KTtcbiAgfVxuICByZXR1cm4gdmFsdWU7XG59XG5cbnZhciBfU3VwcHJlc3NlZEVycm9yID0gdHlwZW9mIFN1cHByZXNzZWRFcnJvciA9PT0gXCJmdW5jdGlvblwiID8gU3VwcHJlc3NlZEVycm9yIDogZnVuY3Rpb24gKGVycm9yLCBzdXBwcmVzc2VkLCBtZXNzYWdlKSB7XG4gIHZhciBlID0gbmV3IEVycm9yKG1lc3NhZ2UpO1xuICByZXR1cm4gZS5uYW1lID0gXCJTdXBwcmVzc2VkRXJyb3JcIiwgZS5lcnJvciA9IGVycm9yLCBlLnN1cHByZXNzZWQgPSBzdXBwcmVzc2VkLCBlO1xufTtcblxuZXhwb3J0IGZ1bmN0aW9uIF9fZGlzcG9zZVJlc291cmNlcyhlbnYpIHtcbiAgZnVuY3Rpb24gZmFpbChlKSB7XG4gICAgZW52LmVycm9yID0gZW52Lmhhc0Vycm9yID8gbmV3IF9TdXBwcmVzc2VkRXJyb3IoZSwgZW52LmVycm9yLCBcIkFuIGVycm9yIHdhcyBzdXBwcmVzc2VkIGR1cmluZyBkaXNwb3NhbC5cIikgOiBlO1xuICAgIGVudi5oYXNFcnJvciA9IHRydWU7XG4gIH1cbiAgdmFyIHIsIHMgPSAwO1xuICBmdW5jdGlvbiBuZXh0KCkge1xuICAgIHdoaWxlIChyID0gZW52LnN0YWNrLnBvcCgpKSB7XG4gICAgICB0cnkge1xuICAgICAgICBpZiAoIXIuYXN5bmMgJiYgcyA9PT0gMSkgcmV0dXJuIHMgPSAwLCBlbnYuc3RhY2sucHVzaChyKSwgUHJvbWlzZS5yZXNvbHZlKCkudGhlbihuZXh0KTtcbiAgICAgICAgaWYgKHIuZGlzcG9zZSkge1xuICAgICAgICAgIHZhciByZXN1bHQgPSByLmRpc3Bvc2UuY2FsbChyLnZhbHVlKTtcbiAgICAgICAgICBpZiAoci5hc3luYykgcmV0dXJuIHMgfD0gMiwgUHJvbWlzZS5yZXNvbHZlKHJlc3VsdCkudGhlbihuZXh0LCBmdW5jdGlvbihlKSB7IGZhaWwoZSk7IHJldHVybiBuZXh0KCk7IH0pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgcyB8PSAxO1xuICAgICAgfVxuICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgZmFpbChlKTtcbiAgICAgIH1cbiAgICB9XG4gICAgaWYgKHMgPT09IDEpIHJldHVybiBlbnYuaGFzRXJyb3IgPyBQcm9taXNlLnJlamVjdChlbnYuZXJyb3IpIDogUHJvbWlzZS5yZXNvbHZlKCk7XG4gICAgaWYgKGVudi5oYXNFcnJvcikgdGhyb3cgZW52LmVycm9yO1xuICB9XG4gIHJldHVybiBuZXh0KCk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX3Jld3JpdGVSZWxhdGl2ZUltcG9ydEV4dGVuc2lvbihwYXRoLCBwcmVzZXJ2ZUpzeCkge1xuICBpZiAodHlwZW9mIHBhdGggPT09IFwic3RyaW5nXCIgJiYgL15cXC5cXC4/XFwvLy50ZXN0KHBhdGgpKSB7XG4gICAgICByZXR1cm4gcGF0aC5yZXBsYWNlKC9cXC4odHN4KSR8KCg/OlxcLmQpPykoKD86XFwuW14uL10rPyk/KVxcLihbY21dPyl0cyQvaSwgZnVuY3Rpb24gKG0sIHRzeCwgZCwgZXh0LCBjbSkge1xuICAgICAgICAgIHJldHVybiB0c3ggPyBwcmVzZXJ2ZUpzeCA/IFwiLmpzeFwiIDogXCIuanNcIiA6IGQgJiYgKCFleHQgfHwgIWNtKSA/IG0gOiAoZCArIGV4dCArIFwiLlwiICsgY20udG9Mb3dlckNhc2UoKSArIFwianNcIik7XG4gICAgICB9KTtcbiAgfVxuICByZXR1cm4gcGF0aDtcbn1cblxuZXhwb3J0IGRlZmF1bHQge1xuICBfX2V4dGVuZHMsXG4gIF9fYXNzaWduLFxuICBfX3Jlc3QsXG4gIF9fZGVjb3JhdGUsXG4gIF9fcGFyYW0sXG4gIF9fZXNEZWNvcmF0ZSxcbiAgX19ydW5Jbml0aWFsaXplcnMsXG4gIF9fcHJvcEtleSxcbiAgX19zZXRGdW5jdGlvbk5hbWUsXG4gIF9fbWV0YWRhdGEsXG4gIF9fYXdhaXRlcixcbiAgX19nZW5lcmF0b3IsXG4gIF9fY3JlYXRlQmluZGluZyxcbiAgX19leHBvcnRTdGFyLFxuICBfX3ZhbHVlcyxcbiAgX19yZWFkLFxuICBfX3NwcmVhZCxcbiAgX19zcHJlYWRBcnJheXMsXG4gIF9fc3ByZWFkQXJyYXksXG4gIF9fYXdhaXQsXG4gIF9fYXN5bmNHZW5lcmF0b3IsXG4gIF9fYXN5bmNEZWxlZ2F0b3IsXG4gIF9fYXN5bmNWYWx1ZXMsXG4gIF9fbWFrZVRlbXBsYXRlT2JqZWN0LFxuICBfX2ltcG9ydFN0YXIsXG4gIF9faW1wb3J0RGVmYXVsdCxcbiAgX19jbGFzc1ByaXZhdGVGaWVsZEdldCxcbiAgX19jbGFzc1ByaXZhdGVGaWVsZFNldCxcbiAgX19jbGFzc1ByaXZhdGVGaWVsZEluLFxuICBfX2FkZERpc3Bvc2FibGVSZXNvdXJjZSxcbiAgX19kaXNwb3NlUmVzb3VyY2VzLFxuICBfX3Jld3JpdGVSZWxhdGl2ZUltcG9ydEV4dGVuc2lvbixcbn07XG4iXSwibmFtZXMiOlsiZXh0ZW5kU3RhdGljcyIsImQiLCJiIiwiT2JqZWN0Iiwic2V0UHJvdG90eXBlT2YiLCJfX3Byb3RvX18iLCJBcnJheSIsInAiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJfX2V4dGVuZHMiLCJUeXBlRXJyb3IiLCJTdHJpbmciLCJfXyIsImNvbnN0cnVjdG9yIiwiY3JlYXRlIiwiX19hc3NpZ24iLCJhc3NpZ24iLCJ0IiwicyIsImkiLCJuIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiYXBwbHkiLCJfX3Jlc3QiLCJlIiwiaW5kZXhPZiIsImdldE93blByb3BlcnR5U3ltYm9scyIsInByb3BlcnR5SXNFbnVtZXJhYmxlIiwiX19kZWNvcmF0ZSIsImRlY29yYXRvcnMiLCJ0YXJnZXQiLCJrZXkiLCJkZXNjIiwiYyIsInIiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJSZWZsZWN0IiwiZGVjb3JhdGUiLCJkZWZpbmVQcm9wZXJ0eSIsIl9fcGFyYW0iLCJwYXJhbUluZGV4IiwiZGVjb3JhdG9yIiwiX19lc0RlY29yYXRlIiwiY3RvciIsImRlc2NyaXB0b3JJbiIsImNvbnRleHRJbiIsImluaXRpYWxpemVycyIsImV4dHJhSW5pdGlhbGl6ZXJzIiwiYWNjZXB0IiwiZiIsImtpbmQiLCJkZXNjcmlwdG9yIiwibmFtZSIsIl8iLCJkb25lIiwiY29udGV4dCIsImFjY2VzcyIsImFkZEluaXRpYWxpemVyIiwicHVzaCIsInJlc3VsdCIsImdldCIsInNldCIsImluaXQiLCJ1bnNoaWZ0IiwiX19ydW5Jbml0aWFsaXplcnMiLCJ0aGlzQXJnIiwidmFsdWUiLCJ1c2VWYWx1ZSIsIl9fcHJvcEtleSIsIngiLCJjb25jYXQiLCJfX3NldEZ1bmN0aW9uTmFtZSIsInByZWZpeCIsImRlc2NyaXB0aW9uIiwiY29uZmlndXJhYmxlIiwiX19tZXRhZGF0YSIsIm1ldGFkYXRhS2V5IiwibWV0YWRhdGFWYWx1ZSIsIm1ldGFkYXRhIiwiX19hd2FpdGVyIiwiX2FyZ3VtZW50cyIsIlAiLCJnZW5lcmF0b3IiLCJhZG9wdCIsInJlc29sdmUiLCJQcm9taXNlIiwicmVqZWN0IiwiZnVsZmlsbGVkIiwic3RlcCIsIm5leHQiLCJyZWplY3RlZCIsInRoZW4iLCJfX2dlbmVyYXRvciIsImJvZHkiLCJsYWJlbCIsInNlbnQiLCJ0cnlzIiwib3BzIiwieSIsImciLCJJdGVyYXRvciIsInZlcmIiLCJTeW1ib2wiLCJpdGVyYXRvciIsInYiLCJvcCIsInBvcCIsIl9fY3JlYXRlQmluZGluZyIsIm8iLCJtIiwiayIsImsyIiwidW5kZWZpbmVkIiwiX19lc01vZHVsZSIsIndyaXRhYmxlIiwiZW51bWVyYWJsZSIsIl9fZXhwb3J0U3RhciIsIl9fdmFsdWVzIiwiX19yZWFkIiwiYXIiLCJlcnJvciIsIl9fc3ByZWFkIiwiX19zcHJlYWRBcnJheXMiLCJpbCIsImEiLCJqIiwiamwiLCJfX3NwcmVhZEFycmF5IiwidG8iLCJmcm9tIiwicGFjayIsImwiLCJzbGljZSIsIl9fYXdhaXQiLCJfX2FzeW5jR2VuZXJhdG9yIiwiYXN5bmNJdGVyYXRvciIsInEiLCJBc3luY0l0ZXJhdG9yIiwiYXdhaXRSZXR1cm4iLCJyZXN1bWUiLCJzZXR0bGUiLCJmdWxmaWxsIiwic2hpZnQiLCJfX2FzeW5jRGVsZWdhdG9yIiwiX19hc3luY1ZhbHVlcyIsIl9fbWFrZVRlbXBsYXRlT2JqZWN0IiwiY29va2VkIiwicmF3IiwiX19zZXRNb2R1bGVEZWZhdWx0Iiwib3duS2V5cyIsImdldE93blByb3BlcnR5TmFtZXMiLCJfX2ltcG9ydFN0YXIiLCJtb2QiLCJfX2ltcG9ydERlZmF1bHQiLCJkZWZhdWx0IiwiX19jbGFzc1ByaXZhdGVGaWVsZEdldCIsInJlY2VpdmVyIiwic3RhdGUiLCJoYXMiLCJfX2NsYXNzUHJpdmF0ZUZpZWxkU2V0IiwiX19jbGFzc1ByaXZhdGVGaWVsZEluIiwiX19hZGREaXNwb3NhYmxlUmVzb3VyY2UiLCJlbnYiLCJhc3luYyIsImRpc3Bvc2UiLCJpbm5lciIsImFzeW5jRGlzcG9zZSIsInN0YWNrIiwiX1N1cHByZXNzZWRFcnJvciIsIlN1cHByZXNzZWRFcnJvciIsInN1cHByZXNzZWQiLCJtZXNzYWdlIiwiRXJyb3IiLCJfX2Rpc3Bvc2VSZXNvdXJjZXMiLCJmYWlsIiwiaGFzRXJyb3IiLCJfX3Jld3JpdGVSZWxhdGl2ZUltcG9ydEV4dGVuc2lvbiIsInBhdGgiLCJwcmVzZXJ2ZUpzeCIsInRlc3QiLCJyZXBsYWNlIiwidHN4IiwiZXh0IiwiY20iLCJ0b0xvd2VyQ2FzZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/webcrypto-core/node_modules/tslib/tslib.es6.mjs\n");

/***/ })

};
;