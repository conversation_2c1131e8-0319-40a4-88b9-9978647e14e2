"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-map";
exports.ids = ["vendor-chunks/side-channel-map"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-map/index.js":
/*!************************************************!*\
  !*** ./node_modules/side-channel-map/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar GetIntrinsic = __webpack_require__(/*! get-intrinsic */ \"(rsc)/./node_modules/get-intrinsic/index.js\");\nvar callBound = __webpack_require__(/*! call-bound */ \"(rsc)/./node_modules/call-bound/index.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar $Map = GetIntrinsic(\"%Map%\", true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => V} */ var $mapGet = callBound(\"Map.prototype.get\", true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K, value: V) => void} */ var $mapSet = callBound(\"Map.prototype.set\", true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */ var $mapHas = callBound(\"Map.prototype.has\", true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */ var $mapDelete = callBound(\"Map.prototype.delete\", true);\n/** @type {<K, V>(thisArg: Map<K, V>) => number} */ var $mapSize = callBound(\"Map.prototype.size\", true);\n/** @type {import('.')} */ module.exports = !!$Map && /** @type {Exclude<import('.'), false>} */ function getSideChannelMap() {\n    /** @typedef {ReturnType<typeof getSideChannelMap>} Channel */ /** @typedef {Parameters<Channel['get']>[0]} K */ /** @typedef {Parameters<Channel['set']>[1]} V */ /** @type {Map<K, V> | undefined} */ var $m;\n    /** @type {Channel} */ var channel = {\n        assert: function(key) {\n            if (!channel.has(key)) {\n                throw new $TypeError(\"Side channel does not contain \" + inspect(key));\n            }\n        },\n        \"delete\": function(key) {\n            if ($m) {\n                var result = $mapDelete($m, key);\n                if ($mapSize($m) === 0) {\n                    $m = void undefined;\n                }\n                return result;\n            }\n            return false;\n        },\n        get: function(key) {\n            if ($m) {\n                return $mapGet($m, key);\n            }\n        },\n        has: function(key) {\n            if ($m) {\n                return $mapHas($m, key);\n            }\n            return false;\n        },\n        set: function(key, value) {\n            if (!$m) {\n                // @ts-expect-error TS can't handle narrowing a variable inside a closure\n                $m = new $Map();\n            }\n            $mapSet($m, key, value);\n        }\n    };\n    // @ts-expect-error TODO: figure out why TS is erroring here\n    return channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-map/index.js\n");

/***/ })

};
;