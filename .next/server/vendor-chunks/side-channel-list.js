"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-list";
exports.ids = ["vendor-chunks/side-channel-list"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-list/index.js":
/*!*************************************************!*\
  !*** ./node_modules/side-channel-list/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\n/*\n* This function traverses the list returning the node corresponding to the given key.\n*\n* That node is also moved to the head of the list, so that if it's accessed again we don't need to traverse the whole list.\n* By doing so, all the recently used nodes can be accessed relatively quickly.\n*/ /** @type {import('./list.d.ts').listGetNode} */ // eslint-disable-next-line consistent-return\nvar listGetNode = function(list, key, isDelete) {\n    /** @type {typeof list | NonNullable<(typeof list)['next']>} */ var prev = list;\n    /** @type {(typeof list)['next']} */ var curr;\n    // eslint-disable-next-line eqeqeq\n    for(; (curr = prev.next) != null; prev = curr){\n        if (curr.key === key) {\n            prev.next = curr.next;\n            if (!isDelete) {\n                // eslint-disable-next-line no-extra-parens\n                curr.next = /** @type {NonNullable<typeof list.next>} */ list.next;\n                list.next = curr; // eslint-disable-line no-param-reassign\n            }\n            return curr;\n        }\n    }\n};\n/** @type {import('./list.d.ts').listGet} */ var listGet = function(objects, key) {\n    if (!objects) {\n        return void undefined;\n    }\n    var node = listGetNode(objects, key);\n    return node && node.value;\n};\n/** @type {import('./list.d.ts').listSet} */ var listSet = function(objects, key, value) {\n    var node = listGetNode(objects, key);\n    if (node) {\n        node.value = value;\n    } else {\n        // Prepend the new node to the beginning of the list\n        objects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */ {\n            key: key,\n            next: objects.next,\n            value: value\n        };\n    }\n};\n/** @type {import('./list.d.ts').listHas} */ var listHas = function(objects, key) {\n    if (!objects) {\n        return false;\n    }\n    return !!listGetNode(objects, key);\n};\n/** @type {import('./list.d.ts').listDelete} */ // eslint-disable-next-line consistent-return\nvar listDelete = function(objects, key) {\n    if (objects) {\n        return listGetNode(objects, key, true);\n    }\n};\n/** @type {import('.')} */ module.exports = function getSideChannelList() {\n    /** @typedef {ReturnType<typeof getSideChannelList>} Channel */ /** @typedef {Parameters<Channel['get']>[0]} K */ /** @typedef {Parameters<Channel['set']>[1]} V */ /** @type {import('./list.d.ts').RootNode<V, K> | undefined} */ var $o;\n    /** @type {Channel} */ var channel = {\n        assert: function(key) {\n            if (!channel.has(key)) {\n                throw new $TypeError(\"Side channel does not contain \" + inspect(key));\n            }\n        },\n        \"delete\": function(key) {\n            var root = $o && $o.next;\n            var deletedNode = listDelete($o, key);\n            if (deletedNode && root && root === deletedNode) {\n                $o = void undefined;\n            }\n            return !!deletedNode;\n        },\n        get: function(key) {\n            return listGet($o, key);\n        },\n        has: function(key) {\n            return listHas($o, key);\n        },\n        set: function(key, value) {\n            if (!$o) {\n                // Initialize the linked list as an empty node, so that we don't have to special-case handling of the first node: we can always refer to it as (previous node).next, instead of something like (list).head\n                $o = {\n                    next: void undefined\n                };\n            }\n            // eslint-disable-next-line no-extra-parens\n            listSet(/** @type {NonNullable<typeof $o>} */ $o, key, value);\n        }\n    };\n    // @ts-expect-error TODO: figure out why this is erroring\n    return channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvc2lkZS1jaGFubmVsLWxpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxJQUFJQSxVQUFVQyxtQkFBT0EsQ0FBQztBQUV0QixJQUFJQyxhQUFhRCxtQkFBT0EsQ0FBQztBQUV6Qjs7Ozs7QUFLQSxHQUNBLDhDQUE4QyxHQUM5Qyw2Q0FBNkM7QUFDN0MsSUFBSUUsY0FBYyxTQUFVQyxJQUFJLEVBQUVDLEdBQUcsRUFBRUMsUUFBUTtJQUM5Qyw2REFBNkQsR0FDN0QsSUFBSUMsT0FBT0g7SUFDWCxrQ0FBa0MsR0FDbEMsSUFBSUk7SUFDSixrQ0FBa0M7SUFDbEMsTUFBTyxDQUFDQSxPQUFPRCxLQUFLRSxJQUFJLEtBQUssTUFBTUYsT0FBT0MsS0FBTTtRQUMvQyxJQUFJQSxLQUFLSCxHQUFHLEtBQUtBLEtBQUs7WUFDckJFLEtBQUtFLElBQUksR0FBR0QsS0FBS0MsSUFBSTtZQUNyQixJQUFJLENBQUNILFVBQVU7Z0JBQ2QsMkNBQTJDO2dCQUMzQ0UsS0FBS0MsSUFBSSxHQUFHLDBDQUEwQyxHQUFJTCxLQUFLSyxJQUFJO2dCQUNuRUwsS0FBS0ssSUFBSSxHQUFHRCxNQUFNLHdDQUF3QztZQUMzRDtZQUNBLE9BQU9BO1FBQ1I7SUFDRDtBQUNEO0FBRUEsMENBQTBDLEdBQzFDLElBQUlFLFVBQVUsU0FBVUMsT0FBTyxFQUFFTixHQUFHO0lBQ25DLElBQUksQ0FBQ00sU0FBUztRQUNiLE9BQU8sS0FBS0M7SUFDYjtJQUNBLElBQUlDLE9BQU9WLFlBQVlRLFNBQVNOO0lBQ2hDLE9BQU9RLFFBQVFBLEtBQUtDLEtBQUs7QUFDMUI7QUFDQSwwQ0FBMEMsR0FDMUMsSUFBSUMsVUFBVSxTQUFVSixPQUFPLEVBQUVOLEdBQUcsRUFBRVMsS0FBSztJQUMxQyxJQUFJRCxPQUFPVixZQUFZUSxTQUFTTjtJQUNoQyxJQUFJUSxNQUFNO1FBQ1RBLEtBQUtDLEtBQUssR0FBR0E7SUFDZCxPQUFPO1FBQ04sb0RBQW9EO1FBQ3BESCxRQUFRRixJQUFJLEdBQUcscUVBQXFFLEdBQUk7WUFDdkZKLEtBQUtBO1lBQ0xJLE1BQU1FLFFBQVFGLElBQUk7WUFDbEJLLE9BQU9BO1FBQ1I7SUFDRDtBQUNEO0FBQ0EsMENBQTBDLEdBQzFDLElBQUlFLFVBQVUsU0FBVUwsT0FBTyxFQUFFTixHQUFHO0lBQ25DLElBQUksQ0FBQ00sU0FBUztRQUNiLE9BQU87SUFDUjtJQUNBLE9BQU8sQ0FBQyxDQUFDUixZQUFZUSxTQUFTTjtBQUMvQjtBQUNBLDZDQUE2QyxHQUM3Qyw2Q0FBNkM7QUFDN0MsSUFBSVksYUFBYSxTQUFVTixPQUFPLEVBQUVOLEdBQUc7SUFDdEMsSUFBSU0sU0FBUztRQUNaLE9BQU9SLFlBQVlRLFNBQVNOLEtBQUs7SUFDbEM7QUFDRDtBQUVBLHdCQUF3QixHQUN4QmEsT0FBT0MsT0FBTyxHQUFHLFNBQVNDO0lBQ3pCLDZEQUE2RCxHQUM3RCwrQ0FBK0MsR0FDL0MsK0NBQStDLEdBRS9DLDZEQUE2RCxHQUFHLElBQUlDO0lBRXBFLG9CQUFvQixHQUNwQixJQUFJQyxVQUFVO1FBQ2JDLFFBQVEsU0FBVWxCLEdBQUc7WUFDcEIsSUFBSSxDQUFDaUIsUUFBUUUsR0FBRyxDQUFDbkIsTUFBTTtnQkFDdEIsTUFBTSxJQUFJSCxXQUFXLG1DQUFtQ0YsUUFBUUs7WUFDakU7UUFDRDtRQUNBLFVBQVUsU0FBVUEsR0FBRztZQUN0QixJQUFJb0IsT0FBT0osTUFBTUEsR0FBR1osSUFBSTtZQUN4QixJQUFJaUIsY0FBY1QsV0FBV0ksSUFBSWhCO1lBQ2pDLElBQUlxQixlQUFlRCxRQUFRQSxTQUFTQyxhQUFhO2dCQUNoREwsS0FBSyxLQUFLVDtZQUNYO1lBQ0EsT0FBTyxDQUFDLENBQUNjO1FBQ1Y7UUFDQUMsS0FBSyxTQUFVdEIsR0FBRztZQUNqQixPQUFPSyxRQUFRVyxJQUFJaEI7UUFDcEI7UUFDQW1CLEtBQUssU0FBVW5CLEdBQUc7WUFDakIsT0FBT1csUUFBUUssSUFBSWhCO1FBQ3BCO1FBQ0F1QixLQUFLLFNBQVV2QixHQUFHLEVBQUVTLEtBQUs7WUFDeEIsSUFBSSxDQUFDTyxJQUFJO2dCQUNSLDBNQUEwTTtnQkFDMU1BLEtBQUs7b0JBQ0paLE1BQU0sS0FBS0c7Z0JBQ1o7WUFDRDtZQUNBLDJDQUEyQztZQUMzQ0csUUFBUSxtQ0FBbUMsR0FBSU0sSUFBS2hCLEtBQUtTO1FBQzFEO0lBQ0Q7SUFDQSx5REFBeUQ7SUFDekQsT0FBT1E7QUFDUiIsInNvdXJjZXMiOlsid2VicGFjazovL2N1c3RvbS1ncm91cC1jcmVhdG9yLy4vbm9kZV9tb2R1bGVzL3NpZGUtY2hhbm5lbC1saXN0L2luZGV4LmpzP2MzMDQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgaW5zcGVjdCA9IHJlcXVpcmUoJ29iamVjdC1pbnNwZWN0Jyk7XG5cbnZhciAkVHlwZUVycm9yID0gcmVxdWlyZSgnZXMtZXJyb3JzL3R5cGUnKTtcblxuLypcbiogVGhpcyBmdW5jdGlvbiB0cmF2ZXJzZXMgdGhlIGxpc3QgcmV0dXJuaW5nIHRoZSBub2RlIGNvcnJlc3BvbmRpbmcgdG8gdGhlIGdpdmVuIGtleS5cbipcbiogVGhhdCBub2RlIGlzIGFsc28gbW92ZWQgdG8gdGhlIGhlYWQgb2YgdGhlIGxpc3QsIHNvIHRoYXQgaWYgaXQncyBhY2Nlc3NlZCBhZ2FpbiB3ZSBkb24ndCBuZWVkIHRvIHRyYXZlcnNlIHRoZSB3aG9sZSBsaXN0LlxuKiBCeSBkb2luZyBzbywgYWxsIHRoZSByZWNlbnRseSB1c2VkIG5vZGVzIGNhbiBiZSBhY2Nlc3NlZCByZWxhdGl2ZWx5IHF1aWNrbHkuXG4qL1xuLyoqIEB0eXBlIHtpbXBvcnQoJy4vbGlzdC5kLnRzJykubGlzdEdldE5vZGV9ICovXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgY29uc2lzdGVudC1yZXR1cm5cbnZhciBsaXN0R2V0Tm9kZSA9IGZ1bmN0aW9uIChsaXN0LCBrZXksIGlzRGVsZXRlKSB7XG5cdC8qKiBAdHlwZSB7dHlwZW9mIGxpc3QgfCBOb25OdWxsYWJsZTwodHlwZW9mIGxpc3QpWyduZXh0J10+fSAqL1xuXHR2YXIgcHJldiA9IGxpc3Q7XG5cdC8qKiBAdHlwZSB7KHR5cGVvZiBsaXN0KVsnbmV4dCddfSAqL1xuXHR2YXIgY3Vycjtcblx0Ly8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGVxZXFlcVxuXHRmb3IgKDsgKGN1cnIgPSBwcmV2Lm5leHQpICE9IG51bGw7IHByZXYgPSBjdXJyKSB7XG5cdFx0aWYgKGN1cnIua2V5ID09PSBrZXkpIHtcblx0XHRcdHByZXYubmV4dCA9IGN1cnIubmV4dDtcblx0XHRcdGlmICghaXNEZWxldGUpIHtcblx0XHRcdFx0Ly8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWV4dHJhLXBhcmVuc1xuXHRcdFx0XHRjdXJyLm5leHQgPSAvKiogQHR5cGUge05vbk51bGxhYmxlPHR5cGVvZiBsaXN0Lm5leHQ+fSAqLyAobGlzdC5uZXh0KTtcblx0XHRcdFx0bGlzdC5uZXh0ID0gY3VycjsgLy8gZXNsaW50LWRpc2FibGUtbGluZSBuby1wYXJhbS1yZWFzc2lnblxuXHRcdFx0fVxuXHRcdFx0cmV0dXJuIGN1cnI7XG5cdFx0fVxuXHR9XG59O1xuXG4vKiogQHR5cGUge2ltcG9ydCgnLi9saXN0LmQudHMnKS5saXN0R2V0fSAqL1xudmFyIGxpc3RHZXQgPSBmdW5jdGlvbiAob2JqZWN0cywga2V5KSB7XG5cdGlmICghb2JqZWN0cykge1xuXHRcdHJldHVybiB2b2lkIHVuZGVmaW5lZDtcblx0fVxuXHR2YXIgbm9kZSA9IGxpc3RHZXROb2RlKG9iamVjdHMsIGtleSk7XG5cdHJldHVybiBub2RlICYmIG5vZGUudmFsdWU7XG59O1xuLyoqIEB0eXBlIHtpbXBvcnQoJy4vbGlzdC5kLnRzJykubGlzdFNldH0gKi9cbnZhciBsaXN0U2V0ID0gZnVuY3Rpb24gKG9iamVjdHMsIGtleSwgdmFsdWUpIHtcblx0dmFyIG5vZGUgPSBsaXN0R2V0Tm9kZShvYmplY3RzLCBrZXkpO1xuXHRpZiAobm9kZSkge1xuXHRcdG5vZGUudmFsdWUgPSB2YWx1ZTtcblx0fSBlbHNlIHtcblx0XHQvLyBQcmVwZW5kIHRoZSBuZXcgbm9kZSB0byB0aGUgYmVnaW5uaW5nIG9mIHRoZSBsaXN0XG5cdFx0b2JqZWN0cy5uZXh0ID0gLyoqIEB0eXBlIHtpbXBvcnQoJy4vbGlzdC5kLnRzJykuTGlzdE5vZGU8dHlwZW9mIHZhbHVlLCB0eXBlb2Yga2V5Pn0gKi8gKHsgLy8gZXNsaW50LWRpc2FibGUtbGluZSBuby1wYXJhbS1yZWFzc2lnbiwgbm8tZXh0cmEtcGFyZW5zXG5cdFx0XHRrZXk6IGtleSxcblx0XHRcdG5leHQ6IG9iamVjdHMubmV4dCxcblx0XHRcdHZhbHVlOiB2YWx1ZVxuXHRcdH0pO1xuXHR9XG59O1xuLyoqIEB0eXBlIHtpbXBvcnQoJy4vbGlzdC5kLnRzJykubGlzdEhhc30gKi9cbnZhciBsaXN0SGFzID0gZnVuY3Rpb24gKG9iamVjdHMsIGtleSkge1xuXHRpZiAoIW9iamVjdHMpIHtcblx0XHRyZXR1cm4gZmFsc2U7XG5cdH1cblx0cmV0dXJuICEhbGlzdEdldE5vZGUob2JqZWN0cywga2V5KTtcbn07XG4vKiogQHR5cGUge2ltcG9ydCgnLi9saXN0LmQudHMnKS5saXN0RGVsZXRlfSAqL1xuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIGNvbnNpc3RlbnQtcmV0dXJuXG52YXIgbGlzdERlbGV0ZSA9IGZ1bmN0aW9uIChvYmplY3RzLCBrZXkpIHtcblx0aWYgKG9iamVjdHMpIHtcblx0XHRyZXR1cm4gbGlzdEdldE5vZGUob2JqZWN0cywga2V5LCB0cnVlKTtcblx0fVxufTtcblxuLyoqIEB0eXBlIHtpbXBvcnQoJy4nKX0gKi9cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gZ2V0U2lkZUNoYW5uZWxMaXN0KCkge1xuXHQvKiogQHR5cGVkZWYge1JldHVyblR5cGU8dHlwZW9mIGdldFNpZGVDaGFubmVsTGlzdD59IENoYW5uZWwgKi9cblx0LyoqIEB0eXBlZGVmIHtQYXJhbWV0ZXJzPENoYW5uZWxbJ2dldCddPlswXX0gSyAqL1xuXHQvKiogQHR5cGVkZWYge1BhcmFtZXRlcnM8Q2hhbm5lbFsnc2V0J10+WzFdfSBWICovXG5cblx0LyoqIEB0eXBlIHtpbXBvcnQoJy4vbGlzdC5kLnRzJykuUm9vdE5vZGU8ViwgSz4gfCB1bmRlZmluZWR9ICovIHZhciAkbztcblxuXHQvKiogQHR5cGUge0NoYW5uZWx9ICovXG5cdHZhciBjaGFubmVsID0ge1xuXHRcdGFzc2VydDogZnVuY3Rpb24gKGtleSkge1xuXHRcdFx0aWYgKCFjaGFubmVsLmhhcyhrZXkpKSB7XG5cdFx0XHRcdHRocm93IG5ldyAkVHlwZUVycm9yKCdTaWRlIGNoYW5uZWwgZG9lcyBub3QgY29udGFpbiAnICsgaW5zcGVjdChrZXkpKTtcblx0XHRcdH1cblx0XHR9LFxuXHRcdCdkZWxldGUnOiBmdW5jdGlvbiAoa2V5KSB7XG5cdFx0XHR2YXIgcm9vdCA9ICRvICYmICRvLm5leHQ7XG5cdFx0XHR2YXIgZGVsZXRlZE5vZGUgPSBsaXN0RGVsZXRlKCRvLCBrZXkpO1xuXHRcdFx0aWYgKGRlbGV0ZWROb2RlICYmIHJvb3QgJiYgcm9vdCA9PT0gZGVsZXRlZE5vZGUpIHtcblx0XHRcdFx0JG8gPSB2b2lkIHVuZGVmaW5lZDtcblx0XHRcdH1cblx0XHRcdHJldHVybiAhIWRlbGV0ZWROb2RlO1xuXHRcdH0sXG5cdFx0Z2V0OiBmdW5jdGlvbiAoa2V5KSB7XG5cdFx0XHRyZXR1cm4gbGlzdEdldCgkbywga2V5KTtcblx0XHR9LFxuXHRcdGhhczogZnVuY3Rpb24gKGtleSkge1xuXHRcdFx0cmV0dXJuIGxpc3RIYXMoJG8sIGtleSk7XG5cdFx0fSxcblx0XHRzZXQ6IGZ1bmN0aW9uIChrZXksIHZhbHVlKSB7XG5cdFx0XHRpZiAoISRvKSB7XG5cdFx0XHRcdC8vIEluaXRpYWxpemUgdGhlIGxpbmtlZCBsaXN0IGFzIGFuIGVtcHR5IG5vZGUsIHNvIHRoYXQgd2UgZG9uJ3QgaGF2ZSB0byBzcGVjaWFsLWNhc2UgaGFuZGxpbmcgb2YgdGhlIGZpcnN0IG5vZGU6IHdlIGNhbiBhbHdheXMgcmVmZXIgdG8gaXQgYXMgKHByZXZpb3VzIG5vZGUpLm5leHQsIGluc3RlYWQgb2Ygc29tZXRoaW5nIGxpa2UgKGxpc3QpLmhlYWRcblx0XHRcdFx0JG8gPSB7XG5cdFx0XHRcdFx0bmV4dDogdm9pZCB1bmRlZmluZWRcblx0XHRcdFx0fTtcblx0XHRcdH1cblx0XHRcdC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1leHRyYS1wYXJlbnNcblx0XHRcdGxpc3RTZXQoLyoqIEB0eXBlIHtOb25OdWxsYWJsZTx0eXBlb2YgJG8+fSAqLyAoJG8pLCBrZXksIHZhbHVlKTtcblx0XHR9XG5cdH07XG5cdC8vIEB0cy1leHBlY3QtZXJyb3IgVE9ETzogZmlndXJlIG91dCB3aHkgdGhpcyBpcyBlcnJvcmluZ1xuXHRyZXR1cm4gY2hhbm5lbDtcbn07XG4iXSwibmFtZXMiOlsiaW5zcGVjdCIsInJlcXVpcmUiLCIkVHlwZUVycm9yIiwibGlzdEdldE5vZGUiLCJsaXN0Iiwia2V5IiwiaXNEZWxldGUiLCJwcmV2IiwiY3VyciIsIm5leHQiLCJsaXN0R2V0Iiwib2JqZWN0cyIsInVuZGVmaW5lZCIsIm5vZGUiLCJ2YWx1ZSIsImxpc3RTZXQiLCJsaXN0SGFzIiwibGlzdERlbGV0ZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJnZXRTaWRlQ2hhbm5lbExpc3QiLCIkbyIsImNoYW5uZWwiLCJhc3NlcnQiLCJoYXMiLCJyb290IiwiZGVsZXRlZE5vZGUiLCJnZXQiLCJzZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-list/index.js\n");

/***/ })

};
;