"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cookie";
exports.ids = ["vendor-chunks/cookie"];
exports.modules = {

/***/ "(rsc)/./node_modules/cookie/index.js":
/*!**************************************!*\
  !*** ./node_modules/cookie/index.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*!\n * cookie\n * Copyright(c) 2012-2014 Roman Shtylman\n * Copyright(c) 2015 Douglas Christopher Wilson\n * MIT Licensed\n */ \n/**\n * Module exports.\n * @public\n */ exports.parse = parse;\nexports.serialize = serialize;\n/**\n * Module variables.\n * @private\n */ var __toString = Object.prototype.toString;\n/**\n * RegExp to match field-content in RFC 7230 sec 3.2\n *\n * field-content = field-vchar [ 1*( SP / HTAB ) field-vchar ]\n * field-vchar   = VCHAR / obs-text\n * obs-text      = %x80-FF\n */ var fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n *\n * @param {string} str\n * @param {object} [options]\n * @return {object}\n * @public\n */ function parse(str, options) {\n    if (typeof str !== \"string\") {\n        throw new TypeError(\"argument str must be a string\");\n    }\n    var obj = {};\n    var opt = options || {};\n    var dec = opt.decode || decode;\n    var index = 0;\n    while(index < str.length){\n        var eqIdx = str.indexOf(\"=\", index);\n        // no more cookie pairs\n        if (eqIdx === -1) {\n            break;\n        }\n        var endIdx = str.indexOf(\";\", index);\n        if (endIdx === -1) {\n            endIdx = str.length;\n        } else if (endIdx < eqIdx) {\n            // backtrack on prior semicolon\n            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n            continue;\n        }\n        var key = str.slice(index, eqIdx).trim();\n        // only assign once\n        if (undefined === obj[key]) {\n            var val = str.slice(eqIdx + 1, endIdx).trim();\n            // quoted values\n            if (val.charCodeAt(0) === 0x22) {\n                val = val.slice(1, -1);\n            }\n            obj[key] = tryDecode(val, dec);\n        }\n        index = endIdx + 1;\n    }\n    return obj;\n}\n/**\n * Serialize data into a cookie header.\n *\n * Serialize the a name value pair into a cookie string suitable for\n * http headers. An optional options object specified cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n *\n * @param {string} name\n * @param {string} val\n * @param {object} [options]\n * @return {string}\n * @public\n */ function serialize(name, val, options) {\n    var opt = options || {};\n    var enc = opt.encode || encode;\n    if (typeof enc !== \"function\") {\n        throw new TypeError(\"option encode is invalid\");\n    }\n    if (!fieldContentRegExp.test(name)) {\n        throw new TypeError(\"argument name is invalid\");\n    }\n    var value = enc(val);\n    if (value && !fieldContentRegExp.test(value)) {\n        throw new TypeError(\"argument val is invalid\");\n    }\n    var str = name + \"=\" + value;\n    if (null != opt.maxAge) {\n        var maxAge = opt.maxAge - 0;\n        if (isNaN(maxAge) || !isFinite(maxAge)) {\n            throw new TypeError(\"option maxAge is invalid\");\n        }\n        str += \"; Max-Age=\" + Math.floor(maxAge);\n    }\n    if (opt.domain) {\n        if (!fieldContentRegExp.test(opt.domain)) {\n            throw new TypeError(\"option domain is invalid\");\n        }\n        str += \"; Domain=\" + opt.domain;\n    }\n    if (opt.path) {\n        if (!fieldContentRegExp.test(opt.path)) {\n            throw new TypeError(\"option path is invalid\");\n        }\n        str += \"; Path=\" + opt.path;\n    }\n    if (opt.expires) {\n        var expires = opt.expires;\n        if (!isDate(expires) || isNaN(expires.valueOf())) {\n            throw new TypeError(\"option expires is invalid\");\n        }\n        str += \"; Expires=\" + expires.toUTCString();\n    }\n    if (opt.httpOnly) {\n        str += \"; HttpOnly\";\n    }\n    if (opt.secure) {\n        str += \"; Secure\";\n    }\n    if (opt.priority) {\n        var priority = typeof opt.priority === \"string\" ? opt.priority.toLowerCase() : opt.priority;\n        switch(priority){\n            case \"low\":\n                str += \"; Priority=Low\";\n                break;\n            case \"medium\":\n                str += \"; Priority=Medium\";\n                break;\n            case \"high\":\n                str += \"; Priority=High\";\n                break;\n            default:\n                throw new TypeError(\"option priority is invalid\");\n        }\n    }\n    if (opt.sameSite) {\n        var sameSite = typeof opt.sameSite === \"string\" ? opt.sameSite.toLowerCase() : opt.sameSite;\n        switch(sameSite){\n            case true:\n                str += \"; SameSite=Strict\";\n                break;\n            case \"lax\":\n                str += \"; SameSite=Lax\";\n                break;\n            case \"strict\":\n                str += \"; SameSite=Strict\";\n                break;\n            case \"none\":\n                str += \"; SameSite=None\";\n                break;\n            default:\n                throw new TypeError(\"option sameSite is invalid\");\n        }\n    }\n    return str;\n}\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n *\n * @param {string} str\n * @returns {string}\n */ function decode(str) {\n    return str.indexOf(\"%\") !== -1 ? decodeURIComponent(str) : str;\n}\n/**\n * URL-encode value.\n *\n * @param {string} str\n * @returns {string}\n */ function encode(val) {\n    return encodeURIComponent(val);\n}\n/**\n * Determine if value is a Date.\n *\n * @param {*} val\n * @private\n */ function isDate(val) {\n    return __toString.call(val) === \"[object Date]\" || val instanceof Date;\n}\n/**\n * Try decoding a string using a decoding function.\n *\n * @param {string} str\n * @param {function} decode\n * @private\n */ function tryDecode(str, decode) {\n    try {\n        return decode(str);\n    } catch (e) {\n        return str;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cookie/index.js\n");

/***/ })

};
;