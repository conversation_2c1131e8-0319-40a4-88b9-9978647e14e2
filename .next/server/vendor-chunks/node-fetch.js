"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/node-fetch";
exports.ids = ["vendor-chunks/node-fetch"];
exports.modules = {

/***/ "(rsc)/./node_modules/node-fetch/lib/index.mjs":
/*!***********************************************!*\
  !*** ./node_modules/node-fetch/lib/index.mjs ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortError: () => (/* binding */ AbortError),\n/* harmony export */   FetchError: () => (/* binding */ FetchError),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   Request: () => (/* binding */ Request),\n/* harmony export */   Response: () => (/* binding */ Response),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! http */ \"http\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var whatwg_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! whatwg-url */ \"(rsc)/./node_modules/whatwg-url/lib/public-api.js\");\n/* harmony import */ var https__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! https */ \"https\");\n/* harmony import */ var zlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zlib */ \"zlib\");\n\n\n\n\n\n\n// Based on https://github.com/tmpvar/jsdom/blob/aa85b2abf07766ff7bf5c1f6daafb3726f2f2db5/lib/jsdom/living/blob.js\n// fix for \"Readable\" isn't a named export issue\nconst Readable = stream__WEBPACK_IMPORTED_MODULE_0__.Readable;\nconst BUFFER = Symbol(\"buffer\");\nconst TYPE = Symbol(\"type\");\nclass Blob {\n    constructor(){\n        this[TYPE] = \"\";\n        const blobParts = arguments[0];\n        const options = arguments[1];\n        const buffers = [];\n        let size = 0;\n        if (blobParts) {\n            const a = blobParts;\n            const length = Number(a.length);\n            for(let i = 0; i < length; i++){\n                const element = a[i];\n                let buffer;\n                if (element instanceof Buffer) {\n                    buffer = element;\n                } else if (ArrayBuffer.isView(element)) {\n                    buffer = Buffer.from(element.buffer, element.byteOffset, element.byteLength);\n                } else if (element instanceof ArrayBuffer) {\n                    buffer = Buffer.from(element);\n                } else if (element instanceof Blob) {\n                    buffer = element[BUFFER];\n                } else {\n                    buffer = Buffer.from(typeof element === \"string\" ? element : String(element));\n                }\n                size += buffer.length;\n                buffers.push(buffer);\n            }\n        }\n        this[BUFFER] = Buffer.concat(buffers);\n        let type = options && options.type !== undefined && String(options.type).toLowerCase();\n        if (type && !/[^\\u0020-\\u007E]/.test(type)) {\n            this[TYPE] = type;\n        }\n    }\n    get size() {\n        return this[BUFFER].length;\n    }\n    get type() {\n        return this[TYPE];\n    }\n    text() {\n        return Promise.resolve(this[BUFFER].toString());\n    }\n    arrayBuffer() {\n        const buf = this[BUFFER];\n        const ab = buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n        return Promise.resolve(ab);\n    }\n    stream() {\n        const readable = new Readable();\n        readable._read = function() {};\n        readable.push(this[BUFFER]);\n        readable.push(null);\n        return readable;\n    }\n    toString() {\n        return \"[object Blob]\";\n    }\n    slice() {\n        const size = this.size;\n        const start = arguments[0];\n        const end = arguments[1];\n        let relativeStart, relativeEnd;\n        if (start === undefined) {\n            relativeStart = 0;\n        } else if (start < 0) {\n            relativeStart = Math.max(size + start, 0);\n        } else {\n            relativeStart = Math.min(start, size);\n        }\n        if (end === undefined) {\n            relativeEnd = size;\n        } else if (end < 0) {\n            relativeEnd = Math.max(size + end, 0);\n        } else {\n            relativeEnd = Math.min(end, size);\n        }\n        const span = Math.max(relativeEnd - relativeStart, 0);\n        const buffer = this[BUFFER];\n        const slicedBuffer = buffer.slice(relativeStart, relativeStart + span);\n        const blob = new Blob([], {\n            type: arguments[2]\n        });\n        blob[BUFFER] = slicedBuffer;\n        return blob;\n    }\n}\nObject.defineProperties(Blob.prototype, {\n    size: {\n        enumerable: true\n    },\n    type: {\n        enumerable: true\n    },\n    slice: {\n        enumerable: true\n    }\n});\nObject.defineProperty(Blob.prototype, Symbol.toStringTag, {\n    value: \"Blob\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\n/**\n * fetch-error.js\n *\n * FetchError interface for operational errors\n */ /**\n * Create FetchError instance\n *\n * @param   String      message      Error message for human\n * @param   String      type         Error type for machine\n * @param   String      systemError  For Node.js system error\n * @return  FetchError\n */ function FetchError(message, type, systemError) {\n    Error.call(this, message);\n    this.message = message;\n    this.type = type;\n    // when err.type is `system`, err.code contains system error code\n    if (systemError) {\n        this.code = this.errno = systemError.code;\n    }\n    // hide custom error implementation details from end-users\n    Error.captureStackTrace(this, this.constructor);\n}\nFetchError.prototype = Object.create(Error.prototype);\nFetchError.prototype.constructor = FetchError;\nFetchError.prototype.name = \"FetchError\";\nlet convert;\ntry {\n    convert = require(\"encoding\").convert;\n} catch (e) {}\nconst INTERNALS = Symbol(\"Body internals\");\n// fix an issue where \"PassThrough\" isn't a named export for node <10\nconst PassThrough = stream__WEBPACK_IMPORTED_MODULE_0__.PassThrough;\n/**\n * Body mixin\n *\n * Ref: https://fetch.spec.whatwg.org/#body\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */ function Body(body) {\n    var _this = this;\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {}, _ref$size = _ref.size;\n    let size = _ref$size === undefined ? 0 : _ref$size;\n    var _ref$timeout = _ref.timeout;\n    let timeout = _ref$timeout === undefined ? 0 : _ref$timeout;\n    if (body == null) {\n        // body is undefined or null\n        body = null;\n    } else if (isURLSearchParams(body)) {\n        // body is a URLSearchParams\n        body = Buffer.from(body.toString());\n    } else if (isBlob(body)) ;\n    else if (Buffer.isBuffer(body)) ;\n    else if (Object.prototype.toString.call(body) === \"[object ArrayBuffer]\") {\n        // body is ArrayBuffer\n        body = Buffer.from(body);\n    } else if (ArrayBuffer.isView(body)) {\n        // body is ArrayBufferView\n        body = Buffer.from(body.buffer, body.byteOffset, body.byteLength);\n    } else if (body instanceof stream__WEBPACK_IMPORTED_MODULE_0__) ;\n    else {\n        // none of the above\n        // coerce to string then buffer\n        body = Buffer.from(String(body));\n    }\n    this[INTERNALS] = {\n        body,\n        disturbed: false,\n        error: null\n    };\n    this.size = size;\n    this.timeout = timeout;\n    if (body instanceof stream__WEBPACK_IMPORTED_MODULE_0__) {\n        body.on(\"error\", function(err) {\n            const error = err.name === \"AbortError\" ? err : new FetchError(`Invalid response body while trying to fetch ${_this.url}: ${err.message}`, \"system\", err);\n            _this[INTERNALS].error = error;\n        });\n    }\n}\nBody.prototype = {\n    get body () {\n        return this[INTERNALS].body;\n    },\n    get bodyUsed () {\n        return this[INTERNALS].disturbed;\n    },\n    /**\n  * Decode response as ArrayBuffer\n  *\n  * @return  Promise\n  */ arrayBuffer () {\n        return consumeBody.call(this).then(function(buf) {\n            return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n        });\n    },\n    /**\n  * Return raw response as Blob\n  *\n  * @return Promise\n  */ blob () {\n        let ct = this.headers && this.headers.get(\"content-type\") || \"\";\n        return consumeBody.call(this).then(function(buf) {\n            return Object.assign(// Prevent copying\n            new Blob([], {\n                type: ct.toLowerCase()\n            }), {\n                [BUFFER]: buf\n            });\n        });\n    },\n    /**\n  * Decode response as json\n  *\n  * @return  Promise\n  */ json () {\n        var _this2 = this;\n        return consumeBody.call(this).then(function(buffer) {\n            try {\n                return JSON.parse(buffer.toString());\n            } catch (err) {\n                return Body.Promise.reject(new FetchError(`invalid json response body at ${_this2.url} reason: ${err.message}`, \"invalid-json\"));\n            }\n        });\n    },\n    /**\n  * Decode response as text\n  *\n  * @return  Promise\n  */ text () {\n        return consumeBody.call(this).then(function(buffer) {\n            return buffer.toString();\n        });\n    },\n    /**\n  * Decode response as buffer (non-spec api)\n  *\n  * @return  Promise\n  */ buffer () {\n        return consumeBody.call(this);\n    },\n    /**\n  * Decode response as text, while automatically detecting the encoding and\n  * trying to decode to UTF-8 (non-spec api)\n  *\n  * @return  Promise\n  */ textConverted () {\n        var _this3 = this;\n        return consumeBody.call(this).then(function(buffer) {\n            return convertBody(buffer, _this3.headers);\n        });\n    }\n};\n// In browsers, all properties are enumerable.\nObject.defineProperties(Body.prototype, {\n    body: {\n        enumerable: true\n    },\n    bodyUsed: {\n        enumerable: true\n    },\n    arrayBuffer: {\n        enumerable: true\n    },\n    blob: {\n        enumerable: true\n    },\n    json: {\n        enumerable: true\n    },\n    text: {\n        enumerable: true\n    }\n});\nBody.mixIn = function(proto) {\n    for (const name of Object.getOwnPropertyNames(Body.prototype)){\n        // istanbul ignore else: future proof\n        if (!(name in proto)) {\n            const desc = Object.getOwnPropertyDescriptor(Body.prototype, name);\n            Object.defineProperty(proto, name, desc);\n        }\n    }\n};\n/**\n * Consume and convert an entire Body to a Buffer.\n *\n * Ref: https://fetch.spec.whatwg.org/#concept-body-consume-body\n *\n * @return  Promise\n */ function consumeBody() {\n    var _this4 = this;\n    if (this[INTERNALS].disturbed) {\n        return Body.Promise.reject(new TypeError(`body used already for: ${this.url}`));\n    }\n    this[INTERNALS].disturbed = true;\n    if (this[INTERNALS].error) {\n        return Body.Promise.reject(this[INTERNALS].error);\n    }\n    let body = this.body;\n    // body is null\n    if (body === null) {\n        return Body.Promise.resolve(Buffer.alloc(0));\n    }\n    // body is blob\n    if (isBlob(body)) {\n        body = body.stream();\n    }\n    // body is buffer\n    if (Buffer.isBuffer(body)) {\n        return Body.Promise.resolve(body);\n    }\n    // istanbul ignore if: should never happen\n    if (!(body instanceof stream__WEBPACK_IMPORTED_MODULE_0__)) {\n        return Body.Promise.resolve(Buffer.alloc(0));\n    }\n    // body is stream\n    // get ready to actually consume the body\n    let accum = [];\n    let accumBytes = 0;\n    let abort = false;\n    return new Body.Promise(function(resolve, reject) {\n        let resTimeout;\n        // allow timeout on slow response body\n        if (_this4.timeout) {\n            resTimeout = setTimeout(function() {\n                abort = true;\n                reject(new FetchError(`Response timeout while trying to fetch ${_this4.url} (over ${_this4.timeout}ms)`, \"body-timeout\"));\n            }, _this4.timeout);\n        }\n        // handle stream errors\n        body.on(\"error\", function(err) {\n            if (err.name === \"AbortError\") {\n                // if the request was aborted, reject with this Error\n                abort = true;\n                reject(err);\n            } else {\n                // other errors, such as incorrect content-encoding\n                reject(new FetchError(`Invalid response body while trying to fetch ${_this4.url}: ${err.message}`, \"system\", err));\n            }\n        });\n        body.on(\"data\", function(chunk) {\n            if (abort || chunk === null) {\n                return;\n            }\n            if (_this4.size && accumBytes + chunk.length > _this4.size) {\n                abort = true;\n                reject(new FetchError(`content size at ${_this4.url} over limit: ${_this4.size}`, \"max-size\"));\n                return;\n            }\n            accumBytes += chunk.length;\n            accum.push(chunk);\n        });\n        body.on(\"end\", function() {\n            if (abort) {\n                return;\n            }\n            clearTimeout(resTimeout);\n            try {\n                resolve(Buffer.concat(accum, accumBytes));\n            } catch (err) {\n                // handle streams that have accumulated too much data (issue #414)\n                reject(new FetchError(`Could not create Buffer from response body for ${_this4.url}: ${err.message}`, \"system\", err));\n            }\n        });\n    });\n}\n/**\n * Detect buffer encoding and convert to target encoding\n * ref: http://www.w3.org/TR/2011/WD-html5-20110113/parsing.html#determining-the-character-encoding\n *\n * @param   Buffer  buffer    Incoming buffer\n * @param   String  encoding  Target encoding\n * @return  String\n */ function convertBody(buffer, headers) {\n    if (typeof convert !== \"function\") {\n        throw new Error(\"The package `encoding` must be installed to use the textConverted() function\");\n    }\n    const ct = headers.get(\"content-type\");\n    let charset = \"utf-8\";\n    let res, str;\n    // header\n    if (ct) {\n        res = /charset=([^;]*)/i.exec(ct);\n    }\n    // no charset in content type, peek at response body for at most 1024 bytes\n    str = buffer.slice(0, 1024).toString();\n    // html5\n    if (!res && str) {\n        res = /<meta.+?charset=(['\"])(.+?)\\1/i.exec(str);\n    }\n    // html4\n    if (!res && str) {\n        res = /<meta[\\s]+?http-equiv=(['\"])content-type\\1[\\s]+?content=(['\"])(.+?)\\2/i.exec(str);\n        if (!res) {\n            res = /<meta[\\s]+?content=(['\"])(.+?)\\1[\\s]+?http-equiv=(['\"])content-type\\3/i.exec(str);\n            if (res) {\n                res.pop(); // drop last quote\n            }\n        }\n        if (res) {\n            res = /charset=(.*)/i.exec(res.pop());\n        }\n    }\n    // xml\n    if (!res && str) {\n        res = /<\\?xml.+?encoding=(['\"])(.+?)\\1/i.exec(str);\n    }\n    // found charset\n    if (res) {\n        charset = res.pop();\n        // prevent decode issues when sites use incorrect encoding\n        // ref: https://hsivonen.fi/encoding-menu/\n        if (charset === \"gb2312\" || charset === \"gbk\") {\n            charset = \"gb18030\";\n        }\n    }\n    // turn raw buffers into a single utf-8 buffer\n    return convert(buffer, \"UTF-8\", charset).toString();\n}\n/**\n * Detect a URLSearchParams object\n * ref: https://github.com/bitinn/node-fetch/issues/296#issuecomment-307598143\n *\n * @param   Object  obj     Object to detect by type or brand\n * @return  String\n */ function isURLSearchParams(obj) {\n    // Duck-typing as a necessary condition.\n    if (typeof obj !== \"object\" || typeof obj.append !== \"function\" || typeof obj.delete !== \"function\" || typeof obj.get !== \"function\" || typeof obj.getAll !== \"function\" || typeof obj.has !== \"function\" || typeof obj.set !== \"function\") {\n        return false;\n    }\n    // Brand-checking and more duck-typing as optional condition.\n    return obj.constructor.name === \"URLSearchParams\" || Object.prototype.toString.call(obj) === \"[object URLSearchParams]\" || typeof obj.sort === \"function\";\n}\n/**\n * Check if `obj` is a W3C `Blob` object (which `File` inherits from)\n * @param  {*} obj\n * @return {boolean}\n */ function isBlob(obj) {\n    return typeof obj === \"object\" && typeof obj.arrayBuffer === \"function\" && typeof obj.type === \"string\" && typeof obj.stream === \"function\" && typeof obj.constructor === \"function\" && typeof obj.constructor.name === \"string\" && /^(Blob|File)$/.test(obj.constructor.name) && /^(Blob|File)$/.test(obj[Symbol.toStringTag]);\n}\n/**\n * Clone body given Res/Req instance\n *\n * @param   Mixed  instance  Response or Request instance\n * @return  Mixed\n */ function clone(instance) {\n    let p1, p2;\n    let body = instance.body;\n    // don't allow cloning a used body\n    if (instance.bodyUsed) {\n        throw new Error(\"cannot clone body after it is used\");\n    }\n    // check that body is a stream and not form-data object\n    // note: we can't clone the form-data object without having it as a dependency\n    if (body instanceof stream__WEBPACK_IMPORTED_MODULE_0__ && typeof body.getBoundary !== \"function\") {\n        // tee instance body\n        p1 = new PassThrough();\n        p2 = new PassThrough();\n        body.pipe(p1);\n        body.pipe(p2);\n        // set instance body to teed body and return the other teed body\n        instance[INTERNALS].body = p1;\n        body = p2;\n    }\n    return body;\n}\n/**\n * Performs the operation \"extract a `Content-Type` value from |object|\" as\n * specified in the specification:\n * https://fetch.spec.whatwg.org/#concept-bodyinit-extract\n *\n * This function assumes that instance.body is present.\n *\n * @param   Mixed  instance  Any options.body input\n */ function extractContentType(body) {\n    if (body === null) {\n        // body is null\n        return null;\n    } else if (typeof body === \"string\") {\n        // body is string\n        return \"text/plain;charset=UTF-8\";\n    } else if (isURLSearchParams(body)) {\n        // body is a URLSearchParams\n        return \"application/x-www-form-urlencoded;charset=UTF-8\";\n    } else if (isBlob(body)) {\n        // body is blob\n        return body.type || null;\n    } else if (Buffer.isBuffer(body)) {\n        // body is buffer\n        return null;\n    } else if (Object.prototype.toString.call(body) === \"[object ArrayBuffer]\") {\n        // body is ArrayBuffer\n        return null;\n    } else if (ArrayBuffer.isView(body)) {\n        // body is ArrayBufferView\n        return null;\n    } else if (typeof body.getBoundary === \"function\") {\n        // detect form data input from form-data module\n        return `multipart/form-data;boundary=${body.getBoundary()}`;\n    } else if (body instanceof stream__WEBPACK_IMPORTED_MODULE_0__) {\n        // body is stream\n        // can't really do much about this\n        return null;\n    } else {\n        // Body constructor defaults other things to string\n        return \"text/plain;charset=UTF-8\";\n    }\n}\n/**\n * The Fetch Standard treats this as if \"total bytes\" is a property on the body.\n * For us, we have to explicitly get it with a function.\n *\n * ref: https://fetch.spec.whatwg.org/#concept-body-total-bytes\n *\n * @param   Body    instance   Instance of Body\n * @return  Number?            Number of bytes, or null if not possible\n */ function getTotalBytes(instance) {\n    const body = instance.body;\n    if (body === null) {\n        // body is null\n        return 0;\n    } else if (isBlob(body)) {\n        return body.size;\n    } else if (Buffer.isBuffer(body)) {\n        // body is buffer\n        return body.length;\n    } else if (body && typeof body.getLengthSync === \"function\") {\n        // detect form data input from form-data module\n        if (body._lengthRetrievers && body._lengthRetrievers.length == 0 || // 1.x\n        body.hasKnownLength && body.hasKnownLength()) {\n            // 2.x\n            return body.getLengthSync();\n        }\n        return null;\n    } else {\n        // body is stream\n        return null;\n    }\n}\n/**\n * Write a Body to a Node.js WritableStream (e.g. http.Request) object.\n *\n * @param   Body    instance   Instance of Body\n * @return  Void\n */ function writeToStream(dest, instance) {\n    const body = instance.body;\n    if (body === null) {\n        // body is null\n        dest.end();\n    } else if (isBlob(body)) {\n        body.stream().pipe(dest);\n    } else if (Buffer.isBuffer(body)) {\n        // body is buffer\n        dest.write(body);\n        dest.end();\n    } else {\n        // body is stream\n        body.pipe(dest);\n    }\n}\n// expose Promise\nBody.Promise = global.Promise;\n/**\n * headers.js\n *\n * Headers class offers convenient helpers\n */ const invalidTokenRegex = /[^\\^_`a-zA-Z\\-0-9!#$%&'*+.|~]/;\nconst invalidHeaderCharRegex = /[^\\t\\x20-\\x7e\\x80-\\xff]/;\nfunction validateName(name) {\n    name = `${name}`;\n    if (invalidTokenRegex.test(name) || name === \"\") {\n        throw new TypeError(`${name} is not a legal HTTP header name`);\n    }\n}\nfunction validateValue(value) {\n    value = `${value}`;\n    if (invalidHeaderCharRegex.test(value)) {\n        throw new TypeError(`${value} is not a legal HTTP header value`);\n    }\n}\n/**\n * Find the key in the map object given a header name.\n *\n * Returns undefined if not found.\n *\n * @param   String  name  Header name\n * @return  String|Undefined\n */ function find(map, name) {\n    name = name.toLowerCase();\n    for(const key in map){\n        if (key.toLowerCase() === name) {\n            return key;\n        }\n    }\n    return undefined;\n}\nconst MAP = Symbol(\"map\");\nclass Headers {\n    /**\n  * Headers class\n  *\n  * @param   Object  headers  Response headers\n  * @return  Void\n  */ constructor(){\n        let init = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : undefined;\n        this[MAP] = Object.create(null);\n        if (init instanceof Headers) {\n            const rawHeaders = init.raw();\n            const headerNames = Object.keys(rawHeaders);\n            for (const headerName of headerNames){\n                for (const value of rawHeaders[headerName]){\n                    this.append(headerName, value);\n                }\n            }\n            return;\n        }\n        // We don't worry about converting prop to ByteString here as append()\n        // will handle it.\n        if (init == null) ;\n        else if (typeof init === \"object\") {\n            const method = init[Symbol.iterator];\n            if (method != null) {\n                if (typeof method !== \"function\") {\n                    throw new TypeError(\"Header pairs must be iterable\");\n                }\n                // sequence<sequence<ByteString>>\n                // Note: per spec we have to first exhaust the lists then process them\n                const pairs = [];\n                for (const pair of init){\n                    if (typeof pair !== \"object\" || typeof pair[Symbol.iterator] !== \"function\") {\n                        throw new TypeError(\"Each header pair must be iterable\");\n                    }\n                    pairs.push(Array.from(pair));\n                }\n                for (const pair of pairs){\n                    if (pair.length !== 2) {\n                        throw new TypeError(\"Each header pair must be a name/value tuple\");\n                    }\n                    this.append(pair[0], pair[1]);\n                }\n            } else {\n                // record<ByteString, ByteString>\n                for (const key of Object.keys(init)){\n                    const value = init[key];\n                    this.append(key, value);\n                }\n            }\n        } else {\n            throw new TypeError(\"Provided initializer must be an object\");\n        }\n    }\n    /**\n  * Return combined header value given name\n  *\n  * @param   String  name  Header name\n  * @return  Mixed\n  */ get(name) {\n        name = `${name}`;\n        validateName(name);\n        const key = find(this[MAP], name);\n        if (key === undefined) {\n            return null;\n        }\n        return this[MAP][key].join(\", \");\n    }\n    /**\n  * Iterate over all headers\n  *\n  * @param   Function  callback  Executed for each item with parameters (value, name, thisArg)\n  * @param   Boolean   thisArg   `this` context for callback function\n  * @return  Void\n  */ forEach(callback) {\n        let thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;\n        let pairs = getHeaders(this);\n        let i = 0;\n        while(i < pairs.length){\n            var _pairs$i = pairs[i];\n            const name = _pairs$i[0], value = _pairs$i[1];\n            callback.call(thisArg, value, name, this);\n            pairs = getHeaders(this);\n            i++;\n        }\n    }\n    /**\n  * Overwrite header values given name\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */ set(name, value) {\n        name = `${name}`;\n        value = `${value}`;\n        validateName(name);\n        validateValue(value);\n        const key = find(this[MAP], name);\n        this[MAP][key !== undefined ? key : name] = [\n            value\n        ];\n    }\n    /**\n  * Append a value onto existing header\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */ append(name, value) {\n        name = `${name}`;\n        value = `${value}`;\n        validateName(name);\n        validateValue(value);\n        const key = find(this[MAP], name);\n        if (key !== undefined) {\n            this[MAP][key].push(value);\n        } else {\n            this[MAP][name] = [\n                value\n            ];\n        }\n    }\n    /**\n  * Check for header name existence\n  *\n  * @param   String   name  Header name\n  * @return  Boolean\n  */ has(name) {\n        name = `${name}`;\n        validateName(name);\n        return find(this[MAP], name) !== undefined;\n    }\n    /**\n  * Delete all header values given name\n  *\n  * @param   String  name  Header name\n  * @return  Void\n  */ delete(name) {\n        name = `${name}`;\n        validateName(name);\n        const key = find(this[MAP], name);\n        if (key !== undefined) {\n            delete this[MAP][key];\n        }\n    }\n    /**\n  * Return raw headers (non-spec api)\n  *\n  * @return  Object\n  */ raw() {\n        return this[MAP];\n    }\n    /**\n  * Get an iterator on keys.\n  *\n  * @return  Iterator\n  */ keys() {\n        return createHeadersIterator(this, \"key\");\n    }\n    /**\n  * Get an iterator on values.\n  *\n  * @return  Iterator\n  */ values() {\n        return createHeadersIterator(this, \"value\");\n    }\n    /**\n  * Get an iterator on entries.\n  *\n  * This is the default iterator of the Headers object.\n  *\n  * @return  Iterator\n  */ [Symbol.iterator]() {\n        return createHeadersIterator(this, \"key+value\");\n    }\n}\nHeaders.prototype.entries = Headers.prototype[Symbol.iterator];\nObject.defineProperty(Headers.prototype, Symbol.toStringTag, {\n    value: \"Headers\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\nObject.defineProperties(Headers.prototype, {\n    get: {\n        enumerable: true\n    },\n    forEach: {\n        enumerable: true\n    },\n    set: {\n        enumerable: true\n    },\n    append: {\n        enumerable: true\n    },\n    has: {\n        enumerable: true\n    },\n    delete: {\n        enumerable: true\n    },\n    keys: {\n        enumerable: true\n    },\n    values: {\n        enumerable: true\n    },\n    entries: {\n        enumerable: true\n    }\n});\nfunction getHeaders(headers) {\n    let kind = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"key+value\";\n    const keys = Object.keys(headers[MAP]).sort();\n    return keys.map(kind === \"key\" ? function(k) {\n        return k.toLowerCase();\n    } : kind === \"value\" ? function(k) {\n        return headers[MAP][k].join(\", \");\n    } : function(k) {\n        return [\n            k.toLowerCase(),\n            headers[MAP][k].join(\", \")\n        ];\n    });\n}\nconst INTERNAL = Symbol(\"internal\");\nfunction createHeadersIterator(target, kind) {\n    const iterator = Object.create(HeadersIteratorPrototype);\n    iterator[INTERNAL] = {\n        target,\n        kind,\n        index: 0\n    };\n    return iterator;\n}\nconst HeadersIteratorPrototype = Object.setPrototypeOf({\n    next () {\n        // istanbul ignore if\n        if (!this || Object.getPrototypeOf(this) !== HeadersIteratorPrototype) {\n            throw new TypeError(\"Value of `this` is not a HeadersIterator\");\n        }\n        var _INTERNAL = this[INTERNAL];\n        const target = _INTERNAL.target, kind = _INTERNAL.kind, index = _INTERNAL.index;\n        const values = getHeaders(target, kind);\n        const len = values.length;\n        if (index >= len) {\n            return {\n                value: undefined,\n                done: true\n            };\n        }\n        this[INTERNAL].index = index + 1;\n        return {\n            value: values[index],\n            done: false\n        };\n    }\n}, Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));\nObject.defineProperty(HeadersIteratorPrototype, Symbol.toStringTag, {\n    value: \"HeadersIterator\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\n/**\n * Export the Headers object in a form that Node.js can consume.\n *\n * @param   Headers  headers\n * @return  Object\n */ function exportNodeCompatibleHeaders(headers) {\n    const obj = Object.assign({\n        __proto__: null\n    }, headers[MAP]);\n    // http.request() only supports string as Host header. This hack makes\n    // specifying custom Host header possible.\n    const hostHeaderKey = find(headers[MAP], \"Host\");\n    if (hostHeaderKey !== undefined) {\n        obj[hostHeaderKey] = obj[hostHeaderKey][0];\n    }\n    return obj;\n}\n/**\n * Create a Headers object from an object of headers, ignoring those that do\n * not conform to HTTP grammar productions.\n *\n * @param   Object  obj  Object of headers\n * @return  Headers\n */ function createHeadersLenient(obj) {\n    const headers = new Headers();\n    for (const name of Object.keys(obj)){\n        if (invalidTokenRegex.test(name)) {\n            continue;\n        }\n        if (Array.isArray(obj[name])) {\n            for (const val of obj[name]){\n                if (invalidHeaderCharRegex.test(val)) {\n                    continue;\n                }\n                if (headers[MAP][name] === undefined) {\n                    headers[MAP][name] = [\n                        val\n                    ];\n                } else {\n                    headers[MAP][name].push(val);\n                }\n            }\n        } else if (!invalidHeaderCharRegex.test(obj[name])) {\n            headers[MAP][name] = [\n                obj[name]\n            ];\n        }\n    }\n    return headers;\n}\nconst INTERNALS$1 = Symbol(\"Response internals\");\n// fix an issue where \"STATUS_CODES\" aren't a named export for node <10\nconst STATUS_CODES = http__WEBPACK_IMPORTED_MODULE_1__.STATUS_CODES;\n/**\n * Response class\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */ class Response {\n    constructor(){\n        let body = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n        let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        Body.call(this, body, opts);\n        const status = opts.status || 200;\n        const headers = new Headers(opts.headers);\n        if (body != null && !headers.has(\"Content-Type\")) {\n            const contentType = extractContentType(body);\n            if (contentType) {\n                headers.append(\"Content-Type\", contentType);\n            }\n        }\n        this[INTERNALS$1] = {\n            url: opts.url,\n            status,\n            statusText: opts.statusText || STATUS_CODES[status],\n            headers,\n            counter: opts.counter\n        };\n    }\n    get url() {\n        return this[INTERNALS$1].url || \"\";\n    }\n    get status() {\n        return this[INTERNALS$1].status;\n    }\n    /**\n  * Convenience property representing if the request ended normally\n  */ get ok() {\n        return this[INTERNALS$1].status >= 200 && this[INTERNALS$1].status < 300;\n    }\n    get redirected() {\n        return this[INTERNALS$1].counter > 0;\n    }\n    get statusText() {\n        return this[INTERNALS$1].statusText;\n    }\n    get headers() {\n        return this[INTERNALS$1].headers;\n    }\n    /**\n  * Clone this response\n  *\n  * @return  Response\n  */ clone() {\n        return new Response(clone(this), {\n            url: this.url,\n            status: this.status,\n            statusText: this.statusText,\n            headers: this.headers,\n            ok: this.ok,\n            redirected: this.redirected\n        });\n    }\n}\nBody.mixIn(Response.prototype);\nObject.defineProperties(Response.prototype, {\n    url: {\n        enumerable: true\n    },\n    status: {\n        enumerable: true\n    },\n    ok: {\n        enumerable: true\n    },\n    redirected: {\n        enumerable: true\n    },\n    statusText: {\n        enumerable: true\n    },\n    headers: {\n        enumerable: true\n    },\n    clone: {\n        enumerable: true\n    }\n});\nObject.defineProperty(Response.prototype, Symbol.toStringTag, {\n    value: \"Response\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\nconst INTERNALS$2 = Symbol(\"Request internals\");\nconst URL = url__WEBPACK_IMPORTED_MODULE_2__.URL || whatwg_url__WEBPACK_IMPORTED_MODULE_3__.URL;\n// fix an issue where \"format\", \"parse\" aren't a named export for node <10\nconst parse_url = url__WEBPACK_IMPORTED_MODULE_2__.parse;\nconst format_url = url__WEBPACK_IMPORTED_MODULE_2__.format;\n/**\n * Wrapper around `new URL` to handle arbitrary URLs\n *\n * @param  {string} urlStr\n * @return {void}\n */ function parseURL(urlStr) {\n    /*\n \tCheck whether the URL is absolute or not\n \t\tScheme: https://tools.ietf.org/html/rfc3986#section-3.1\n \tAbsolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\n */ if (/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.exec(urlStr)) {\n        urlStr = new URL(urlStr).toString();\n    }\n    // Fallback to old implementation for arbitrary URLs\n    return parse_url(urlStr);\n}\nconst streamDestructionSupported = \"destroy\" in stream__WEBPACK_IMPORTED_MODULE_0__.Readable.prototype;\n/**\n * Check if a value is an instance of Request.\n *\n * @param   Mixed   input\n * @return  Boolean\n */ function isRequest(input) {\n    return typeof input === \"object\" && typeof input[INTERNALS$2] === \"object\";\n}\nfunction isAbortSignal(signal) {\n    const proto = signal && typeof signal === \"object\" && Object.getPrototypeOf(signal);\n    return !!(proto && proto.constructor.name === \"AbortSignal\");\n}\n/**\n * Request class\n *\n * @param   Mixed   input  Url or Request instance\n * @param   Object  init   Custom options\n * @return  Void\n */ class Request {\n    constructor(input){\n        let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        let parsedURL;\n        // normalize input\n        if (!isRequest(input)) {\n            if (input && input.href) {\n                // in order to support Node.js' Url objects; though WHATWG's URL objects\n                // will fall into this branch also (since their `toString()` will return\n                // `href` property anyway)\n                parsedURL = parseURL(input.href);\n            } else {\n                // coerce input to a string before attempting to parse\n                parsedURL = parseURL(`${input}`);\n            }\n            input = {};\n        } else {\n            parsedURL = parseURL(input.url);\n        }\n        let method = init.method || input.method || \"GET\";\n        method = method.toUpperCase();\n        if ((init.body != null || isRequest(input) && input.body !== null) && (method === \"GET\" || method === \"HEAD\")) {\n            throw new TypeError(\"Request with GET/HEAD method cannot have body\");\n        }\n        let inputBody = init.body != null ? init.body : isRequest(input) && input.body !== null ? clone(input) : null;\n        Body.call(this, inputBody, {\n            timeout: init.timeout || input.timeout || 0,\n            size: init.size || input.size || 0\n        });\n        const headers = new Headers(init.headers || input.headers || {});\n        if (inputBody != null && !headers.has(\"Content-Type\")) {\n            const contentType = extractContentType(inputBody);\n            if (contentType) {\n                headers.append(\"Content-Type\", contentType);\n            }\n        }\n        let signal = isRequest(input) ? input.signal : null;\n        if (\"signal\" in init) signal = init.signal;\n        if (signal != null && !isAbortSignal(signal)) {\n            throw new TypeError(\"Expected signal to be an instanceof AbortSignal\");\n        }\n        this[INTERNALS$2] = {\n            method,\n            redirect: init.redirect || input.redirect || \"follow\",\n            headers,\n            parsedURL,\n            signal\n        };\n        // node-fetch-only options\n        this.follow = init.follow !== undefined ? init.follow : input.follow !== undefined ? input.follow : 20;\n        this.compress = init.compress !== undefined ? init.compress : input.compress !== undefined ? input.compress : true;\n        this.counter = init.counter || input.counter || 0;\n        this.agent = init.agent || input.agent;\n    }\n    get method() {\n        return this[INTERNALS$2].method;\n    }\n    get url() {\n        return format_url(this[INTERNALS$2].parsedURL);\n    }\n    get headers() {\n        return this[INTERNALS$2].headers;\n    }\n    get redirect() {\n        return this[INTERNALS$2].redirect;\n    }\n    get signal() {\n        return this[INTERNALS$2].signal;\n    }\n    /**\n  * Clone this request\n  *\n  * @return  Request\n  */ clone() {\n        return new Request(this);\n    }\n}\nBody.mixIn(Request.prototype);\nObject.defineProperty(Request.prototype, Symbol.toStringTag, {\n    value: \"Request\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\nObject.defineProperties(Request.prototype, {\n    method: {\n        enumerable: true\n    },\n    url: {\n        enumerable: true\n    },\n    headers: {\n        enumerable: true\n    },\n    redirect: {\n        enumerable: true\n    },\n    clone: {\n        enumerable: true\n    },\n    signal: {\n        enumerable: true\n    }\n});\n/**\n * Convert a Request to Node.js http request options.\n *\n * @param   Request  A Request instance\n * @return  Object   The options object to be passed to http.request\n */ function getNodeRequestOptions(request) {\n    const parsedURL = request[INTERNALS$2].parsedURL;\n    const headers = new Headers(request[INTERNALS$2].headers);\n    // fetch step 1.3\n    if (!headers.has(\"Accept\")) {\n        headers.set(\"Accept\", \"*/*\");\n    }\n    // Basic fetch\n    if (!parsedURL.protocol || !parsedURL.hostname) {\n        throw new TypeError(\"Only absolute URLs are supported\");\n    }\n    if (!/^https?:$/.test(parsedURL.protocol)) {\n        throw new TypeError(\"Only HTTP(S) protocols are supported\");\n    }\n    if (request.signal && request.body instanceof stream__WEBPACK_IMPORTED_MODULE_0__.Readable && !streamDestructionSupported) {\n        throw new Error(\"Cancellation of streamed requests with AbortSignal is not supported in node < 8\");\n    }\n    // HTTP-network-or-cache fetch steps 2.4-2.7\n    let contentLengthValue = null;\n    if (request.body == null && /^(POST|PUT)$/i.test(request.method)) {\n        contentLengthValue = \"0\";\n    }\n    if (request.body != null) {\n        const totalBytes = getTotalBytes(request);\n        if (typeof totalBytes === \"number\") {\n            contentLengthValue = String(totalBytes);\n        }\n    }\n    if (contentLengthValue) {\n        headers.set(\"Content-Length\", contentLengthValue);\n    }\n    // HTTP-network-or-cache fetch step 2.11\n    if (!headers.has(\"User-Agent\")) {\n        headers.set(\"User-Agent\", \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\");\n    }\n    // HTTP-network-or-cache fetch step 2.15\n    if (request.compress && !headers.has(\"Accept-Encoding\")) {\n        headers.set(\"Accept-Encoding\", \"gzip,deflate\");\n    }\n    let agent = request.agent;\n    if (typeof agent === \"function\") {\n        agent = agent(parsedURL);\n    }\n    // HTTP-network fetch step 4.2\n    // chunked encoding is handled by Node.js\n    return Object.assign({}, parsedURL, {\n        method: request.method,\n        headers: exportNodeCompatibleHeaders(headers),\n        agent\n    });\n}\n/**\n * abort-error.js\n *\n * AbortError interface for cancelled requests\n */ /**\n * Create AbortError instance\n *\n * @param   String      message      Error message for human\n * @return  AbortError\n */ function AbortError(message) {\n    Error.call(this, message);\n    this.type = \"aborted\";\n    this.message = message;\n    // hide custom error implementation details from end-users\n    Error.captureStackTrace(this, this.constructor);\n}\nAbortError.prototype = Object.create(Error.prototype);\nAbortError.prototype.constructor = AbortError;\nAbortError.prototype.name = \"AbortError\";\nconst URL$1 = url__WEBPACK_IMPORTED_MODULE_2__.URL || whatwg_url__WEBPACK_IMPORTED_MODULE_3__.URL;\n// fix an issue where \"PassThrough\", \"resolve\" aren't a named export for node <10\nconst PassThrough$1 = stream__WEBPACK_IMPORTED_MODULE_0__.PassThrough;\nconst isDomainOrSubdomain = function isDomainOrSubdomain(destination, original) {\n    const orig = new URL$1(original).hostname;\n    const dest = new URL$1(destination).hostname;\n    return orig === dest || orig[orig.length - dest.length - 1] === \".\" && orig.endsWith(dest);\n};\n/**\n * isSameProtocol reports whether the two provided URLs use the same protocol.\n *\n * Both domains must already be in canonical form.\n * @param {string|URL} original\n * @param {string|URL} destination\n */ const isSameProtocol = function isSameProtocol(destination, original) {\n    const orig = new URL$1(original).protocol;\n    const dest = new URL$1(destination).protocol;\n    return orig === dest;\n};\n/**\n * Fetch function\n *\n * @param   Mixed    url   Absolute url or Request instance\n * @param   Object   opts  Fetch options\n * @return  Promise\n */ function fetch(url, opts) {\n    // allow custom promise\n    if (!fetch.Promise) {\n        throw new Error(\"native promise missing, set fetch.Promise to your favorite alternative\");\n    }\n    Body.Promise = fetch.Promise;\n    // wrap http.request into fetch\n    return new fetch.Promise(function(resolve, reject) {\n        // build request object\n        const request = new Request(url, opts);\n        const options = getNodeRequestOptions(request);\n        const send = (options.protocol === \"https:\" ? https__WEBPACK_IMPORTED_MODULE_4__ : http__WEBPACK_IMPORTED_MODULE_1__).request;\n        const signal = request.signal;\n        let response = null;\n        const abort = function abort() {\n            let error = new AbortError(\"The user aborted a request.\");\n            reject(error);\n            if (request.body && request.body instanceof stream__WEBPACK_IMPORTED_MODULE_0__.Readable) {\n                destroyStream(request.body, error);\n            }\n            if (!response || !response.body) return;\n            response.body.emit(\"error\", error);\n        };\n        if (signal && signal.aborted) {\n            abort();\n            return;\n        }\n        const abortAndFinalize = function abortAndFinalize() {\n            abort();\n            finalize();\n        };\n        // send request\n        const req = send(options);\n        let reqTimeout;\n        if (signal) {\n            signal.addEventListener(\"abort\", abortAndFinalize);\n        }\n        function finalize() {\n            req.abort();\n            if (signal) signal.removeEventListener(\"abort\", abortAndFinalize);\n            clearTimeout(reqTimeout);\n        }\n        if (request.timeout) {\n            req.once(\"socket\", function(socket) {\n                reqTimeout = setTimeout(function() {\n                    reject(new FetchError(`network timeout at: ${request.url}`, \"request-timeout\"));\n                    finalize();\n                }, request.timeout);\n            });\n        }\n        req.on(\"error\", function(err) {\n            reject(new FetchError(`request to ${request.url} failed, reason: ${err.message}`, \"system\", err));\n            if (response && response.body) {\n                destroyStream(response.body, err);\n            }\n            finalize();\n        });\n        fixResponseChunkedTransferBadEnding(req, function(err) {\n            if (signal && signal.aborted) {\n                return;\n            }\n            if (response && response.body) {\n                destroyStream(response.body, err);\n            }\n        });\n        /* c8 ignore next 18 */ if (parseInt(process.version.substring(1)) < 14) {\n            // Before Node.js 14, pipeline() does not fully support async iterators and does not always\n            // properly handle when the socket close/end events are out of order.\n            req.on(\"socket\", function(s) {\n                s.addListener(\"close\", function(hadError) {\n                    // if a data listener is still present we didn't end cleanly\n                    const hasDataListener = s.listenerCount(\"data\") > 0;\n                    // if end happened before close but the socket didn't emit an error, do it now\n                    if (response && hasDataListener && !hadError && !(signal && signal.aborted)) {\n                        const err = new Error(\"Premature close\");\n                        err.code = \"ERR_STREAM_PREMATURE_CLOSE\";\n                        response.body.emit(\"error\", err);\n                    }\n                });\n            });\n        }\n        req.on(\"response\", function(res) {\n            clearTimeout(reqTimeout);\n            const headers = createHeadersLenient(res.headers);\n            // HTTP fetch step 5\n            if (fetch.isRedirect(res.statusCode)) {\n                // HTTP fetch step 5.2\n                const location = headers.get(\"Location\");\n                // HTTP fetch step 5.3\n                let locationURL = null;\n                try {\n                    locationURL = location === null ? null : new URL$1(location, request.url).toString();\n                } catch (err) {\n                    // error here can only be invalid URL in Location: header\n                    // do not throw when options.redirect == manual\n                    // let the user extract the errorneous redirect URL\n                    if (request.redirect !== \"manual\") {\n                        reject(new FetchError(`uri requested responds with an invalid redirect URL: ${location}`, \"invalid-redirect\"));\n                        finalize();\n                        return;\n                    }\n                }\n                // HTTP fetch step 5.5\n                switch(request.redirect){\n                    case \"error\":\n                        reject(new FetchError(`uri requested responds with a redirect, redirect mode is set to error: ${request.url}`, \"no-redirect\"));\n                        finalize();\n                        return;\n                    case \"manual\":\n                        // node-fetch-specific step: make manual redirect a bit easier to use by setting the Location header value to the resolved URL.\n                        if (locationURL !== null) {\n                            // handle corrupted header\n                            try {\n                                headers.set(\"Location\", locationURL);\n                            } catch (err) {\n                                // istanbul ignore next: nodejs server prevent invalid response headers, we can't test this through normal request\n                                reject(err);\n                            }\n                        }\n                        break;\n                    case \"follow\":\n                        // HTTP-redirect fetch step 2\n                        if (locationURL === null) {\n                            break;\n                        }\n                        // HTTP-redirect fetch step 5\n                        if (request.counter >= request.follow) {\n                            reject(new FetchError(`maximum redirect reached at: ${request.url}`, \"max-redirect\"));\n                            finalize();\n                            return;\n                        }\n                        // HTTP-redirect fetch step 6 (counter increment)\n                        // Create a new Request object.\n                        const requestOpts = {\n                            headers: new Headers(request.headers),\n                            follow: request.follow,\n                            counter: request.counter + 1,\n                            agent: request.agent,\n                            compress: request.compress,\n                            method: request.method,\n                            body: request.body,\n                            signal: request.signal,\n                            timeout: request.timeout,\n                            size: request.size\n                        };\n                        if (!isDomainOrSubdomain(request.url, locationURL) || !isSameProtocol(request.url, locationURL)) {\n                            for (const name of [\n                                \"authorization\",\n                                \"www-authenticate\",\n                                \"cookie\",\n                                \"cookie2\"\n                            ]){\n                                requestOpts.headers.delete(name);\n                            }\n                        }\n                        // HTTP-redirect fetch step 9\n                        if (res.statusCode !== 303 && request.body && getTotalBytes(request) === null) {\n                            reject(new FetchError(\"Cannot follow redirect with body being a readable stream\", \"unsupported-redirect\"));\n                            finalize();\n                            return;\n                        }\n                        // HTTP-redirect fetch step 11\n                        if (res.statusCode === 303 || (res.statusCode === 301 || res.statusCode === 302) && request.method === \"POST\") {\n                            requestOpts.method = \"GET\";\n                            requestOpts.body = undefined;\n                            requestOpts.headers.delete(\"content-length\");\n                        }\n                        // HTTP-redirect fetch step 15\n                        resolve(fetch(new Request(locationURL, requestOpts)));\n                        finalize();\n                        return;\n                }\n            }\n            // prepare response\n            res.once(\"end\", function() {\n                if (signal) signal.removeEventListener(\"abort\", abortAndFinalize);\n            });\n            let body = res.pipe(new PassThrough$1());\n            const response_options = {\n                url: request.url,\n                status: res.statusCode,\n                statusText: res.statusMessage,\n                headers: headers,\n                size: request.size,\n                timeout: request.timeout,\n                counter: request.counter\n            };\n            // HTTP-network fetch step ********\n            const codings = headers.get(\"Content-Encoding\");\n            // HTTP-network fetch step ********: handle content codings\n            // in following scenarios we ignore compression support\n            // 1. compression support is disabled\n            // 2. HEAD request\n            // 3. no Content-Encoding header\n            // 4. no content response (204)\n            // 5. content not modified response (304)\n            if (!request.compress || request.method === \"HEAD\" || codings === null || res.statusCode === 204 || res.statusCode === 304) {\n                response = new Response(body, response_options);\n                resolve(response);\n                return;\n            }\n            // For Node v6+\n            // Be less strict when decoding compressed responses, since sometimes\n            // servers send slightly invalid responses that are still accepted\n            // by common browsers.\n            // Always using Z_SYNC_FLUSH is what cURL does.\n            const zlibOptions = {\n                flush: zlib__WEBPACK_IMPORTED_MODULE_5__.Z_SYNC_FLUSH,\n                finishFlush: zlib__WEBPACK_IMPORTED_MODULE_5__.Z_SYNC_FLUSH\n            };\n            // for gzip\n            if (codings == \"gzip\" || codings == \"x-gzip\") {\n                body = body.pipe(zlib__WEBPACK_IMPORTED_MODULE_5__.createGunzip(zlibOptions));\n                response = new Response(body, response_options);\n                resolve(response);\n                return;\n            }\n            // for deflate\n            if (codings == \"deflate\" || codings == \"x-deflate\") {\n                // handle the infamous raw deflate response from old servers\n                // a hack for old IIS and Apache servers\n                const raw = res.pipe(new PassThrough$1());\n                raw.once(\"data\", function(chunk) {\n                    // see http://stackoverflow.com/questions/37519828\n                    if ((chunk[0] & 0x0F) === 0x08) {\n                        body = body.pipe(zlib__WEBPACK_IMPORTED_MODULE_5__.createInflate());\n                    } else {\n                        body = body.pipe(zlib__WEBPACK_IMPORTED_MODULE_5__.createInflateRaw());\n                    }\n                    response = new Response(body, response_options);\n                    resolve(response);\n                });\n                raw.on(\"end\", function() {\n                    // some old IIS servers return zero-length OK deflate responses, so 'data' is never emitted.\n                    if (!response) {\n                        response = new Response(body, response_options);\n                        resolve(response);\n                    }\n                });\n                return;\n            }\n            // for br\n            if (codings == \"br\" && typeof zlib__WEBPACK_IMPORTED_MODULE_5__.createBrotliDecompress === \"function\") {\n                body = body.pipe(zlib__WEBPACK_IMPORTED_MODULE_5__.createBrotliDecompress());\n                response = new Response(body, response_options);\n                resolve(response);\n                return;\n            }\n            // otherwise, use response as-is\n            response = new Response(body, response_options);\n            resolve(response);\n        });\n        writeToStream(req, request);\n    });\n}\nfunction fixResponseChunkedTransferBadEnding(request, errorCallback) {\n    let socket;\n    request.on(\"socket\", function(s) {\n        socket = s;\n    });\n    request.on(\"response\", function(response) {\n        const headers = response.headers;\n        if (headers[\"transfer-encoding\"] === \"chunked\" && !headers[\"content-length\"]) {\n            response.once(\"close\", function(hadError) {\n                // tests for socket presence, as in some situations the\n                // the 'socket' event is not triggered for the request\n                // (happens in deno), avoids `TypeError`\n                // if a data listener is still present we didn't end cleanly\n                const hasDataListener = socket && socket.listenerCount(\"data\") > 0;\n                if (hasDataListener && !hadError) {\n                    const err = new Error(\"Premature close\");\n                    err.code = \"ERR_STREAM_PREMATURE_CLOSE\";\n                    errorCallback(err);\n                }\n            });\n        }\n    });\n}\nfunction destroyStream(stream, err) {\n    if (stream.destroy) {\n        stream.destroy(err);\n    } else {\n        // node < 8\n        stream.emit(\"error\", err);\n        stream.end();\n    }\n}\n/**\n * Redirect code matching\n *\n * @param   Number   code  Status code\n * @return  Boolean\n */ fetch.isRedirect = function(code) {\n    return code === 301 || code === 302 || code === 303 || code === 307 || code === 308;\n};\n// expose Promise\nfetch.Promise = global.Promise;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (fetch);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/node-fetch/lib/index.mjs\n");

/***/ })

};
;