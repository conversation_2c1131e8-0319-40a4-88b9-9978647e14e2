"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/swr";
exports.ids = ["vendor-chunks/swr"];
exports.modules = {

/***/ "(ssr)/./node_modules/swr/_internal/dist/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/swr/_internal/dist/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IS_REACT_LEGACY: () => (/* binding */ IS_REACT_LEGACY),\n/* harmony export */   IS_SERVER: () => (/* binding */ IS_SERVER),\n/* harmony export */   OBJECT: () => (/* binding */ OBJECT),\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   SWRGlobalState: () => (/* binding */ SWRGlobalState),\n/* harmony export */   UNDEFINED: () => (/* binding */ UNDEFINED),\n/* harmony export */   cache: () => (/* binding */ cache),\n/* harmony export */   compare: () => (/* binding */ compare),\n/* harmony export */   createCacheHelper: () => (/* binding */ createCacheHelper),\n/* harmony export */   defaultConfig: () => (/* binding */ defaultConfig),\n/* harmony export */   defaultConfigOptions: () => (/* binding */ defaultConfigOptions),\n/* harmony export */   getTimestamp: () => (/* binding */ getTimestamp),\n/* harmony export */   hasRequestAnimationFrame: () => (/* binding */ hasRequestAnimationFrame),\n/* harmony export */   initCache: () => (/* binding */ initCache),\n/* harmony export */   internalMutate: () => (/* binding */ internalMutate),\n/* harmony export */   isDocumentDefined: () => (/* binding */ isDocumentDefined),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isPromiseLike: () => (/* binding */ isPromiseLike),\n/* harmony export */   isUndefined: () => (/* binding */ isUndefined),\n/* harmony export */   isWindowDefined: () => (/* binding */ isWindowDefined),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   mergeObjects: () => (/* binding */ mergeObjects),\n/* harmony export */   mutate: () => (/* binding */ mutate),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   preload: () => (/* binding */ preload),\n/* harmony export */   preset: () => (/* binding */ preset),\n/* harmony export */   rAF: () => (/* binding */ rAF),\n/* harmony export */   revalidateEvents: () => (/* binding */ constants),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   slowConnection: () => (/* binding */ slowConnection),\n/* harmony export */   stableHash: () => (/* binding */ stableHash),\n/* harmony export */   subscribeCallback: () => (/* binding */ subscribeCallback),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useSWRConfig: () => (/* binding */ useSWRConfig),\n/* harmony export */   useStateWithDeps: () => (/* binding */ useStateWithDeps),\n/* harmony export */   withArgs: () => (/* binding */ withArgs),\n/* harmony export */   withMiddleware: () => (/* binding */ withMiddleware)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == \"function\";\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const constructor = arg && arg.constructor;\n    const isDate = constructor == Date;\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && constructor != RegExp) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + \"~\";\n        table.set(arg, result);\n        if (constructor == Array) {\n            // Array.\n            result = \"@\";\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + \",\";\n            }\n            table.set(arg, result);\n        }\n        if (constructor == OBJECT) {\n            // Object, sort keys.\n            result = \"#\";\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + \":\" + stableHash(arg[index]) + \",\";\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == \"symbol\" ? arg.toString() : type == \"string\" ? JSON.stringify(arg) : \"\" + arg;\n    }\n    return result;\n};\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = \"undefined\";\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = \"undefined\" != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window[\"requestAnimationFrame\"] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== \"hidden\";\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener(\"visibilitychange\", callback);\n    }\n    onWindowEvent(\"focus\", callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener(\"visibilitychange\", callback);\n        }\n        offWindowEvent(\"focus\", callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent(\"online\", onOnline);\n    onWindowEvent(\"offline\", onOffline);\n    return ()=>{\n        offWindowEvent(\"online\", onOnline);\n        offWindowEvent(\"offline\", onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\nconst IS_REACT_LEGACY = !react__WEBPACK_IMPORTED_MODULE_0__.useId;\nconst IS_SERVER = !isWindowDefined || \"Deno\" in window;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window[\"requestAnimationFrame\"](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== \"undefined\" && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    \"slow-2g\",\n    \"2g\"\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = \"\";\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == \"string\" ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : \"\";\n    return [\n        key,\n        args\n    ];\n};\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\nvar constants = {\n    __proto__: null,\n    ERROR_REVALIDATE_EVENT: ERROR_REVALIDATE_EVENT,\n    FOCUS_EVENT: FOCUS_EVENT,\n    MUTATE_EVENT: MUTATE_EVENT,\n    RECONNECT_EVENT: RECONNECT_EVENT\n};\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === \"boolean\" ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const revalidate = options.revalidate !== false;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === \"function\" ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (!/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const revalidators = EVENT_REVALIDATORS[key];\n        const startRevalidate = ()=>{\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                data = committedData;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    data = populateCache(data, committedData);\n                }\n                // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                set({\n                    data,\n                    error: UNDEFINED,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        const res = await startRevalidate();\n        // The mutation and revalidation are ended, we can clear it since the data is\n        // not an optimistic value anymore.\n        set({\n            _c: UNDEFINED\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return populateCache ? res : data;\n    }\n}\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = {};\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = {};\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    {},\n                    {},\n                    {},\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = (currentData, newData)=>stableHash(currentData) == stableHash(newData);\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, preset);\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\nconst SWRConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = react__WEBPACK_IMPORTED_MODULE_0__;\n    }\n};\nconst normalize = (args)=>{\n    return isFunction(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\nconst useSWRConfig = ()=>{\n    return mergeObjects(defaultConfig, (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext));\n};\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = serialize(key_);\n    const [, , , PRELOAD] = SWRGlobalState.get(cache);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = serialize(key_);\n            const [, , , PRELOAD] = SWRGlobalState.get(cache);\n            const req = PRELOAD[key];\n            if (isUndefined(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = mergeConfigs(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n/**\n * An implementation of state with dependency-tracking.\n */ const useStateWithDeps = (state)=>{\n    const rerender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({})[1];\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const stateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(state);\n    // If a state property (data, error, or isValidating) is accessed by the render\n    // function, we mark the property as a dependency so if it is updated again\n    // in the future, we trigger a rerender.\n    // This is also known as dependency-tracking.\n    const stateDependenciesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        data: false,\n        error: false,\n        isValidating: false\n    });\n    /**\n   * @param payload To change stateRef, pass the values explicitly to setState:\n   * @example\n   * ```js\n   * setState({\n   *   isValidating: false\n   *   data: newData // set data to newData\n   *   error: undefined // set error to undefined\n   * })\n   *\n   * setState({\n   *   isValidating: false\n   *   data: undefined // set data to undefined\n   *   error: err // set error to err\n   * })\n   * ```\n   */ const setState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((payload)=>{\n        let shouldRerender = false;\n        const currentState = stateRef.current;\n        for(const _ in payload){\n            const k = _;\n            // If the property has changed, update the state and mark rerender as\n            // needed.\n            if (currentState[k] !== payload[k]) {\n                currentState[k] = payload[k];\n                // If the property is accessed by the component, a rerender should be\n                // triggered.\n                if (stateDependenciesRef.current[k]) {\n                    shouldRerender = true;\n                }\n            }\n        }\n        if (shouldRerender && !unmountedRef.current) {\n            if (IS_REACT_LEGACY) {\n                rerender({});\n            } else {\n                react__WEBPACK_IMPORTED_MODULE_0__.startTransition(()=>rerender({}));\n            }\n        }\n    }, [\n        rerender\n    ]);\n    useIsomorphicLayoutEffect(()=>{\n        unmountedRef.current = false;\n        return ()=>{\n            unmountedRef.current = true;\n        };\n    });\n    return [\n        stateRef,\n        stateDependenciesRef.current,\n        setState\n    ];\n};\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\nsetupDevTools();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/_internal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/core/dist/index.mjs":
/*!**********************************************!*\
  !*** ./node_modules/swr/core/dist/index.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   \"default\": () => (/* binding */ useSWR),\n/* harmony export */   mutate: () => (/* reexport safe */ swr_internal__WEBPACK_IMPORTED_MODULE_2__.mutate),\n/* harmony export */   preload: () => (/* reexport safe */ swr_internal__WEBPACK_IMPORTED_MODULE_2__.preload),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize),\n/* harmony export */   useSWRConfig: () => (/* reexport safe */ swr_internal__WEBPACK_IMPORTED_MODULE_2__.useSWRConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var swr_internal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swr/_internal */ \"(ssr)/./node_modules/swr/_internal/dist/index.mjs\");\n\n\n\n\nconst unstable_serialize = (key)=>(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.serialize)(key)[0];\n/// <reference types=\"react/experimental\" />\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || ((promise)=>{\n    if (promise.status === \"pending\") {\n        throw promise;\n    } else if (promise.status === \"fulfilled\") {\n        return promise.value;\n    } else if (promise.status === \"rejected\") {\n        throw promise.reason;\n    } else {\n        promise.status = \"pending\";\n        promise.then((v)=>{\n            promise.status = \"fulfilled\";\n            promise.value = v;\n        }, (e)=>{\n            promise.status = \"rejected\";\n            promise.reason = e;\n        });\n        throw promise;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = swr_internal__WEBPACK_IMPORTED_MODULE_2__.SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.serialize)(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Refs to keep the key and config.\n    const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(key);\n    const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fetcher);\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.createCacheHelper)(cache, key);\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({}).current;\n    const fallback = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(fallbackData) ? config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === \"data\") {\n                if (!compare(prev[t], current[t])) {\n                    if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(revalidateIfStale)) return revalidateIfStale;\n            return true;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.mergeObjects)(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(cachedData) ? fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(data);\n    const returnedData = keepPreviousData ? (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(cachedData) ? laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (swr_internal__WEBPACK_IMPORTED_MODULE_2__.IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.getTimestamp)()\n                ];\n            }\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = swr_internal__WEBPACK_IMPORTED_MODULE_2__.UNDEFINED;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isFunction)(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](swr_internal__WEBPACK_IMPORTED_MODULE_2__.revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const boundMutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((...args)=>{\n        return (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.internalMutate)(cache, keyRef.current, ...args);\n    }, []);\n    // The logic for updating refs.\n    (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(swr_internal__WEBPACK_IMPORTED_MODULE_2__.UNDEFINED, WITH_DEDUPE);\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        let nextFocusRevalidatedAt = 0;\n        const onRevalidate = (type, opts = {})=>{\n            if (type == swr_internal__WEBPACK_IMPORTED_MODULE_2__.revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == swr_internal__WEBPACK_IMPORTED_MODULE_2__.revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == swr_internal__WEBPACK_IMPORTED_MODULE_2__.revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == swr_internal__WEBPACK_IMPORTED_MODULE_2__.revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.subscribeCallback)(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(data) || swr_internal__WEBPACK_IMPORTED_MODULE_2__.IS_SERVER) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.rAF)(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isFunction)(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any initial data. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!swr_internal__WEBPACK_IMPORTED_MODULE_2__.IS_REACT_LEGACY && swr_internal__WEBPACK_IMPORTED_MODULE_2__.IS_SERVER) {\n            throw new Error(\"Fallback data is required when using suspense in SSR.\");\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(returnedData)) {\n                promise.status = \"fulfilled\";\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    return {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n};\nconst SWRConfig = swr_internal__WEBPACK_IMPORTED_MODULE_2__.OBJECT.defineProperty(swr_internal__WEBPACK_IMPORTED_MODULE_2__.SWRConfig, \"defaultValue\", {\n    value: swr_internal__WEBPACK_IMPORTED_MODULE_2__.defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (!data) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.withArgs)(useSWRHandler);\n// useSWR\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/core/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/swr/infinite/dist/index.mjs":
/*!**************************************************!*\
  !*** ./node_modules/swr/infinite/dist/index.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSWRInfinite),\n/* harmony export */   infinite: () => (/* binding */ infinite),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/swr/core/dist/index.mjs\");\n/* harmony import */ var swr_internal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swr/_internal */ \"(ssr)/./node_modules/swr/_internal/dist/index.mjs\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/use-sync-external-store/shim/index.js\");\n\n\n\n\nconst INFINITE_PREFIX = \"$inf$\";\nconst getFirstPageKey = (getKey)=>{\n    return (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.serialize)(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = (getKey)=>{\n    return INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n// We have to several type castings here because `useSWRInfinite` is a special\n// const INFINITE_PREFIX = '$inf$'\nconst EMPTY_PROMISE = Promise.resolve();\n// export const unstable_serialize = (getKey: SWRInfiniteKeyLoader) => {\n//   return INFINITE_PREFIX + getFirstPageKey(getKey)\n// }\nconst infinite = (useSWRNext)=>(getKey, fn, config)=>{\n        const didMountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n        const { cache, initialSize = 1, revalidateAll = false, persistSize = false, revalidateFirstPage = true, revalidateOnMount = false, parallel = false } = config;\n        // The serialized key of the first page. This key will be used to store\n        // metadata of this SWR infinite hook.\n        let infiniteKey;\n        try {\n            infiniteKey = getFirstPageKey(getKey);\n            if (infiniteKey) infiniteKey = INFINITE_PREFIX + infiniteKey;\n        } catch (err) {\n        // Not ready yet.\n        }\n        const [get, set, subscribeCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.createCacheHelper)(cache, infiniteKey);\n        const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const size = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(get()._l) ? initialSize : get()._l;\n            return size;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            cache,\n            infiniteKey,\n            initialSize\n        ]);\n        (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_3__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>{\n            if (infiniteKey) return subscribeCache(infiniteKey, ()=>{\n                callback();\n            });\n            return ()=>{};\n        }, [\n            cache,\n            infiniteKey\n        ]), getSnapshot, getSnapshot);\n        const resolvePageSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n            const cachedPageSize = get()._l;\n            return (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(cachedPageSize) ? initialSize : cachedPageSize;\n        // `cache` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            initialSize\n        ]);\n        // keep the last page size to restore it with the persistSize option\n        const lastPageSizeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(resolvePageSize());\n        // When the page key changes, we reset the page size if it's not persisted\n        (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(()=>{\n            if (!didMountRef.current) {\n                didMountRef.current = true;\n                return;\n            }\n            if (infiniteKey) {\n                // If the key has been changed, we keep the current page size if persistSize is enabled\n                // Otherwise, we reset the page size to cached pageSize\n                set({\n                    _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n                });\n            }\n        // `initialSize` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            cache\n        ]);\n        // Needs to check didMountRef during mounting, not in the fetcher\n        const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n        // Actual SWR hook to load all pages in one fetcher.\n        const swr = useSWRNext(infiniteKey, async (key)=>{\n            // get the revalidate context\n            const forceRevalidateAll = get()._i;\n            // return an array of page data\n            const data = [];\n            const pageSize = resolvePageSize();\n            const [getCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.createCacheHelper)(cache, key);\n            const cacheData = getCache().data;\n            const revalidators = [];\n            let previousPageData = null;\n            for(let i = 0; i < pageSize; ++i){\n                const [pageKey, pageArg] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.serialize)(getKey(i, parallel ? null : previousPageData));\n                if (!pageKey) {\n                    break;\n                }\n                const [getSWRCache, setSWRCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.createCacheHelper)(cache, pageKey);\n                // Get the cached page data.\n                let pageData = getSWRCache().data;\n                // should fetch (or revalidate) if:\n                // - `revalidateAll` is enabled\n                // - `mutate()` called\n                // - the cache is missing\n                // - it's the first page and it's not the initial render\n                // - `revalidateOnMount` is enabled and it's on mount\n                // - cache for that page has changed\n                const shouldFetchPage = revalidateAll || forceRevalidateAll || (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(pageData) || revalidateFirstPage && !i && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(cacheData) || shouldRevalidateOnMount || cacheData && !(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(cacheData[i]) && !config.compare(cacheData[i], pageData);\n                if (fn && shouldFetchPage) {\n                    const revalidate = async ()=>{\n                        pageData = await fn(pageArg);\n                        setSWRCache({\n                            data: pageData,\n                            _k: pageArg\n                        });\n                        data[i] = pageData;\n                    };\n                    if (parallel) {\n                        revalidators.push(revalidate);\n                    } else {\n                        await revalidate();\n                    }\n                } else {\n                    data[i] = pageData;\n                }\n                if (!parallel) {\n                    previousPageData = pageData;\n                }\n            }\n            // flush all revalidateions in parallel\n            if (parallel) {\n                await Promise.all(revalidators.map((r)=>r()));\n            }\n            // once we executed the data fetching based on the context, clear the context\n            set({\n                _i: swr_internal__WEBPACK_IMPORTED_MODULE_2__.UNDEFINED\n            });\n            // return the data\n            return data;\n        }, config);\n        const mutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(data, opts) {\n            // When passing as a boolean, it's explicitly used to disable/enable\n            // revalidation.\n            const options = typeof opts === \"boolean\" ? {\n                revalidate: opts\n            } : opts || {};\n            // Default to true.\n            const shouldRevalidate = options.revalidate !== false;\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            if (shouldRevalidate) {\n                if (!(0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(data)) {\n                    // We only revalidate the pages that are changed\n                    set({\n                        _i: false\n                    });\n                } else {\n                    // Calling `mutate()`, we revalidate all pages\n                    set({\n                        _i: true\n                    });\n                }\n            }\n            return arguments.length ? swr.mutate(data, {\n                ...options,\n                revalidate: shouldRevalidate\n            }) : swr.mutate();\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache\n        ]);\n        // Extend the SWR API\n        const setSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((arg)=>{\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            const [, changeSize] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.createCacheHelper)(cache, infiniteKey);\n            let size;\n            if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isFunction)(arg)) {\n                size = arg(resolvePageSize());\n            } else if (typeof arg == \"number\") {\n                size = arg;\n            }\n            if (typeof size != \"number\") return EMPTY_PROMISE;\n            changeSize({\n                _l: size\n            });\n            lastPageSizeRef.current = size;\n            // Calculate the page data after the size change.\n            const data = [];\n            const [getInfiniteCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.createCacheHelper)(cache, infiniteKey);\n            let previousPageData = null;\n            for(let i = 0; i < size; ++i){\n                const [pageKey] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.serialize)(getKey(i, previousPageData));\n                const [getCache] = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.createCacheHelper)(cache, pageKey);\n                // Get the cached page data.\n                const pageData = pageKey ? getCache().data : swr_internal__WEBPACK_IMPORTED_MODULE_2__.UNDEFINED;\n                // Call `mutate` with infinte cache data if we can't get it from the page cache.\n                if ((0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.isUndefined)(pageData)) {\n                    return mutate(getInfiniteCache().data);\n                }\n                data.push(pageData);\n                previousPageData = pageData;\n            }\n            return mutate(data);\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache,\n            mutate,\n            resolvePageSize\n        ]);\n        // Use getter functions to avoid unnecessary re-renders caused by triggering\n        // all the getters of the returned swr object.\n        return {\n            size: resolvePageSize(),\n            setSize,\n            mutate,\n            get data () {\n                return swr.data;\n            },\n            get error () {\n                return swr.error;\n            },\n            get isValidating () {\n                return swr.isValidating;\n            },\n            get isLoading () {\n                return swr.isLoading;\n            }\n        };\n    };\nconst useSWRInfinite = (0,swr_internal__WEBPACK_IMPORTED_MODULE_2__.withMiddleware)(swr__WEBPACK_IMPORTED_MODULE_1__[\"default\"], infinite);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/swr/infinite/dist/index.mjs\n");

/***/ })

};
;