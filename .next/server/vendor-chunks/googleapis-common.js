"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/googleapis-common";
exports.ids = ["vendor-chunks/googleapis-common"];
exports.modules = {

/***/ "(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/apiIndex.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getAPI = void 0;\nfunction getAPI(api, options, // eslint-disable-next-line @typescript-eslint/no-explicit-any\nversions, context) {\n    let version;\n    if (typeof options === \"string\") {\n        version = options;\n        options = {};\n    } else if (typeof options === \"object\") {\n        version = options.version;\n        delete options.version;\n    } else {\n        throw new Error(\"Argument error: Accepts only string or object\");\n    }\n    try {\n        const ctr = versions[version];\n        const ep = new ctr(options, context);\n        return Object.freeze(ep);\n    } catch (e) {\n        throw new Error(`Unable to load endpoint ${api}(\"${version}\"): ${e.message}`);\n    }\n}\nexports.getAPI = getAPI; //# sourceMappingURL=apiIndex.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/apirequest.js":
/*!****************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/apirequest.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.createAPIRequest = void 0;\nconst google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nconst qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/qs/lib/index.js\");\nconst stream = __webpack_require__(/*! stream */ \"stream\");\nconst urlTemplate = __webpack_require__(/*! url-template */ \"(rsc)/./node_modules/url-template/lib/url-template.js\");\nconst uuid = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm-node/index.js\");\nconst extend = __webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\");\nconst isbrowser_1 = __webpack_require__(/*! ./isbrowser */ \"(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js\");\nconst h2 = __webpack_require__(/*! ./http2 */ \"(rsc)/./node_modules/googleapis-common/build/src/http2.js\");\n// eslint-disable-next-line @typescript-eslint/no-var-requires\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/googleapis-common/package.json\");\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction isReadableStream(obj) {\n    return obj !== null && typeof obj === \"object\" && typeof obj.pipe === \"function\" && obj.readable !== false && typeof obj._read === \"function\" && typeof obj._readableState === \"object\";\n}\nfunction getMissingParams(params, required) {\n    const missing = new Array();\n    required.forEach((param)=>{\n        // Is the required param in the params object?\n        if (params[param] === undefined) {\n            missing.push(param);\n        }\n    });\n    // If there are any required params missing, return their names in array,\n    // otherwise return null\n    return missing.length > 0 ? missing : null;\n}\nfunction createAPIRequest(parameters, callback) {\n    if (callback) {\n        createAPIRequestAsync(parameters).then((r)=>callback(null, r), callback);\n    } else {\n        return createAPIRequestAsync(parameters);\n    }\n}\nexports.createAPIRequest = createAPIRequest;\nasync function createAPIRequestAsync(parameters) {\n    var _a, _b, _c, _d;\n    // Combine the GaxiosOptions options passed with this specific\n    // API call with the global options configured at the API Context\n    // level, or at the global level.\n    const options = extend(true, {}, ((_a = parameters.context.google) === null || _a === void 0 ? void 0 : _a._options) || {}, parameters.context._options || {}, parameters.options // API call params\n    );\n    const params = extend(true, {}, options.params, parameters.params // API call params\n    );\n    options.userAgentDirectives = options.userAgentDirectives || [];\n    const media = params.media || {};\n    /**\n     * In a previous version of this API, the request body was stuffed in a field\n     * named `resource`.  This caused lots of problems, because it's not uncommon\n     * to have an actual named parameter required which is also named `resource`.\n     * This meant that users would have to use `resource_` in those cases, which\n     * pretty much nobody figures out on their own. The request body is now\n     * documented as being in the `requestBody` property, but we also need to keep\n     * using `resource` for reasons of back-compat. Cases that need to be covered\n     * here:\n     * - user provides just a `resource` with a request body\n     * - user provides both a `resource` and a `resource_`\n     * - user provides just a `requestBody`\n     * - user provides both a `requestBody` and a `resource`\n     */ let resource = params.requestBody;\n    if (!params.requestBody && params.resource && (!parameters.requiredParams.includes(\"resource\") || typeof params.resource !== \"string\")) {\n        resource = params.resource;\n        delete params.resource;\n    }\n    delete params.requestBody;\n    let authClient = params.auth || options.auth;\n    const defaultMime = typeof media.body === \"string\" ? \"text/plain\" : \"application/octet-stream\";\n    delete params.media;\n    delete params.auth;\n    // Grab headers from user provided options\n    const headers = params.headers || {};\n    populateAPIHeader(headers, options.apiVersion);\n    delete params.headers;\n    // Un-alias parameters that were modified due to conflicts with reserved names\n    Object.keys(params).forEach((key)=>{\n        if (key.slice(-1) === \"_\") {\n            const newKey = key.slice(0, -1);\n            params[newKey] = params[key];\n            delete params[key];\n        }\n    });\n    // Check for missing required parameters in the API request\n    const missingParams = getMissingParams(params, parameters.requiredParams);\n    if (missingParams) {\n        // Some params are missing - stop further operations and inform the\n        // developer which required params are not included in the request\n        throw new Error(\"Missing required parameters: \" + missingParams.join(\", \"));\n    }\n    // Parse urls\n    if (options.url) {\n        let url = options.url;\n        if (typeof url === \"object\") {\n            url = url.toString();\n        }\n        options.url = urlTemplate.parse(url).expand(params);\n    }\n    if (parameters.mediaUrl) {\n        parameters.mediaUrl = urlTemplate.parse(parameters.mediaUrl).expand(params);\n    }\n    // Rewrite url if rootUrl is globally set\n    if (parameters.context._options.rootUrl !== undefined && options.url !== undefined) {\n        const originalUrl = new URL(options.url);\n        const path = originalUrl.href.substr(originalUrl.origin.length);\n        options.url = new URL(path, parameters.context._options.rootUrl).href;\n    }\n    // When forming the querystring, override the serializer so that array\n    // values are serialized like this:\n    // myParams: ['one', 'two'] ---> 'myParams=one&myParams=two'\n    // This serializer also encodes spaces in the querystring as `%20`,\n    // whereas the default serializer in gaxios encodes to a `+`.\n    options.paramsSerializer = (params)=>{\n        return qs.stringify(params, {\n            arrayFormat: \"repeat\"\n        });\n    };\n    // delete path params from the params object so they do not end up in query\n    parameters.pathParams.forEach((param)=>delete params[param]);\n    // if authClient is actually a string, use it as an API KEY\n    if (typeof authClient === \"string\") {\n        params.key = params.key || authClient;\n        authClient = undefined;\n    }\n    function multipartUpload(multipart) {\n        const boundary = uuid.v4();\n        const finale = `--${boundary}--`;\n        const rStream = new stream.PassThrough({\n            flush (callback) {\n                this.push(\"\\r\\n\");\n                this.push(finale);\n                callback();\n            }\n        });\n        const pStream = new ProgressStream();\n        const isStream = isReadableStream(multipart[1].body);\n        headers[\"content-type\"] = `multipart/related; boundary=${boundary}`;\n        for (const part of multipart){\n            const preamble = `--${boundary}\\r\\ncontent-type: ${part[\"content-type\"]}\\r\\n\\r\\n`;\n            rStream.push(preamble);\n            if (typeof part.body === \"string\") {\n                rStream.push(part.body);\n                rStream.push(\"\\r\\n\");\n            } else {\n                // Gaxios does not natively support onUploadProgress in node.js.\n                // Pipe through the pStream first to read the number of bytes read\n                // for the purpose of tracking progress.\n                pStream.on(\"progress\", (bytesRead)=>{\n                    if (options.onUploadProgress) {\n                        options.onUploadProgress({\n                            bytesRead\n                        });\n                    }\n                });\n                part.body.pipe(pStream).pipe(rStream);\n            }\n        }\n        if (!isStream) {\n            rStream.push(finale);\n            rStream.push(null);\n        }\n        options.data = rStream;\n    }\n    function browserMultipartUpload(multipart) {\n        const boundary = uuid.v4();\n        const finale = `--${boundary}--`;\n        headers[\"content-type\"] = `multipart/related; boundary=${boundary}`;\n        let content = \"\";\n        for (const part of multipart){\n            const preamble = `--${boundary}\\r\\ncontent-type: ${part[\"content-type\"]}\\r\\n\\r\\n`;\n            content += preamble;\n            if (typeof part.body === \"string\") {\n                content += part.body;\n                content += \"\\r\\n\";\n            }\n        }\n        content += finale;\n        options.data = content;\n    }\n    if (parameters.mediaUrl && media.body) {\n        options.url = parameters.mediaUrl;\n        if (resource) {\n            params.uploadType = \"multipart\";\n            const multipart = [\n                {\n                    \"content-type\": \"application/json\",\n                    body: JSON.stringify(resource)\n                },\n                {\n                    \"content-type\": media.mimeType || resource && resource.mimeType || defaultMime,\n                    body: media.body\n                }\n            ];\n            if (!(0, isbrowser_1.isBrowser)()) {\n                // gaxios doesn't support multipart/related uploads, so it has to\n                // be implemented here.\n                multipartUpload(multipart);\n            } else {\n                browserMultipartUpload(multipart);\n            }\n        } else {\n            params.uploadType = \"media\";\n            Object.assign(headers, {\n                \"content-type\": media.mimeType || defaultMime\n            });\n            options.data = media.body;\n        }\n    } else {\n        options.data = resource || undefined;\n    }\n    options.headers = extend(true, options.headers || {}, headers);\n    options.params = params;\n    if (!(0, isbrowser_1.isBrowser)()) {\n        options.headers[\"Accept-Encoding\"] = \"gzip\";\n        options.userAgentDirectives.push({\n            product: \"google-api-nodejs-client\",\n            version: pkg.version,\n            comment: \"gzip\"\n        });\n        const userAgent = options.userAgentDirectives.map((d)=>{\n            let line = `${d.product}/${d.version}`;\n            if (d.comment) {\n                line += ` (${d.comment})`;\n            }\n            return line;\n        }).join(\" \");\n        options.headers[\"User-Agent\"] = userAgent;\n    }\n    // By default gaxios treats any 2xx as valid, and all non 2xx status\n    // codes as errors.  This is a problem for HTTP 304s when used along\n    // with an eTag.\n    if (!options.validateStatus) {\n        options.validateStatus = (status)=>{\n            return status >= 200 && status < 300 || status === 304;\n        };\n    }\n    // Retry by default\n    options.retry = options.retry === undefined ? true : options.retry;\n    delete options.auth; // is overridden by our auth code\n    // Determine TPC universe\n    if (options.universeDomain && options.universe_domain && options.universeDomain !== options.universe_domain) {\n        throw new Error(\"Please set either universe_domain or universeDomain, but not both.\");\n    }\n    const universeDomainEnvVar = typeof process === \"object\" && typeof process.env === \"object\" ? process.env[\"GOOGLE_CLOUD_UNIVERSE_DOMAIN\"] : undefined;\n    const universeDomain = (_d = (_c = (_b = options.universeDomain) !== null && _b !== void 0 ? _b : options.universe_domain) !== null && _c !== void 0 ? _c : universeDomainEnvVar) !== null && _d !== void 0 ? _d : \"googleapis.com\";\n    // Update URL to point to the given TPC universe\n    if (universeDomain !== \"googleapis.com\" && options.url) {\n        const url = new URL(options.url);\n        if (url.hostname.endsWith(\".googleapis.com\")) {\n            url.hostname = url.hostname.replace(/googleapis\\.com$/, universeDomain);\n            options.url = url.toString();\n        }\n    }\n    // Perform the HTTP request.  NOTE: this function used to return a\n    // mikeal/request object. Since the transition to Axios, the method is\n    // now void.  This may be a source of confusion for users upgrading from\n    // version 24.0 -> 25.0 or up.\n    if (authClient && typeof authClient === \"object\") {\n        // Validate TPC universe\n        const universeFromAuth = typeof authClient.getUniverseDomain === \"function\" ? await authClient.getUniverseDomain() : undefined;\n        if (universeFromAuth && universeDomain !== universeFromAuth) {\n            throw new Error(`The configured universe domain (${universeDomain}) does not match the universe domain found in the credentials (${universeFromAuth}). ` + \"If you haven't configured the universe domain explicitly, googleapis.com is the default.\");\n        }\n        if (options.http2) {\n            const authHeaders = await authClient.getRequestHeaders(options.url);\n            const mooOpts = Object.assign({}, options);\n            mooOpts.headers = Object.assign(mooOpts.headers, authHeaders);\n            return h2.request(mooOpts);\n        } else {\n            return authClient.request(options);\n        }\n    } else {\n        return new google_auth_library_1.DefaultTransporter().request(options);\n    }\n}\n/**\n * Basic Passthrough Stream that records the number of bytes read\n * every time the cursor is moved.\n */ class ProgressStream extends stream.Transform {\n    constructor(){\n        super(...arguments);\n        this.bytesRead = 0;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    _transform(chunk, encoding, callback) {\n        this.bytesRead += chunk.length;\n        this.emit(\"progress\", this.bytesRead);\n        this.push(chunk);\n        callback();\n    }\n}\nfunction populateAPIHeader(headers, apiVersion) {\n    // TODO: we should eventually think about adding browser support for this\n    // populating the gl-web header (web support should also be added to\n    // google-auth-library-nodejs).\n    if (!(0, isbrowser_1.isBrowser)()) {\n        headers[\"x-goog-api-client\"] = `gdcl/${pkg.version} gl-node/${process.versions.node}`;\n    }\n    if (apiVersion) {\n        headers[\"x-goog-api-version\"] = apiVersion;\n    }\n} //# sourceMappingURL=apirequest.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/authplus.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/authplus.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.AuthPlus = void 0;\nconst google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nclass AuthPlus extends google_auth_library_1.GoogleAuth {\n    constructor(){\n        super(...arguments);\n        this.JWT = google_auth_library_1.JWT;\n        this.Compute = google_auth_library_1.Compute;\n        this.OAuth2 = google_auth_library_1.OAuth2Client;\n        this.GoogleAuth = google_auth_library_1.GoogleAuth;\n        this.AwsClient = google_auth_library_1.AwsClient;\n        this.IdentityPoolClient = google_auth_library_1.IdentityPoolClient;\n        this.ExternalAccountClient = google_auth_library_1.ExternalAccountClient;\n    }\n    /**\n     * Override getClient(), memoizing an instance of auth for\n     * subsequent calls to getProjectId().\n     */ async getClient(options) {\n        this._cachedAuth = new google_auth_library_1.GoogleAuth(options);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return this._cachedAuth.getClient();\n    }\n    getProjectId(callback) {\n        if (callback) {\n            return this._cachedAuth ? this._cachedAuth.getProjectId(callback) : super.getProjectId(callback);\n        } else {\n            return this._cachedAuth ? this._cachedAuth.getProjectId() : super.getProjectId();\n        }\n    }\n}\nexports.AuthPlus = AuthPlus; //# sourceMappingURL=authplus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/authplus.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/discovery.js":
/*!***************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/discovery.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Discovery = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nconst resolve = __webpack_require__(/*! url */ \"url\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\");\nconst readFile = util.promisify(fs.readFile);\nclass Discovery {\n    /**\n     * Discovery for discovering API endpoints\n     *\n     * @param options Options for discovery\n     */ constructor(options){\n        this.transporter = new google_auth_library_1.DefaultTransporter();\n        this.options = options || {};\n    }\n    /**\n     * Generate and Endpoint from an endpoint schema object.\n     *\n     * @param schema The schema from which to generate the Endpoint.\n     * @return A function that creates an endpoint.\n     */ makeEndpoint(schema) {\n        return (options)=>{\n            const ep = new endpoint_1.Endpoint(options);\n            ep.applySchema(ep, schema, schema, ep);\n            return ep;\n        };\n    }\n    /**\n     * Log output of generator. Works just like console.log\n     */ log(...args) {\n        if (this.options && this.options.debug) {\n            console.log(...args);\n        }\n    }\n    /**\n     * Generate all APIs and return as in-memory object.\n     * @param discoveryUrl\n     */ async discoverAllAPIs(discoveryUrl) {\n        const headers = this.options.includePrivate ? {} : {\n            \"X-User-Ip\": \"0.0.0.0\"\n        };\n        const res = await this.transporter.request({\n            url: discoveryUrl,\n            headers\n        });\n        const items = res.data.items;\n        const apis = await Promise.all(items.map(async (api)=>{\n            const endpointCreator = await this.discoverAPI(api.discoveryRestUrl);\n            return {\n                api,\n                endpointCreator\n            };\n        }));\n        const versionIndex = {};\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const apisIndex = {};\n        for (const set of apis){\n            if (!apisIndex[set.api.name]) {\n                versionIndex[set.api.name] = {};\n                apisIndex[set.api.name] = (options)=>{\n                    const type = typeof options;\n                    let version;\n                    if (type === \"string\") {\n                        version = options;\n                        options = {};\n                    } else if (type === \"object\") {\n                        version = options.version;\n                        delete options.version;\n                    } else {\n                        throw new Error(\"Argument error: Accepts only string or object\");\n                    }\n                    try {\n                        const ep = set.endpointCreator(options, this);\n                        return Object.freeze(ep); // create new & freeze\n                    } catch (e) {\n                        throw new Error(util.format('Unable to load endpoint %s(\"%s\"): %s', set.api.name, version, e.message));\n                    }\n                };\n            }\n            versionIndex[set.api.name][set.api.version] = set.endpointCreator;\n        }\n        return apisIndex;\n    }\n    /**\n     * Generate API file given discovery URL\n     *\n     * @param apiDiscoveryUrl URL or filename of discovery doc for API\n     * @returns A promise that resolves with a function that creates the endpoint\n     */ async discoverAPI(apiDiscoveryUrl) {\n        if (typeof apiDiscoveryUrl === \"string\") {\n            const parts = resolve.parse(apiDiscoveryUrl);\n            if (apiDiscoveryUrl && !parts.protocol) {\n                this.log(\"Reading from file \" + apiDiscoveryUrl);\n                const file = await readFile(apiDiscoveryUrl, {\n                    encoding: \"utf8\"\n                });\n                return this.makeEndpoint(JSON.parse(file));\n            } else {\n                this.log(\"Requesting \" + apiDiscoveryUrl);\n                const res = await this.transporter.request({\n                    url: apiDiscoveryUrl\n                });\n                return this.makeEndpoint(res.data);\n            }\n        } else {\n            const options = apiDiscoveryUrl;\n            this.log(\"Requesting \" + options.url);\n            const url = options.url;\n            delete options.url;\n            const parameters = {\n                options: {\n                    url,\n                    method: \"GET\"\n                },\n                requiredParams: [],\n                pathParams: [],\n                params: options,\n                context: {\n                    google: {\n                        _options: {}\n                    },\n                    _options: {}\n                }\n            };\n            const res = await (0, apirequest_1.createAPIRequest)(parameters);\n            return this.makeEndpoint(res.data);\n        }\n    }\n}\nexports.Discovery = Discovery; //# sourceMappingURL=discovery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/discovery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/endpoint.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/endpoint.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Endpoint = void 0;\nconst apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nclass Endpoint {\n    constructor(options){\n        this._options = options || {};\n    }\n    /**\n     * Given a schema, add methods and resources to a target.\n     *\n     * @param {object} target The target to which to apply the schema.\n     * @param {object} rootSchema The top-level schema, so we don't lose track of it\n     * during recursion.\n     * @param {object} schema The current schema from which to extract methods and\n     * resources.\n     * @param {object} context The context to add to each method.\n     */ applySchema(target, rootSchema, schema, context) {\n        this.applyMethodsFromSchema(target, rootSchema, schema, context);\n        if (schema.resources) {\n            for(const resourceName in schema.resources){\n                if (Object.prototype.hasOwnProperty.call(schema.resources, resourceName)) {\n                    const resource = schema.resources[resourceName];\n                    if (!target[resourceName]) {\n                        target[resourceName] = {};\n                    }\n                    this.applySchema(target[resourceName], rootSchema, resource, context);\n                }\n            }\n        }\n    }\n    /**\n     * Given a schema, add methods to a target.\n     *\n     * @param {object} target The target to which to apply the methods.\n     * @param {object} rootSchema The top-level schema, so we don't lose track of it\n     * during recursion.\n     * @param {object} schema The current schema from which to extract methods.\n     * @param {object} context The context to add to each method.\n     */ applyMethodsFromSchema(target, rootSchema, schema, context) {\n        if (schema.methods) {\n            for(const name in schema.methods){\n                if (Object.prototype.hasOwnProperty.call(schema.methods, name)) {\n                    const method = schema.methods[name];\n                    target[name] = this.makeMethod(rootSchema, method, context);\n                }\n            }\n        }\n    }\n    /**\n     * Given a method schema, add a method to a target.\n     *\n     * @param target The target to which to add the method.\n     * @param schema The top-level schema that contains the rootUrl, etc.\n     * @param method The method schema from which to generate the method.\n     * @param context The context to add to the method.\n     */ makeMethod(schema, method, context) {\n        return (paramsOrCallback, callback)=>{\n            const params = typeof paramsOrCallback === \"function\" ? {} : paramsOrCallback;\n            callback = typeof paramsOrCallback === \"function\" ? paramsOrCallback : callback;\n            const schemaUrl = buildurl(schema.rootUrl + schema.servicePath + method.path);\n            const parameters = {\n                options: {\n                    url: schemaUrl.substring(1, schemaUrl.length - 1),\n                    method: method.httpMethod,\n                    apiVersion: method.apiVersion\n                },\n                params,\n                requiredParams: method.parameterOrder || [],\n                pathParams: this.getPathParams(method.parameters),\n                context\n            };\n            if (method.mediaUpload && method.mediaUpload.protocols && method.mediaUpload.protocols.simple && method.mediaUpload.protocols.simple.path) {\n                const mediaUrl = buildurl(schema.rootUrl + method.mediaUpload.protocols.simple.path);\n                parameters.mediaUrl = mediaUrl.substring(1, mediaUrl.length - 1);\n            }\n            if (!callback) {\n                return (0, apirequest_1.createAPIRequest)(parameters);\n            }\n            (0, apirequest_1.createAPIRequest)(parameters, callback);\n            return;\n        };\n    }\n    getPathParams(params) {\n        const pathParams = new Array();\n        if (typeof params !== \"object\") {\n            params = {};\n        }\n        Object.keys(params).forEach((key)=>{\n            if (params[key].location === \"path\") {\n                pathParams.push(key);\n            }\n        });\n        return pathParams;\n    }\n}\nexports.Endpoint = Endpoint;\n/**\n * Build a string used to create a URL from the discovery doc provided URL.\n * replace double slashes with single slash (except in https://)\n * @private\n * @param  input URL to build from\n * @return Resulting built URL\n */ function buildurl(input) {\n    return input ? `'${input}'`.replace(/([^:]\\/)\\/+/g, \"$1\") : \"\";\n} //# sourceMappingURL=endpoint.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/http2.js":
/*!***********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/http2.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.closeSession = exports.request = exports.sessions = void 0;\nconst http2 = __webpack_require__(/*! http2 */ \"http2\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/qs/lib/index.js\");\nconst extend = __webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst process = __webpack_require__(/*! process */ \"process\");\nconst common_1 = __webpack_require__(/*! gaxios/build/src/common */ \"(rsc)/./node_modules/gaxios/build/src/common.js\");\nconst { HTTP2_HEADER_CONTENT_ENCODING, HTTP2_HEADER_CONTENT_TYPE, HTTP2_HEADER_METHOD, HTTP2_HEADER_PATH, HTTP2_HEADER_STATUS } = http2.constants;\nconst DEBUG = !!process.env.HTTP2_DEBUG;\n/**\n * List of sessions current in use.\n * @private\n */ exports.sessions = {};\n/**\n * Public method to make an http2 request.\n * @param config - Request options.\n */ async function request(config) {\n    const opts = extend(true, {}, config);\n    opts.validateStatus = opts.validateStatus || validateStatus;\n    opts.responseType = opts.responseType || \"json\";\n    const url = new url_1.URL(opts.url);\n    // Check for an existing session to this host, or go create a new one.\n    const sessionData = _getClient(url.host);\n    // Since we're using this session, clear the timeout handle to ensure\n    // it stays in memory and connected for a while further.\n    if (sessionData.timeoutHandle !== undefined) {\n        clearTimeout(sessionData.timeoutHandle);\n    }\n    // Assemble the querystring based on config.params.  We're using the\n    // `qs` module to make life a little easier.\n    let pathWithQs = url.pathname;\n    if (config.params && Object.keys(config.params).length > 0) {\n        const serializer = config.paramsSerializer || qs.stringify;\n        const q = serializer(opts.params);\n        pathWithQs += `?${q}`;\n    }\n    // Assemble the headers based on basic HTTP2 primitives (path, method) and\n    // custom headers sent from the consumer.  Note: I am using `Object.assign`\n    // here making the assumption these objects are not deep.  If it turns out\n    // they are, we may need to use the `extend` npm module for deep cloning.\n    const headers = Object.assign({}, opts.headers, {\n        [HTTP2_HEADER_PATH]: pathWithQs,\n        [HTTP2_HEADER_METHOD]: config.method || \"GET\"\n    });\n    // NOTE: This is working around an upstream bug in `apirequest.ts`. The\n    // request path assumes that the `content-type` header is going to be set in\n    // the underlying HTTP Client. This hack provides bug for bug compatability\n    // with this bug in gaxios:\n    // https://github.com/googleapis/gaxios/blob/main/src/gaxios.ts#L202\n    if (!headers[HTTP2_HEADER_CONTENT_TYPE]) {\n        if (opts.responseType !== \"text\") {\n            headers[HTTP2_HEADER_CONTENT_TYPE] = \"application/json\";\n        }\n    }\n    const res = {\n        config,\n        request: {},\n        headers: [],\n        status: 0,\n        data: {},\n        statusText: \"\"\n    };\n    const chunks = [];\n    const session = sessionData.session;\n    let req;\n    return new Promise((resolve, reject)=>{\n        try {\n            req = session.request(headers).on(\"response\", (headers)=>{\n                res.headers = headers;\n                res.status = Number(headers[HTTP2_HEADER_STATUS]);\n                let stream = req;\n                if (headers[HTTP2_HEADER_CONTENT_ENCODING] === \"gzip\") {\n                    stream = req.pipe(zlib.createGunzip());\n                }\n                if (opts.responseType === \"stream\") {\n                    res.data = stream;\n                    resolve(res);\n                    return;\n                }\n                stream.on(\"data\", (d)=>{\n                    chunks.push(d);\n                }).on(\"error\", (err)=>{\n                    reject(err);\n                    return;\n                }).on(\"end\", ()=>{\n                    const buf = Buffer.concat(chunks);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    let data = buf;\n                    if (buf) {\n                        if (opts.responseType === \"json\") {\n                            try {\n                                data = JSON.parse(buf.toString(\"utf8\"));\n                            } catch (_a) {\n                                data = buf.toString(\"utf8\");\n                            }\n                        } else if (opts.responseType === \"text\") {\n                            data = buf.toString(\"utf8\");\n                        } else if (opts.responseType === \"arraybuffer\") {\n                            data = buf.buffer;\n                        }\n                        res.data = data;\n                    }\n                    if (!opts.validateStatus(res.status)) {\n                        let message = `Request failed with status code ${res.status}. `;\n                        if (res.data && typeof res.data === \"object\") {\n                            const body = util.inspect(res.data, {\n                                depth: 5\n                            });\n                            message = `${message}\\n'${body}`;\n                        }\n                        reject(new common_1.GaxiosError(message, opts, res));\n                    }\n                    resolve(res);\n                    return;\n                });\n            }).on(\"error\", (e)=>{\n                reject(e);\n                return;\n            });\n        } catch (e) {\n            closeSession(url);\n            reject(e);\n        }\n        res.request = req;\n        // If data was provided, write it to the request in the form of\n        // a stream, string data, or a basic object.\n        if (config.data) {\n            if (config.data instanceof stream_1.Stream) {\n                config.data.pipe(req);\n            } else if (typeof config.data === \"string\") {\n                const data = Buffer.from(config.data);\n                req.end(data);\n            } else if (typeof config.data === \"object\") {\n                const data = JSON.stringify(config.data);\n                req.end(data);\n            }\n        }\n        // Create a timeout so the Http2Session will be cleaned up after\n        // a period of non-use. 500 milliseconds was chosen because it's\n        // a nice round number, and I don't know what would be a better\n        // choice. Keeping this channel open will hold a file descriptor\n        // which will prevent the process from exiting.\n        sessionData.timeoutHandle = setTimeout(()=>{\n            closeSession(url);\n        }, 500);\n    });\n}\nexports.request = request;\n/**\n * By default, throw for any non-2xx status code\n * @param status - status code from the HTTP response\n */ function validateStatus(status) {\n    return status >= 200 && status < 300;\n}\n/**\n * Obtain an existing h2 session or go create a new one.\n * @param host - The hostname to which the session belongs.\n */ function _getClient(host) {\n    if (!exports.sessions[host]) {\n        if (DEBUG) {\n            console.log(`Creating client for ${host}`);\n        }\n        const session = http2.connect(`https://${host}`);\n        session.on(\"error\", (e)=>{\n            console.error(`*ERROR*: ${e}`);\n            delete exports.sessions[host];\n        }).on(\"goaway\", (errorCode, lastStreamId)=>{\n            console.error(`*GOAWAY*: ${errorCode} : ${lastStreamId}`);\n            delete exports.sessions[host];\n        });\n        exports.sessions[host] = {\n            session\n        };\n    } else {\n        if (DEBUG) {\n            console.log(`Used cached client for ${host}`);\n        }\n    }\n    return exports.sessions[host];\n}\nasync function closeSession(url) {\n    const sessionData = exports.sessions[url.host];\n    if (!sessionData) {\n        return;\n    }\n    const { session } = sessionData;\n    delete exports.sessions[url.host];\n    if (DEBUG) {\n        console.error(`Closing ${url.host}`);\n    }\n    session.close(()=>{\n        if (DEBUG) {\n            console.error(`Closed ${url.host}`);\n        }\n    });\n    setTimeout(()=>{\n        if (session && !session.destroyed) {\n            if (DEBUG) {\n                console.log(`Forcing close ${url.host}`);\n            }\n            if (session) {\n                session.destroy();\n            }\n        }\n    }, 1000);\n}\nexports.closeSession = closeSession; //# sourceMappingURL=http2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/http2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Endpoint = exports.Discovery = exports.AuthPlus = exports.createAPIRequest = exports.getAPI = exports.GaxiosError = exports.Gaxios = exports.AwsClient = exports.IdentityPoolClient = exports.BaseExternalAccountClient = exports.ExternalAccountClient = exports.GoogleAuth = exports.DefaultTransporter = exports.UserRefreshClient = exports.Compute = exports.JWT = exports.OAuth2Client = void 0;\nvar google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nObject.defineProperty(exports, \"OAuth2Client\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.OAuth2Client;\n    }\n}));\nObject.defineProperty(exports, \"JWT\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.JWT;\n    }\n}));\nObject.defineProperty(exports, \"Compute\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.Compute;\n    }\n}));\nObject.defineProperty(exports, \"UserRefreshClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.UserRefreshClient;\n    }\n}));\nObject.defineProperty(exports, \"DefaultTransporter\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.DefaultTransporter;\n    }\n}));\nObject.defineProperty(exports, \"GoogleAuth\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.GoogleAuth;\n    }\n}));\nObject.defineProperty(exports, \"ExternalAccountClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.ExternalAccountClient;\n    }\n}));\nObject.defineProperty(exports, \"BaseExternalAccountClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.BaseExternalAccountClient;\n    }\n}));\nObject.defineProperty(exports, \"IdentityPoolClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.IdentityPoolClient;\n    }\n}));\nObject.defineProperty(exports, \"AwsClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.AwsClient;\n    }\n}));\nvar gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/src/index.js\");\nObject.defineProperty(exports, \"Gaxios\", ({\n    enumerable: true,\n    get: function() {\n        return gaxios_1.Gaxios;\n    }\n}));\nObject.defineProperty(exports, \"GaxiosError\", ({\n    enumerable: true,\n    get: function() {\n        return gaxios_1.GaxiosError;\n    }\n}));\nvar apiIndex_1 = __webpack_require__(/*! ./apiIndex */ \"(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js\");\nObject.defineProperty(exports, \"getAPI\", ({\n    enumerable: true,\n    get: function() {\n        return apiIndex_1.getAPI;\n    }\n}));\nvar apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nObject.defineProperty(exports, \"createAPIRequest\", ({\n    enumerable: true,\n    get: function() {\n        return apirequest_1.createAPIRequest;\n    }\n}));\nvar authplus_1 = __webpack_require__(/*! ./authplus */ \"(rsc)/./node_modules/googleapis-common/build/src/authplus.js\");\nObject.defineProperty(exports, \"AuthPlus\", ({\n    enumerable: true,\n    get: function() {\n        return authplus_1.AuthPlus;\n    }\n}));\nvar discovery_1 = __webpack_require__(/*! ./discovery */ \"(rsc)/./node_modules/googleapis-common/build/src/discovery.js\");\nObject.defineProperty(exports, \"Discovery\", ({\n    enumerable: true,\n    get: function() {\n        return discovery_1.Discovery;\n    }\n}));\nvar endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\");\nObject.defineProperty(exports, \"Endpoint\", ({\n    enumerable: true,\n    get: function() {\n        return endpoint_1.Endpoint;\n    }\n})); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js":
/*!***************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/isbrowser.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.isBrowser = void 0;\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nexports.isBrowser = isBrowser; //# sourceMappingURL=isbrowser.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ29vZ2xlYXBpcy1jb21tb24vYnVpbGQvc3JjL2lzYnJvd3Nlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDRCQUE0QjtBQUM1QixrRUFBa0U7QUFDbEUsbUVBQW1FO0FBQ25FLDBDQUEwQztBQUMxQyxFQUFFO0FBQ0YsZ0RBQWdEO0FBQ2hELEVBQUU7QUFDRixzRUFBc0U7QUFDdEUsb0VBQW9FO0FBQ3BFLDJFQUEyRTtBQUMzRSxzRUFBc0U7QUFDdEUsaUNBQWlDO0FBQ2pDQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUM3REQsaUJBQWlCLEdBQUcsS0FBSztBQUN6QixTQUFTRTtJQUNMLE9BQU8sZ0JBQWtCO0FBQzdCO0FBQ0FGLGlCQUFpQixHQUFHRSxXQUNwQixxQ0FBcUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jdXN0b20tZ3JvdXAtY3JlYXRvci8uL25vZGVfbW9kdWxlcy9nb29nbGVhcGlzLWNvbW1vbi9idWlsZC9zcmMvaXNicm93c2VyLmpzP2ZlNTMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBDb3B5cmlnaHQgMjAyMCBHb29nbGUgTExDXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuLy8geW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuLy8gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4vL1xuLy8gICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4vL1xuLy8gVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuLy8gZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuLy8gV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4vLyBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4vLyBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuaXNCcm93c2VyID0gdm9pZCAwO1xuZnVuY3Rpb24gaXNCcm93c2VyKCkge1xuICAgIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJztcbn1cbmV4cG9ydHMuaXNCcm93c2VyID0gaXNCcm93c2VyO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aXNicm93c2VyLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImlzQnJvd3NlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/package.json":
/*!*****************************************************!*\
  !*** ./node_modules/googleapis-common/package.json ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = JSON.parse('{"name":"googleapis-common","version":"7.2.0","description":"A common tooling library used by the googleapis npm module. You probably don\'t want to use this directly.","repository":"googleapis/nodejs-googleapis-common","main":"build/src/index.js","types":"build/src/index.d.ts","files":["build/src","!build/src/**/*.map"],"scripts":{"prebenchmark":"npm run compile","benchmark":"node build/benchmark/bench.js","compile":"tsc -p .","test":"c8 mocha build/test","system-test":"c8 mocha build/system-test --timeout 600000","presystem-test":"npm run compile","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","lint":"gts check","samples-test":"mocha build/samples-test","docs":"compodoc src/","docs-test":"linkinator docs","webpack":"webpack","browser-test":"karma start","predocs-test":"npm run docs","prelint":"cd samples; npm link ../; npm install","clean":"gts clean","precompile":"gts clean"},"keywords":[],"author":"Google LLC","license":"Apache-2.0","dependencies":{"extend":"^3.0.2","gaxios":"^6.0.3","google-auth-library":"^9.7.0","qs":"^6.7.0","url-template":"^2.0.8","uuid":"^9.0.0"},"devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@compodoc/compodoc":"1.1.23","@types/execa":"^0.9.0","@types/extend":"^3.0.1","@types/mocha":"^9.0.0","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/nock":"^11.0.0","@types/proxyquire":"^1.3.28","@types/qs":"^6.5.3","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","@types/url-template":"^2.0.28","@types/uuid":"^9.0.0","c8":"^8.0.0","codecov":"^3.5.0","execa":"^5.0.0","gts":"^5.0.0","http2spy":"^2.0.0","is-docker":"^2.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"^4.0.0","linkinator":"^3.1.0","mocha":"^9.2.2","mv":"^2.1.1","ncp":"^2.0.0","nock":"^13.0.0","null-loader":"^4.0.0","proxyquire":"^2.1.3","puppeteer":"^18.2.1","sinon":"^17.0.0","tmp":"^0.2.0","ts-loader":"^8.0.0","typescript":"5.1.6","webpack":"^4.0.0","webpack-cli":"^4.0.0"},"engines":{"node":">=14.0.0"}}');

/***/ })

};
;