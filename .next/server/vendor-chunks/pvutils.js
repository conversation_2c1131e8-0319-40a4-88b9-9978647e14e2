"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pvutils";
exports.ids = ["vendor-chunks/pvutils"];
exports.modules = {

/***/ "(rsc)/./node_modules/pvutils/build/utils.es.js":
/*!************************************************!*\
  !*** ./node_modules/pvutils/build/utils.es.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayBufferToString: () => (/* binding */ arrayBufferToString),\n/* harmony export */   bufferToHexCodes: () => (/* binding */ bufferToHexCodes),\n/* harmony export */   checkBufferParams: () => (/* binding */ checkBufferParams),\n/* harmony export */   clearProps: () => (/* binding */ clearProps),\n/* harmony export */   fromBase64: () => (/* binding */ fromBase64),\n/* harmony export */   getParametersValue: () => (/* binding */ getParametersValue),\n/* harmony export */   getUTCDate: () => (/* binding */ getUTCDate),\n/* harmony export */   isEqualBuffer: () => (/* binding */ isEqualBuffer),\n/* harmony export */   nearestPowerOf2: () => (/* binding */ nearestPowerOf2),\n/* harmony export */   padNumber: () => (/* binding */ padNumber),\n/* harmony export */   stringToArrayBuffer: () => (/* binding */ stringToArrayBuffer),\n/* harmony export */   toBase64: () => (/* binding */ toBase64),\n/* harmony export */   utilConcatBuf: () => (/* binding */ utilConcatBuf),\n/* harmony export */   utilConcatView: () => (/* binding */ utilConcatView),\n/* harmony export */   utilDecodeTC: () => (/* binding */ utilDecodeTC),\n/* harmony export */   utilEncodeTC: () => (/* binding */ utilEncodeTC),\n/* harmony export */   utilFromBase: () => (/* binding */ utilFromBase),\n/* harmony export */   utilToBase: () => (/* binding */ utilToBase)\n/* harmony export */ });\n/*!\n Copyright (c) Peculiar Ventures, LLC\n*/ function getUTCDate(date) {\n    return new Date(date.getTime() + date.getTimezoneOffset() * 60000);\n}\nfunction getParametersValue(parameters, name, defaultValue) {\n    var _a;\n    if (parameters instanceof Object === false) {\n        return defaultValue;\n    }\n    return (_a = parameters[name]) !== null && _a !== void 0 ? _a : defaultValue;\n}\nfunction bufferToHexCodes(inputBuffer, inputOffset = 0, inputLength = inputBuffer.byteLength - inputOffset, insertSpace = false) {\n    let result = \"\";\n    for (const item of new Uint8Array(inputBuffer, inputOffset, inputLength)){\n        const str = item.toString(16).toUpperCase();\n        if (str.length === 1) {\n            result += \"0\";\n        }\n        result += str;\n        if (insertSpace) {\n            result += \" \";\n        }\n    }\n    return result.trim();\n}\nfunction checkBufferParams(baseBlock, inputBuffer, inputOffset, inputLength) {\n    if (!(inputBuffer instanceof ArrayBuffer)) {\n        baseBlock.error = 'Wrong parameter: inputBuffer must be \"ArrayBuffer\"';\n        return false;\n    }\n    if (!inputBuffer.byteLength) {\n        baseBlock.error = \"Wrong parameter: inputBuffer has zero length\";\n        return false;\n    }\n    if (inputOffset < 0) {\n        baseBlock.error = \"Wrong parameter: inputOffset less than zero\";\n        return false;\n    }\n    if (inputLength < 0) {\n        baseBlock.error = \"Wrong parameter: inputLength less than zero\";\n        return false;\n    }\n    if (inputBuffer.byteLength - inputOffset - inputLength < 0) {\n        baseBlock.error = \"End of input reached before message was fully decoded (inconsistent offset and length values)\";\n        return false;\n    }\n    return true;\n}\nfunction utilFromBase(inputBuffer, inputBase) {\n    let result = 0;\n    if (inputBuffer.length === 1) {\n        return inputBuffer[0];\n    }\n    for(let i = inputBuffer.length - 1; i >= 0; i--){\n        result += inputBuffer[inputBuffer.length - 1 - i] * Math.pow(2, inputBase * i);\n    }\n    return result;\n}\nfunction utilToBase(value, base, reserved = -1) {\n    const internalReserved = reserved;\n    let internalValue = value;\n    let result = 0;\n    let biggest = Math.pow(2, base);\n    for(let i = 1; i < 8; i++){\n        if (value < biggest) {\n            let retBuf;\n            if (internalReserved < 0) {\n                retBuf = new ArrayBuffer(i);\n                result = i;\n            } else {\n                if (internalReserved < i) {\n                    return new ArrayBuffer(0);\n                }\n                retBuf = new ArrayBuffer(internalReserved);\n                result = internalReserved;\n            }\n            const retView = new Uint8Array(retBuf);\n            for(let j = i - 1; j >= 0; j--){\n                const basis = Math.pow(2, j * base);\n                retView[result - j - 1] = Math.floor(internalValue / basis);\n                internalValue -= retView[result - j - 1] * basis;\n            }\n            return retBuf;\n        }\n        biggest *= Math.pow(2, base);\n    }\n    return new ArrayBuffer(0);\n}\nfunction utilConcatBuf(...buffers) {\n    let outputLength = 0;\n    let prevLength = 0;\n    for (const buffer of buffers){\n        outputLength += buffer.byteLength;\n    }\n    const retBuf = new ArrayBuffer(outputLength);\n    const retView = new Uint8Array(retBuf);\n    for (const buffer of buffers){\n        retView.set(new Uint8Array(buffer), prevLength);\n        prevLength += buffer.byteLength;\n    }\n    return retBuf;\n}\nfunction utilConcatView(...views) {\n    let outputLength = 0;\n    let prevLength = 0;\n    for (const view of views){\n        outputLength += view.length;\n    }\n    const retBuf = new ArrayBuffer(outputLength);\n    const retView = new Uint8Array(retBuf);\n    for (const view of views){\n        retView.set(view, prevLength);\n        prevLength += view.length;\n    }\n    return retView;\n}\nfunction utilDecodeTC() {\n    const buf = new Uint8Array(this.valueHex);\n    if (this.valueHex.byteLength >= 2) {\n        const condition1 = buf[0] === 0xFF && buf[1] & 0x80;\n        const condition2 = buf[0] === 0x00 && (buf[1] & 0x80) === 0x00;\n        if (condition1 || condition2) {\n            this.warnings.push(\"Needlessly long format\");\n        }\n    }\n    const bigIntBuffer = new ArrayBuffer(this.valueHex.byteLength);\n    const bigIntView = new Uint8Array(bigIntBuffer);\n    for(let i = 0; i < this.valueHex.byteLength; i++){\n        bigIntView[i] = 0;\n    }\n    bigIntView[0] = buf[0] & 0x80;\n    const bigInt = utilFromBase(bigIntView, 8);\n    const smallIntBuffer = new ArrayBuffer(this.valueHex.byteLength);\n    const smallIntView = new Uint8Array(smallIntBuffer);\n    for(let j = 0; j < this.valueHex.byteLength; j++){\n        smallIntView[j] = buf[j];\n    }\n    smallIntView[0] &= 0x7F;\n    const smallInt = utilFromBase(smallIntView, 8);\n    return smallInt - bigInt;\n}\nfunction utilEncodeTC(value) {\n    const modValue = value < 0 ? value * -1 : value;\n    let bigInt = 128;\n    for(let i = 1; i < 8; i++){\n        if (modValue <= bigInt) {\n            if (value < 0) {\n                const smallInt = bigInt - modValue;\n                const retBuf = utilToBase(smallInt, 8, i);\n                const retView = new Uint8Array(retBuf);\n                retView[0] |= 0x80;\n                return retBuf;\n            }\n            let retBuf = utilToBase(modValue, 8, i);\n            let retView = new Uint8Array(retBuf);\n            if (retView[0] & 0x80) {\n                const tempBuf = retBuf.slice(0);\n                const tempView = new Uint8Array(tempBuf);\n                retBuf = new ArrayBuffer(retBuf.byteLength + 1);\n                retView = new Uint8Array(retBuf);\n                for(let k = 0; k < tempBuf.byteLength; k++){\n                    retView[k + 1] = tempView[k];\n                }\n                retView[0] = 0x00;\n            }\n            return retBuf;\n        }\n        bigInt *= Math.pow(2, 8);\n    }\n    return new ArrayBuffer(0);\n}\nfunction isEqualBuffer(inputBuffer1, inputBuffer2) {\n    if (inputBuffer1.byteLength !== inputBuffer2.byteLength) {\n        return false;\n    }\n    const view1 = new Uint8Array(inputBuffer1);\n    const view2 = new Uint8Array(inputBuffer2);\n    for(let i = 0; i < view1.length; i++){\n        if (view1[i] !== view2[i]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction padNumber(inputNumber, fullLength) {\n    const str = inputNumber.toString(10);\n    if (fullLength < str.length) {\n        return \"\";\n    }\n    const dif = fullLength - str.length;\n    const padding = new Array(dif);\n    for(let i = 0; i < dif; i++){\n        padding[i] = \"0\";\n    }\n    const paddingString = padding.join(\"\");\n    return paddingString.concat(str);\n}\nconst base64Template = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\nconst base64UrlTemplate = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=\";\nfunction toBase64(input, useUrlTemplate = false, skipPadding = false, skipLeadingZeros = false) {\n    let i = 0;\n    let flag1 = 0;\n    let flag2 = 0;\n    let output = \"\";\n    const template = useUrlTemplate ? base64UrlTemplate : base64Template;\n    if (skipLeadingZeros) {\n        let nonZeroPosition = 0;\n        for(let i = 0; i < input.length; i++){\n            if (input.charCodeAt(i) !== 0) {\n                nonZeroPosition = i;\n                break;\n            }\n        }\n        input = input.slice(nonZeroPosition);\n    }\n    while(i < input.length){\n        const chr1 = input.charCodeAt(i++);\n        if (i >= input.length) {\n            flag1 = 1;\n        }\n        const chr2 = input.charCodeAt(i++);\n        if (i >= input.length) {\n            flag2 = 1;\n        }\n        const chr3 = input.charCodeAt(i++);\n        const enc1 = chr1 >> 2;\n        const enc2 = (chr1 & 0x03) << 4 | chr2 >> 4;\n        let enc3 = (chr2 & 0x0F) << 2 | chr3 >> 6;\n        let enc4 = chr3 & 0x3F;\n        if (flag1 === 1) {\n            enc3 = enc4 = 64;\n        } else {\n            if (flag2 === 1) {\n                enc4 = 64;\n            }\n        }\n        if (skipPadding) {\n            if (enc3 === 64) {\n                output += `${template.charAt(enc1)}${template.charAt(enc2)}`;\n            } else {\n                if (enc4 === 64) {\n                    output += `${template.charAt(enc1)}${template.charAt(enc2)}${template.charAt(enc3)}`;\n                } else {\n                    output += `${template.charAt(enc1)}${template.charAt(enc2)}${template.charAt(enc3)}${template.charAt(enc4)}`;\n                }\n            }\n        } else {\n            output += `${template.charAt(enc1)}${template.charAt(enc2)}${template.charAt(enc3)}${template.charAt(enc4)}`;\n        }\n    }\n    return output;\n}\nfunction fromBase64(input, useUrlTemplate = false, cutTailZeros = false) {\n    const template = useUrlTemplate ? base64UrlTemplate : base64Template;\n    function indexOf(toSearch) {\n        for(let i = 0; i < 64; i++){\n            if (template.charAt(i) === toSearch) return i;\n        }\n        return 64;\n    }\n    function test(incoming) {\n        return incoming === 64 ? 0x00 : incoming;\n    }\n    let i = 0;\n    let output = \"\";\n    while(i < input.length){\n        const enc1 = indexOf(input.charAt(i++));\n        const enc2 = i >= input.length ? 0x00 : indexOf(input.charAt(i++));\n        const enc3 = i >= input.length ? 0x00 : indexOf(input.charAt(i++));\n        const enc4 = i >= input.length ? 0x00 : indexOf(input.charAt(i++));\n        const chr1 = test(enc1) << 2 | test(enc2) >> 4;\n        const chr2 = (test(enc2) & 0x0F) << 4 | test(enc3) >> 2;\n        const chr3 = (test(enc3) & 0x03) << 6 | test(enc4);\n        output += String.fromCharCode(chr1);\n        if (enc3 !== 64) {\n            output += String.fromCharCode(chr2);\n        }\n        if (enc4 !== 64) {\n            output += String.fromCharCode(chr3);\n        }\n    }\n    if (cutTailZeros) {\n        const outputLength = output.length;\n        let nonZeroStart = -1;\n        for(let i = outputLength - 1; i >= 0; i--){\n            if (output.charCodeAt(i) !== 0) {\n                nonZeroStart = i;\n                break;\n            }\n        }\n        if (nonZeroStart !== -1) {\n            output = output.slice(0, nonZeroStart + 1);\n        } else {\n            output = \"\";\n        }\n    }\n    return output;\n}\nfunction arrayBufferToString(buffer) {\n    let resultString = \"\";\n    const view = new Uint8Array(buffer);\n    for (const element of view){\n        resultString += String.fromCharCode(element);\n    }\n    return resultString;\n}\nfunction stringToArrayBuffer(str) {\n    const stringLength = str.length;\n    const resultBuffer = new ArrayBuffer(stringLength);\n    const resultView = new Uint8Array(resultBuffer);\n    for(let i = 0; i < stringLength; i++){\n        resultView[i] = str.charCodeAt(i);\n    }\n    return resultBuffer;\n}\nconst log2 = Math.log(2);\nfunction nearestPowerOf2(length) {\n    const base = Math.log(length) / log2;\n    const floor = Math.floor(base);\n    const round = Math.round(base);\n    return floor === round ? floor : round;\n}\nfunction clearProps(object, propsArray) {\n    for (const prop of propsArray){\n        delete object[prop];\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/pvutils/build/utils.es.js\n");

/***/ })

};
;