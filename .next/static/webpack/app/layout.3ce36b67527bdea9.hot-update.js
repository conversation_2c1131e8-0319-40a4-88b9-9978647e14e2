/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fsrc%2Fapp%2Fglobals.css&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fsrc%2Fapp%2Fglobals.css&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js */ \"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fnode_modules%2Fsonner%2Fdist%2Findex.mjs&modules=%2FUsers%2Ffrancescooddo%2FDownloads%2FCoding%2FIBC%2FGroup%20Creator%2Fsrc%2Fapp%2Fglobals.css&server=false!\n"));

/***/ })

});